﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WindowsFormsCargill.Constant
{
    // For PLC
    public enum Device_Type
    {
        GATEWAY = 0,
        DEVICE = 1
    }
    public enum Gateway_Type
    {
        UNKNOWN = 0,
        PLC = 1
    }

    public enum Dashboard
    {
        ENEGRY = 1,
        STEAM = 2,
        ENEGRY_REPORT = 3,
        STEAM_REPORT = 4
    }
    // For Device
    public enum Device_Application
    {
        GENERAL = 0,
        ENEGRY = 1,
        STEAM = 2
    }
    // For Device
    public enum Device_Model
    {
        GENERAL = 0,
        PM5310 = 1,
        VORTEX420mA = 2
    }
    public enum Device_ConnectionType
    {
        UNKNOWN = 0,
        RS485 = 1
    }


    // For Attribute
    public enum Attribute_Type
    {
        UNKNOWN = 0,
        GENERAL = 1
    }


    // For DashboardConfig
    public enum DashboardConfig_Type
    {
        UNKNOWN = 0,
        GENERAL = 1
    }


    // For ChartData
    public enum ChartData_Type
    {
        UNKNOWN = 0,
        GENERAL = 1
    }
}


