﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Diagnostics;
using System.Windows.Forms;
using System.Data.SqlClient;
using WindowsFormsCargill.DB;

namespace WindowsFormsCargill.DB
{
    public class WidgetSerial2Plot_InfoDb
    {
        public int ID { get; set; }
        public int dashboardId { get; set; }

        public int deviceId { get; set; }
        public string ParaName { get; set; }
        public int NumberValue { get; set; }
        public int refreshTime { get; set; }
        public string HistoryTime { get; set; }
    }
    public class WidgetSerial2ConfigDb
    {
        private string connectionString;
        private string TableString;

        public WidgetSerial2ConfigDb(string connection)
        {
            connectionString = connection;
            TableString = "WidgetSerial2ConfigTable";
            // CreateTable();
        }

        public void CreateTable()
        {
            try
            {
                using (SqlConnection sql_conn = new SqlConnection(connectionString))
                {
                    sql_conn.Open();
                    if (sql_conn.State == ConnectionState.Open)
                    {
                        string commandText = $"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='{TableString}' AND xtype='U') BEGIN CREATE TABLE {TableString} (ID INT PRIMARY KEY IDENTITY, dashboardId INT, deviceId INT, ParaName VARCHAR(MAX), NumberValue INT, refreshTime INT, HistoryTime VARCHAR(MAX)) END";

                        using (SqlCommand sql_cmd = new SqlCommand(commandText, sql_conn))
                        {
                            sql_cmd.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
        }

        public List<WidgetSerial2Plot_InfoDb> ReadByDashboardId(int dashboardId)
        {
            List<WidgetSerial2Plot_InfoDb> responseBuf = new List<WidgetSerial2Plot_InfoDb>();
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        string commandText = $"SELECT * FROM {TableString} WHERE dashboardId = @dashboardId";
                        using (SqlCommand commandBuf = new SqlCommand(commandText, sqlConnectionBuf))
                        {
                            commandBuf.Parameters.AddWithValue("@dashboardId", dashboardId);

                            using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                            {
                                while (readerBuf.Read())
                                {
                                    if (readerBuf.FieldCount == 7)
                                    {
                                        WidgetSerial2Plot_InfoDb deviceInfoBuf = new WidgetSerial2Plot_InfoDb
                                        {
                                            ID = readerBuf.GetInt32(0),
                                            dashboardId = readerBuf.GetInt32(1),
                                            deviceId = readerBuf.GetInt32(2),
                                            ParaName = readerBuf["ParaName"].ToString(),
                                            NumberValue = readerBuf.GetInt32(4),
                                            refreshTime = readerBuf.GetInt32(5),
                                            HistoryTime = readerBuf["HistoryTime"].ToString()
                                        };
                                        responseBuf.Add(deviceInfoBuf);
                                    }
                                }
                                readerBuf.Close();
                            }
                        }
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
            return responseBuf;
        }

        public WidgetSerial2Plot_InfoDb ReadById(int Id)
        {
            WidgetSerial2Plot_InfoDb responseBuf = new WidgetSerial2Plot_InfoDb();
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        string commandText = $"SELECT * FROM {TableString} WHERE ID = @ID";
                        using (SqlCommand commandBuf = new SqlCommand(commandText, sqlConnectionBuf))
                        {
                            commandBuf.Parameters.AddWithValue("@ID", Id);

                            using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                            {
                                while (readerBuf.Read())
                                {
                                    if (readerBuf.FieldCount == 7)
                                    {
                                        responseBuf.ID = readerBuf.GetInt32(0);
                                        responseBuf.dashboardId = readerBuf.GetInt32(1);
                                        responseBuf.deviceId = readerBuf.GetInt32(2);
                                        responseBuf.ParaName = readerBuf["ParaName"].ToString();
                                        responseBuf.NumberValue = readerBuf.GetInt32(4);
                                        responseBuf.refreshTime = readerBuf.GetInt32(5);
                                        responseBuf.HistoryTime = readerBuf["HistoryTime"].ToString();
                                    }
                                }
                                readerBuf.Close();
                            }
                        }
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
            return responseBuf;
        }

        public void Update(WidgetSerial2Plot_InfoDb serialPlot)
        {
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        string commandText = $"UPDATE {TableString} SET dashboardId = @dashboardId, deviceId = @deviceId, ParaName = @ParaName, NumberValue = @NumberValue, refreshTime = @refreshTime, HistoryTime = @HistoryTime WHERE ID = @ID";

                        using (SqlCommand command = new SqlCommand(commandText, sqlConnectionBuf))
                        {
                            command.Parameters.AddWithValue("@dashboardId", serialPlot.dashboardId);
                            command.Parameters.AddWithValue("@deviceId", serialPlot.deviceId);
                            command.Parameters.AddWithValue("@ParaName", serialPlot.ParaName);
                            command.Parameters.AddWithValue("@NumberValue", serialPlot.NumberValue);
                            command.Parameters.AddWithValue("@refreshTime", serialPlot.refreshTime);
                            command.Parameters.AddWithValue("@HistoryTime", serialPlot.HistoryTime);
                            command.Parameters.AddWithValue("@ID", serialPlot.ID);

                            command.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
        }
    }
}
