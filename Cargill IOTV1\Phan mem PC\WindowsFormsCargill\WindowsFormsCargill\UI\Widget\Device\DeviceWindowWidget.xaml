﻿<UserControl x:Class="WindowsFormsCargill.UI.Widget.Device.DeviceWindowWidget"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:WindowsFormsCargill.UI.Widget.Device"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="600">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary 
                Source="../../../DataGridRes.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="45"/>
            <RowDefinition Height="4*"/>
            <RowDefinition Height="45"/>
        </Grid.RowDefinitions>
        <StackPanel Background="AliceBlue" Grid.Row="0" Orientation="Horizontal" VerticalAlignment="Stretch">
            <TextBlock FontSize="20">Device:</TextBlock>
            <TextBlock  FontSize="20" Name="XamlDeviceName">Device name</TextBlock>
        </StackPanel>
        <DataGrid Grid.Row="1"  x:Name="XamlMeterPara" Background="White" Padding="10" AutoGenerateColumns="False"  FontSize="13"
                       IsReadOnly="True" VerticalAlignment="Stretch" Margin="5,0,5,0" VerticalScrollBarVisibility="Visible" >
            <DataGrid.Columns>
                <DataGridTextColumn Header="Name" Binding="{Binding Name}" CanUserReorder="False" CanUserResize="False"></DataGridTextColumn>
                <DataGridTextColumn Header="Value"  Binding="{Binding Value}" CanUserReorder="False" CanUserResize="False"></DataGridTextColumn>
                <DataGridTextColumn Header="Time" Binding="{Binding Time}" CanUserReorder="False" CanUserResize="False"></DataGridTextColumn>
            </DataGrid.Columns>
        </DataGrid>
        <Grid Grid.Row="2">
            <DockPanel Grid.Row="0" Grid.Column="0" Background="WhiteSmoke">
                <Label HorizontalAlignment ="Center" VerticalAlignment="Center">Xpos</Label>
                <TextBox Name="Xpos_Textbox" BorderThickness="0,0,0,1"   Width="100" Margin="10,5,5,5"/>
                <Label HorizontalAlignment ="Center" VerticalAlignment="Center">Ypos</Label>
                <TextBox Name="Ypos_Textbox" BorderThickness="0,0,0,1"   Width="80" Margin="5"/>
                <Button  HorizontalAlignment="Right" Click="SavePOS_BtOnClick" Margin="5" >
                    <Viewbox Width="45" Height="30">
                        <Canvas Width="24" Height="24">
                            <Path Data="M15,9H5V5H15M12,19A3,3 0 0,1 9,16A3,3 0 0,1 12,13A3,3 0 0,1 15,16A3,3 0 0,1 12,19M17,3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V7L17,3Z" 
                 Fill="White"/>
                        </Canvas>
                    </Viewbox>
                </Button>

            </DockPanel>

        </Grid>
    </Grid>
</UserControl>
