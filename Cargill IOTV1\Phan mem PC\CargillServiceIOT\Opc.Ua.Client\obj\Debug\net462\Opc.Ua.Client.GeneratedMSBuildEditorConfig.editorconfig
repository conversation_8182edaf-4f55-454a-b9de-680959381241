is_global = true
build_property.TargetFramework = net462
build_property.TargetPlatformMinVersion = 7.0
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = Opc.Ua.Client
build_property.ProjectDir = D:\Project\Cargill\Cargill\Dependencies\Opc.Ua.Client\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
