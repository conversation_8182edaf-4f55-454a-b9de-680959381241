{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"Opc.Ua.Configuration/1.0.0": {"dependencies": {"OPCFoundation.NetStandard.Opc.Ua.Core.Debug": "1.0.0", "Opc.Ua.Core": "*******", "Opc.Ua.Security.Certificates": "*******"}, "runtime": {"Opc.Ua.Configuration.dll": {}}}, "Microsoft.Extensions.Logging.Abstractions/6.0.3": {"runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.1122.52304"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "NModbus/3.0.81": {"runtime": {"lib/net6.0/NModbus.dll": {"assemblyVersion": "3.0.81.0", "fileVersion": "3.0.81.0"}}}, "System.Formats.Asn1/6.0.0": {}, "OPCFoundation.NetStandard.Opc.Ua.Core.Debug/1.0.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "6.0.3", "NModbus": "3.0.81", "Newtonsoft.Json": "13.0.3", "OPCFoundation.NetStandard.Opc.Ua.Security.Certificates.Debug": "1.0.0"}, "runtime": {"Opc.Ua.Core.dll": {}}}, "OPCFoundation.NetStandard.Opc.Ua.Security.Certificates.Debug/1.0.0": {"dependencies": {"System.Formats.Asn1": "6.0.0"}, "runtime": {"Opc.Ua.Security.Certificates.dll": {}}}, "Opc.Ua.Core/*******": {"runtime": {"Opc.Ua.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Opc.Ua.Security.Certificates/*******": {"runtime": {"Opc.Ua.Security.Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"Opc.Ua.Configuration/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Abstractions/6.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-SUpStcdjeBbdKjPKe53hVVLkFjylX0yIXY8K+xWa47+o1d+REDyOMZjHZa+chsQI1K9qZeiHWk9jos0TFU7vGg==", "path": "microsoft.extensions.logging.abstractions/6.0.3", "hashPath": "microsoft.extensions.logging.abstractions.6.0.3.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "NModbus/3.0.81": {"type": "package", "serviceable": true, "sha512": "sha512-EzKEp7CHD8ErBL36iMts+6IrZZ9FEqllaD7Y5XzhoRjlxt5yXRughQ1bxPs99QFYFkW5xfkANB0Qs1gAmYGP8Q==", "path": "nmodbus/3.0.81", "hashPath": "nmodbus.3.0.81.nupkg.sha512"}, "System.Formats.Asn1/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T6fD00dQ3NTbPDy31m4eQUwKW84s03z0N2C8HpOklyeaDgaJPa/TexP4/SkORMSOwc7WhKifnA6Ya33AkzmafA==", "path": "system.formats.asn1/6.0.0", "hashPath": "system.formats.asn1.6.0.0.nupkg.sha512"}, "OPCFoundation.NetStandard.Opc.Ua.Core.Debug/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "OPCFoundation.NetStandard.Opc.Ua.Security.Certificates.Debug/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Opc.Ua.Core/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Opc.Ua.Security.Certificates/*******": {"type": "reference", "serviceable": false, "sha512": ""}}}