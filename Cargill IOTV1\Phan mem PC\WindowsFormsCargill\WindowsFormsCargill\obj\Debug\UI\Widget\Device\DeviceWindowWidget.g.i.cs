﻿#pragma checksum "..\..\..\..\..\UI\Widget\Device\DeviceWindowWidget.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "8D835C75897B9F5213EBB4B541E667461CD6C222A0EFBFDC826C88881365F58F"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using WindowsFormsCargill.UI.Widget.Device;


namespace WindowsFormsCargill.UI.Widget.Device {
    
    
    /// <summary>
    /// DeviceWindowWidget
    /// </summary>
    public partial class DeviceWindowWidget : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 25 "..\..\..\..\..\UI\Widget\Device\DeviceWindowWidget.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock XamlDeviceName;
        
        #line default
        #line hidden
        
        
        #line 27 "..\..\..\..\..\UI\Widget\Device\DeviceWindowWidget.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid XamlMeterPara;
        
        #line default
        #line hidden
        
        
        #line 38 "..\..\..\..\..\UI\Widget\Device\DeviceWindowWidget.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox Xpos_Textbox;
        
        #line default
        #line hidden
        
        
        #line 40 "..\..\..\..\..\UI\Widget\Device\DeviceWindowWidget.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox Ypos_Textbox;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/WindowsFormsCargill;component/ui/widget/device/devicewindowwidget.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\UI\Widget\Device\DeviceWindowWidget.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.XamlDeviceName = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.XamlMeterPara = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 3:
            this.Xpos_Textbox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.Ypos_Textbox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            
            #line 41 "..\..\..\..\..\UI\Widget\Device\DeviceWindowWidget.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SavePOS_BtOnClick);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

