<UserControl x:Class="WindowsFormsCargill.UI.Widget.Serial.SerialConfig"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:WindowsFormsCargill.UI.Widget.Serial"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="1080">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary 
                Source="../../../ButtonRes.xaml"/>
                <ResourceDictionary 
                Source="../../../ComboxRes.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="50"/>
            <RowDefinition Height="45"/>
            <RowDefinition Height="1*"/>
        </Grid.RowDefinitions>
        <TextBlock Grid.Row="0" Background="AliceBlue" Name="XamlTextname"/>
        <Grid Grid.Row="1">
            <StackPanel Grid.Row="0" Orientation="Horizontal">
                <ComboBox Name="XamlDeviceName" Width="150" Margin="10,0,10,0" Height="30" SelectionChanged="DeviceSelectChanger"/>
                <ComboBox Name="XamlParaName" Width="150" Margin="10,0,10,0" FontSize="18" Height="30"/>
                <TextBox Name="XamlRefreshTime" Width="130" Margin="10,0,10,0" FontSize="18" Height="30" BorderThickness="0,0,0,1"/>
                <ComboBox Name="XamlHistoryTime" Width="150" Margin="10,0,10,0" FontSize="18" Height="30"/>
                <TextBox Name="XamlMinDashboardValue" Width="100" Margin="10,0,10,0" FontSize="18" Height="30" BorderThickness="0,0,0,1"/>
                <TextBox Name="XamlMaxDashboardValue" Width="100" Margin="10,0,10,0" FontSize="18" Height="30" BorderThickness="0,0,0,1"/>
                <Button Click="SavePara_btOnClick" Width="80" Margin="5">
                    <Viewbox Width="45" Height="30">
                        <Canvas Width="24" Height="24">
                            <Path Data="M15,9H5V5H15M12,19A3,3 0 0,1 9,16A3,3 0 0,1 12,13A3,3 0 0,1 15,16A3,3 0 0,1 12,19M17,3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V7L17,3Z" 
                            Fill="White"/>
                        </Canvas>
                    </Viewbox>
                </Button>
            </StackPanel>
        </Grid>

        <Grid Grid.Row="2"  Name="XamlSerialPlot"/>
    </Grid>
</UserControl>
