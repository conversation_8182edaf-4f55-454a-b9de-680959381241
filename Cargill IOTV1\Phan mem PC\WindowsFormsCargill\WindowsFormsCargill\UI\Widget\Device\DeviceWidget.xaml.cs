using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using static WindowsFormsCargill.UI.MetterPage.Sub_Metter;
using WindowsFormsCargill.DB;
using System.Collections.ObjectModel;
namespace WindowsFormsCargill.UI.Widget.Device
{
    /// <summary>
    /// Interaction logic for DeviceWidget.xaml
    /// </summary>
    public partial class DeviceWidget : UserControl
    {
        //public static DeviceWindowWidget dialog = new DeviceWindowWidget();
        public  DeviceWindowWidgetForm Form = new DeviceWindowWidgetForm();
        public class DataModel
        {
            public int DeviceId;
            public string DeviceName;
        }
        DataModel dataModel = new DataModel();
        public DeviceWidget()
        {
            InitializeComponent();

            // Display the dialog box and read the response

        }
        public void SetId(int deviceId, string Name)
        {
            dataModel.DeviceId = deviceId;
            dataModel.DeviceName = Name;
        }
        public void SetHeader(string header)
        {
            XamlHeader.Text = header;
        }
        public void SetValue(int deviceId, string paraName, float MaxValue)
        {
            //string getValue;
            //getValue = GetValue(deviceId, paraName);
            //if (MaxValue == 0) XamlValue.Text = getValue;
            //else XamlValue.Text = getValue + "(" + Math.Round(float.Parse(getValue)/MaxValue,0) + "%)";
            string getValue;
            getValue = GetValue(deviceId, paraName);

            // Định dạng số với dấu chấm ngăn cách 3 chữ số
            if (double.TryParse(getValue, out double valueNumber))
            {
                // "N0" sẽ cho ra 1,234,567, thay dấu phẩy thành dấu chấm
                string formattedValue = valueNumber.ToString("N0");//.Replace(",", ".");
                if (MaxValue == 0)
                    XamlValue.Text = formattedValue;
                else
                    XamlValue.Text = formattedValue + " (" + Math.Round(valueNumber / MaxValue * 100, 0) + "%)";
            }
            else
            {
                // Nếu không phải số, hiển thị như cũ
                XamlValue.Text = getValue;
            }
        }

        private string GetValue(int deviceId, string para)
        {
            double value = 0;
            List<TelemetryData> listTelemetry = new List<TelemetryData>();
            for (int k = 0; k < MainWindow.systemState.listDevState.Count; k++)
            {
                if (MainWindow.systemState.listDevState[k].deviceId == deviceId)
                {
                    // Get value flow para name
                    if (MainWindow.systemState.listDevState[k].listTelemetry != null)
                    {
                        for (int i = 0; i < MainWindow.systemState.listDevState[k].listTelemetry.Count; i++)
                        {
                            if (para == MainWindow.systemState.listDevState[k].listTelemetry[i].Name) value = MainWindow.systemState.listDevState[k].listTelemetry[i].Value;
                        }
                    }
                }
            }
            return value.ToString();
        }

        public void SetColor(SolidColorBrush color)
        {
            XamlHeader.Foreground = new SolidColorBrush(Colors.Gray);
            XamlValue.Foreground = new SolidColorBrush(Colors.Black);
            XamlIcon.Fill = color;

            // Get
            //Icon icon = SystemIcons.Information;

            //// Change icon to ImageSource
            //BitmapSource bitmapSource = Imaging.CreateBitmapSourceFromHIcon(
            //    icon.Handle,
            //    Int32Rect.Empty,
            //    BitmapSizeOptions.FromEmptyOptions());

            //XamlIcon.Source = bitmapSource;
        }

        private void DeviceClick_BtHandle(object sender, System.Windows.RoutedEventArgs e)
        {
            Form.dialog.SetId(dataModel.DeviceId, dataModel.DeviceName);
            Form.TopMost = true;
            Form.Show();
            //DeviceWindowWidget dialog1 = new DeviceWindowWidget();
            //dialog1.SetId(dataModel.DeviceId, dataModel.DeviceName);
            //dialog1
            ////Display the dialog box and read the response
            //if (dialog.IsActive == false)
            //{

            //    bool? result = dialog.ShowDialog();

            //    if (result == true)
            //    {
            //        ///User accepted the dialog box
            //        Console.WriteLine("Your request will be processed.");
            //    }
            //    else
            //    {
            //        //User cancelled the dialog box
            //        Console.WriteLine("Sorry it didn't work out, we'll try again later.");
            //    }
            //}


        }
    }
}
