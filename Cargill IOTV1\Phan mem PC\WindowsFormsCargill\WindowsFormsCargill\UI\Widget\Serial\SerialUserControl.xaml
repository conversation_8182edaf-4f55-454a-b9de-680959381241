<UserControl x:Class="WindowsFormsCargill.UI.Widget.Serial.SerialUserControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:WindowsFormsCargill.UI.Widget.Serial"
             xmlns:wf="clr-namespace:System.Windows.Forms.DataVisualization.Charting;assembly=System.Windows.Forms.DataVisualization"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="1500">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="15"/>
            <RowDefinition Height="1*"/>
        </Grid.RowDefinitions>
        <WindowsFormsHost Grid.Row="1">
            <wf:Chart x:Name="XamlPlot">
            </wf:Chart>
        </WindowsFormsHost>

        <StackPanel  Grid.Row="0" Orientation="Horizontal" VerticalAlignment="Center" HorizontalAlignment="Center">
            <TextBlock Name="XamlHeader" Text="Para" Background="Transparent" FontSize="12" TextAlignment="Center" Foreground="Black" Margin="10,0,20,0"/>
        </StackPanel>
    </Grid>
</UserControl>
