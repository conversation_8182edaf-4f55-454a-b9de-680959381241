﻿<UserControl x:Class="WindowsFormsCargill.UI.DashboardEnergy.SubPageEnegryDashboard"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:WindowsFormsCargill.UI.DashboardEnergy"
             xmlns:wf="clr-namespace:System.Windows.Forms;assembly=System.Windows.Forms"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary 
                Source="../../ButtonRes.xaml"/>
                <ResourceDictionary 
                Source="../../DataGridRes.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>

    </UserControl.Resources>

    <Grid Background="GhostWhite" x:Name="XamlGrid" SizeChanged="MainGrid_SizeChanged">
        <Grid.RowDefinitions>
            <RowDefinition Height="2*"></RowDefinition>
            <RowDefinition Height="3"></RowDefinition>
            <RowDefinition Height="1*"></RowDefinition>
        </Grid.RowDefinitions>
        <GridSplitter Grid.Row="1" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Background="#FF31577E"/>
        <Grid Grid.Row="2" Margin="5" Background="White" >
            <Grid.RowDefinitions>
                <!--<RowDefinition Height="25"/>-->
                <RowDefinition Height="1*"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="1*"/>
                <ColumnDefinition Width="3"/>
                <ColumnDefinition Width="1*"/>
            </Grid.ColumnDefinitions>
            <GridSplitter Grid.Column="1" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Background="#FF31577E"/>
            <!--<Label Grid.Row="0" FontSize="18" FontWeight="Bold" Grid.ColumnSpan="2" Background="GhostWhite"
   HorizontalContentAlignment="Center"  Padding="0,0,0,0">Enegry Mesurement</Label>-->
            <Grid Grid.Row="0" Grid.Column="0">
                <WindowsFormsHost>
                    <wf:TableLayoutPanel x:Name="XamlPlot_StackPanelSerialPlot" ColumnCount="1" Anchor="Top,Bottom,Left,Right" AutoScroll="True" AutoSize="True" Dock="Fill"/>
                </WindowsFormsHost>
            </Grid>

            <Grid Grid.Row="1" Grid.Column="2">
                <WindowsFormsHost>
                    <wf:TableLayoutPanel x:Name="XamlPlot_StackPanelSerialPlot2" ColumnCount="1" Anchor="Top,Bottom,Left,Right" AutoScroll="True" AutoSize="True" Dock="Fill"/>
                </WindowsFormsHost>
            </Grid>
            <!--<ScrollViewer Grid.Row="1" Grid.Column="0" VerticalScrollBarVisibility="Visible" HorizontalScrollBarVisibility="Disabled" Margin="10,0,10,0">
                <Grid>
                    <StackPanel Name="XamlPlot_StackPanelSerialPlot" Orientation="Vertical">
                    </StackPanel>
                </Grid>
            </ScrollViewer>-->
            <!--<ScrollViewer Grid.Row="1" Grid.Column="1" VerticalScrollBarVisibility="Visible" HorizontalScrollBarVisibility="Disabled" Margin="10,0,10,0">
                <Grid>
                    <StackPanel Name="XamlPlot_StackPanelSerialPlot2" Orientation="Vertical"/>
                </Grid>
            </ScrollViewer>-->

        </Grid>
        <Grid Grid.Row="0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="1000*"></ColumnDefinition>
                <!--<ColumnDefinition Width="3"></ColumnDefinition>
                <ColumnDefinition Width="1*" x:Name="XamlDeviceProperty"></ColumnDefinition>-->
            </Grid.ColumnDefinitions>
            <!--<GridSplitter Grid.Column="1" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Background="#FF31577E"/>-->
            <Grid  Background="White" Grid.Row="0" Grid.Column="0" Margin="5" SizeChanged="SizeImageChangedEventHandel">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*"></ColumnDefinition>
                        <!--<ColumnDefinition Width="0.8*"></ColumnDefinition>-->
                    </Grid.ColumnDefinitions>

                    <Grid x:Name="XamlPositionPlot" Grid.Column="0" Grid.ColumnSpan="2">
                        <Image Name="XamlImageMape" HorizontalAlignment="Left" VerticalAlignment="Top" Stretch="Fill">
                            <Image.Style>
                                <Style TargetType="Image">
                                    <Style.Triggers>
                                        <Trigger Property="IsEnabled" Value="True">
                                            <Setter Property="Opacity" Value="0.5" />
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Image.Style>
                        </Image>
                    </Grid>
                    <Grid Grid.Column="0" Name="XamlBarValue"/>
                    <!--<Grid Grid.Column="1" Name="XamlPiePlot"/>-->
                </Grid>

            </Grid>

            <!--<Grid Grid.Row="0" Grid.Column="2" Background="White" Margin="5" >
                <Grid.RowDefinitions>
                    <RowDefinition Height="42"/>
                    <RowDefinition Height="1*"/>
                </Grid.RowDefinitions>
                <DockPanel Grid.Row="0" >
                    <Label  Name="XamlRecState" HorizontalAlignment="Stretch" VerticalAlignment="Top" FontSize="18" FontWeight="Bold" 
                   HorizontalContentAlignment="Left" VerticalContentAlignment="Center">Device Property</Label>
                    <Button Click="OnHandelUpdateProperty" Margin="5" HorizontalAlignment="Right">
                        <Viewbox Width="45" Height="25">
                            <Canvas Width="24" Height="24">
                                <Path Data="M15,9H5V5H15M12,19A3,3 0 0,1 9,16A3,3 0 0,1 12,13A3,3 0 0,1 15,16A3,3 0 0,1 12,19M17,3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V7L17,3Z" 
                           Fill="White"/>
                            </Canvas>
                        </Viewbox>
                    </Button>
                </DockPanel>

                <DataGrid Grid.Row="1" x:Name="XamlListDevEDashboard_DataGrid" Background="White" 
             BeginningEdit="BeginningEdit_EventHandle" CellEditEnding="CellEditEnding_EventHandle" PreparingCellForEdit="PreparingCellForEdit_EventHandle" RowEditEnding="RowEditEnding_EventHandle"
             Margin="0,0,0,0" AutoGenerateColumns="False" CanUserAddRows="False" CanUserDeleteRows="False" >
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Name" Binding="{Binding deviceName}" IsReadOnly="True" CanUserResize="False"/>
                        <DataGridTextColumn Header="Par"  Binding="{Binding parName}" CanUserResize="False" IsReadOnly="True"/>
                        <DataGridTextColumn Header="Value" Binding="{Binding parValue}" IsReadOnly="True" CanUserResize="False"/>
                        --><!--<DataGridTextColumn Header="Time" Binding="{Binding Time}" IsReadOnly="True" CanUserResize="False"/>--><!--
                        <DataGridTextColumn Header="Time" IsReadOnly="True" CanUserResize="False">
                            <DataGridTextColumn.Binding>
                                <Binding Path="Time" StringFormat="{}{0:dd/MM/yy HH:mm}"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="Xpos"  Binding="{Binding Xpos}" CanUserResize="False"/>
                        <DataGridTextColumn Header="Ypos"  Binding="{Binding Ypos}" CanUserResize="False"/>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>-->
        </Grid>
    </Grid>
</UserControl>
