﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using WindowsFormsCargill.UI.MetterPage;

namespace WindowsFormsCargill.UI.HardwareConfig
{
    /// <summary>
    /// Interaction logic for UI_HwConfigMainLayout.xaml
    /// </summary>
    public partial class UI_HwConfigMainLayout : UserControl
    {
        public UI_HwConfigGateway uiHwConfigGateway = new UI_HwConfigGateway();
        //public UI_HwConfigDevice uiHwConfigDevices = new UI_HwConfigDevice();
        //public UI_HwConfigOpcConnection uiHwConfigOpcConn = new UI_HwConfigOpcConnection();
        public UI_MetterPage uiMetterpage = new UI_MetterPage();
        public UI_HwConfigMainLayout()
        {
            InitializeComponent();

            {// Set frame
                XamllistGateways_Frame.Content = uiHwConfigGateway;
                //XamllistDevices_Frame.Content = uiHwConfigDevices;
                //XamlOpcConfig_Frame.Content = uiHwConfigOpcConn;
                XamlDevices_Frame.Content = uiMetterpage;
            }
        }
    }
}
