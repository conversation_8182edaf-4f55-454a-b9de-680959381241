<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Opc.Ua.Client</name>
    </assembly>
    <members>
        <member name="T:Opc.Ua.Client.Browser">
            <summary>
            Stores the options to use for a browse operation.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Browser.#ctor">
            <summary>
            Creates an unattached instance of a browser.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Browser.#ctor(Opc.Ua.Client.ISession)">
            <summary>
            Creates new instance of a browser and attaches it to a session.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Browser.#ctor(Opc.Ua.Client.Browser)">
            <summary>
            Creates a copy of a browser.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Browser.Initialize">
            <summary>
            Sets all private fields to default values.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Browser.Session">
            <summary>
            The session that the browse is attached to.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Browser.View">
            <summary>
            The view to use for the browse operation.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Browser.MaxReferencesReturned">
            <summary>
            The maximum number of refrences to return in a single browse operation.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Browser.BrowseDirection">
            <summary>
            The direction to browse.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Browser.ReferenceTypeId">
            <summary>
            The reference type to follow.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Browser.IncludeSubtypes">
            <summary>
            Whether subtypes of the reference type should be included.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Browser.NodeClassMask">
            <summary>
            The classes of the target nodes.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Browser.ResultMask">
            <summary>
            The results to return.
            </summary>
        </member>
        <member name="E:Opc.Ua.Client.Browser.MoreReferences">
            <summary>
            Raised when a browse operation halted because of a continuation point.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Browser.ContinueUntilDone">
            <summary>
            Whether subsequent continuation points should be processed automatically.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Browser.Browse(Opc.Ua.NodeId)">
            <summary>
            Browses the specified node.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Browser.CheckBrowserState">
            <summary>
            Checks the state of the browser.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Browser.BrowseNext(System.Byte[]@,System.Boolean)">
            <summary>
            Fetches the next batch of references.
            </summary>
            <param name="continuationPoint">The continuation point.</param>
            <param name="cancel">if set to <c>true</c> the browse operation is cancelled.</param>
            <returns>The next batch of references</returns>
        </member>
        <member name="T:Opc.Ua.Client.BrowserEventArgs">
            <summary>
            The event arguments provided a browse operation returns a continuation point.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.BrowserEventArgs.#ctor(Opc.Ua.ReferenceDescriptionCollection)">
            <summary>
            Creates a new instance.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.BrowserEventArgs.Cancel">
            <summary>
            Whether the browse operation should be cancelled.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.BrowserEventArgs.ContinueUntilDone">
            <summary>
            Whether subsequent continuation points should be processed automatically.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.BrowserEventArgs.References">
            <summary>
            The references that have been fetched so far.
            </summary>
        </member>
        <member name="T:Opc.Ua.Client.BrowserEventHandler">
            <summary>
            A delegate used to received browser events.
            </summary>
        </member>
        <member name="T:Opc.Ua.Client.CoreClientUtils">
            <summary>
            Defines numerous re-useable utility functions for clients.
            </summary>
            <summary>
            EventSource for client.
            </summary>
        </member>
        <member name="F:Opc.Ua.Client.CoreClientUtils.DefaultDiscoverTimeout">
            <summary>
            The default discover operation timeout.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.CoreClientUtils.DiscoverServers(Opc.Ua.ApplicationConfiguration)">
            <summary>
            Discovers the servers on the local machine.
            </summary>
            <param name="configuration">The configuration.</param>
            <returns>A list of server urls.</returns>
        </member>
        <member name="M:Opc.Ua.Client.CoreClientUtils.DiscoverServers(Opc.Ua.ApplicationConfiguration,System.Int32)">
            <summary>
            Discovers the servers on the local machine.
            </summary>
            <param name="configuration">The configuration.</param>
            <param name="discoverTimeout">Operation timeout in milliseconds.</param>
            <returns>A list of server urls.</returns>
        </member>
        <member name="M:Opc.Ua.Client.CoreClientUtils.SelectEndpoint(System.String,System.Boolean)">
            <summary>
            Finds the endpoint that best matches the current settings.
            </summary>
            <param name="discoveryUrl">The discovery URL.</param>
            <param name="useSecurity">if set to <c>true</c> select an endpoint that uses security.</param>
            <returns>The best available endpoint.</returns>
        </member>
        <member name="M:Opc.Ua.Client.CoreClientUtils.SelectEndpoint(System.String,System.Boolean,System.Int32)">
            <summary>
            Finds the endpoint that best matches the current settings.
            </summary>
            <param name="discoveryUrl">The discovery URL.</param>
            <param name="useSecurity">if set to <c>true</c> select an endpoint that uses security.</param>
            <param name="discoverTimeout">Operation timeout in milliseconds.</param>
            <returns>The best available endpoint.</returns>
        </member>
        <member name="M:Opc.Ua.Client.CoreClientUtils.SelectEndpoint(Opc.Ua.ApplicationConfiguration,Opc.Ua.ITransportWaitingConnection,System.Boolean)">
            <summary>
            Finds the endpoint that best matches the current settings.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.CoreClientUtils.SelectEndpoint(Opc.Ua.ApplicationConfiguration,Opc.Ua.ITransportWaitingConnection,System.Boolean,System.Int32)">
            <summary>
            Finds the endpoint that best matches the current settings.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.CoreClientUtils.SelectEndpoint(Opc.Ua.ApplicationConfiguration,System.String,System.Boolean)">
            <summary>
            Finds the endpoint that best matches the current settings.
            </summary>
            <param name="application">The application configuration.</param>
            <param name="discoveryUrl">The discovery URL.</param>
            <param name="useSecurity">if set to <c>true</c> select an endpoint that uses security.</param>
            <returns>The best available endpoint.</returns>
        </member>
        <member name="M:Opc.Ua.Client.CoreClientUtils.SelectEndpoint(Opc.Ua.ApplicationConfiguration,System.String,System.Boolean,System.Int32)">
            <summary>
            Finds the endpoint that best matches the current settings.
            </summary>
            <param name="application">The application configuration.</param>
            <param name="discoveryUrl">The discovery URL.</param>
            <param name="useSecurity">if set to <c>true</c> select an endpoint that uses security.</param>
            <param name="discoverTimeout">The timeout for the discover operation.</param>
            <returns>The best available endpoint.</returns>
        </member>
        <member name="M:Opc.Ua.Client.CoreClientUtils.SelectEndpoint(System.Uri,Opc.Ua.EndpointDescriptionCollection,System.Boolean)">
            <summary>
            Select the best supported endpoint from an
            EndpointDescriptionCollection, with or without security.
            </summary>
            <param name="url"></param>
            <param name="endpoints"></param>
            <param name="useSecurity"></param>
        </member>
        <member name="M:Opc.Ua.Client.CoreClientUtils.GetDiscoveryUrl(System.String)">
            <summary>
            Convert the discoveryUrl to a Uri and modify endpoint as per connection scheme if required.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.CoreClientUtils.EventLog">
            <summary>
            The EventSource log interface.
            </summary>
        </member>
        <member name="T:Opc.Ua.Client.DataDictionary">
            <summary>
            A class that holds the configuration for a UA service.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.DataDictionary.#ctor(Opc.Ua.Client.ISession)">
            <summary>
            The default constructor.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.DataDictionary.Initialize">
            <summary>
            Sets private members to default values.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.DataDictionary.DictionaryId">
            <summary>
            The node id for the dictionary.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.DataDictionary.Name">
            <summary>
            The display name for the dictionary.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.DataDictionary.TypeSystemId">
            <summary>
            The node id for the type system.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.DataDictionary.TypeSystemName">
            <summary>
            The display name for the type system.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.DataDictionary.TypeDictionary">
            <summary>
            The type dictionary.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.DataDictionary.DataTypes">
            <summary>
            The data type dictionary DataTypes
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.DataDictionary.Load(Opc.Ua.INode)">
            <summary>
            Loads the dictionary identified by the node id.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.DataDictionary.Load(Opc.Ua.NodeId,System.String,System.Byte[],System.Collections.Generic.IDictionary{System.String,System.Byte[]})">
            <summary>
            Loads the dictionary identified by the node id.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.DataDictionary.Contains(Opc.Ua.NodeId)">
            <summary>
            Returns true if the dictionary contains the data type description;
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.DataDictionary.GetSchema(Opc.Ua.NodeId)">
            <summary>
            Returns the schema for the specified type (returns the entire dictionary if null).
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.DataDictionary.GetTypeSystem(Opc.Ua.NodeId)">
            <summary>
            Retrieves the type system for the dictionary.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.DataDictionary.ReadDataTypes(Opc.Ua.NodeId)">
            <summary>
            Retrieves the data types in the dictionary.
            </summary>
            <remarks>
            In order to allow for fast Linq matching of dictionary
            QNames with the data type nodes, the BrowseName of
            the DataType node is replaced with Value string.
            </remarks>
        </member>
        <member name="M:Opc.Ua.Client.DataDictionary.ReadDictionaries(Opc.Ua.ISessionClientMethods,System.Collections.Generic.IList{Opc.Ua.NodeId})">
            <summary>
            Reads the contents of multiple data dictionaries.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.DataDictionary.ReadDictionary(Opc.Ua.NodeId)">
            <summary>
            Reads the contents of a data dictionary.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.DataDictionary.Validate(System.Byte[],System.Boolean)">
            <summary>
            Validates the type dictionary.
            </summary>
            <param name="dictionary">The encoded dictionary to validate.</param>
            <param name="throwOnError">Throw if an error occurred.</param>
        </member>
        <member name="M:Opc.Ua.Client.DataDictionary.Validate(System.Byte[],System.Collections.Generic.IDictionary{System.String,System.Byte[]},System.Boolean)">
            <summary>
            Validates the type dictionary.
            </summary>
            <param name="dictionary">The encoded dictionary to validate.</param>
            <param name="imports">A table of imported namespace schemas.</param>
            <param name="throwOnError">Throw if an error occurred.</param>
        </member>
        <member name="T:Opc.Ua.Client.DefaultSessionFactory">
            <summary>
            Object that creates instances of an Opc.Ua.Client.Session object.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.DefaultSessionFactory.CreateAsync(Opc.Ua.ApplicationConfiguration,Opc.Ua.ConfiguredEndpoint,System.Boolean,System.String,System.UInt32,Opc.Ua.IUserIdentity,System.Collections.Generic.IList{System.String})">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.DefaultSessionFactory.CreateAsync(Opc.Ua.ApplicationConfiguration,Opc.Ua.ConfiguredEndpoint,System.Boolean,System.Boolean,System.String,System.UInt32,Opc.Ua.IUserIdentity,System.Collections.Generic.IList{System.String})">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.DefaultSessionFactory.CreateAsync(Opc.Ua.ApplicationConfiguration,Opc.Ua.ITransportWaitingConnection,Opc.Ua.ConfiguredEndpoint,System.Boolean,System.Boolean,System.String,System.UInt32,Opc.Ua.IUserIdentity,System.Collections.Generic.IList{System.String})">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.DefaultSessionFactory.CreateAsync(Opc.Ua.ApplicationConfiguration,Opc.Ua.Client.ReverseConnectManager,Opc.Ua.ConfiguredEndpoint,System.Boolean,System.Boolean,System.String,System.UInt32,Opc.Ua.IUserIdentity,System.Collections.Generic.IList{System.String},System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.DefaultSessionFactory.RecreateAsync(Opc.Ua.Client.ISession)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.DefaultSessionFactory.RecreateAsync(Opc.Ua.Client.ISession,Opc.Ua.ITransportWaitingConnection)">
            <inheritdoc/>
        </member>
        <member name="T:Opc.Ua.Client.NamespaceDoc">
             <summary>
             The <b>Opc.Ua.Client</b> namespace defines classes which can be used to implement a UA client.
             These classes manage client side state information, provide higher level abstractions for UA 
             tasks such as managing sessions/subscriptions and saving/restoring connection information for 
             later use.
             </summary>
            <exclude/>
        </member>
        <member name="T:Opc.Ua.Client.INodeCache">
            <summary>
            A client side cache of the server's type model.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.INodeCache.LoadUaDefinedTypes(Opc.Ua.ISystemContext)">
            <summary>
            Loads the UA defined types into the cache.
            </summary>
            <param name="context">The context.</param>
        </member>
        <member name="M:Opc.Ua.Client.INodeCache.Clear">
            <summary>
            Removes all nodes from the cache.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.INodeCache.FetchNode(Opc.Ua.ExpandedNodeId)">
            <summary>
            Fetches a node from the server and updates the cache.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.INodeCache.Find(System.Collections.Generic.IList{Opc.Ua.ExpandedNodeId})">
            <summary>
            Finds a set of nodes in the nodeset,
            fetches missing nodes from server.
            </summary>
            <param name="nodeIds">The node identifier collection.</param>
        </member>
        <member name="M:Opc.Ua.Client.INodeCache.FetchNodes(System.Collections.Generic.IList{Opc.Ua.ExpandedNodeId})">
            <summary>
            Fetches a node collection from the server and updates the cache.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.INodeCache.FetchSuperTypes(Opc.Ua.ExpandedNodeId)">
            <summary>
            Adds the supertypes of the node to the cache.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.INodeCache.FindReferences(Opc.Ua.ExpandedNodeId,Opc.Ua.NodeId,System.Boolean,System.Boolean)">
            <summary>
            Returns the references of the specified node that meet the criteria specified.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.INodeCache.FindReferences(System.Collections.Generic.IList{Opc.Ua.ExpandedNodeId},System.Collections.Generic.IList{Opc.Ua.NodeId},System.Boolean,System.Boolean)">
            <summary>
            Returns the references of the specified nodes that meet the criteria specified.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.INodeCache.GetDisplayText(Opc.Ua.INode)">
            <summary>
            Returns a display name for a node.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.INodeCache.GetDisplayText(Opc.Ua.ExpandedNodeId)">
            <summary>
            Returns a display name for a node.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.INodeCache.GetDisplayText(Opc.Ua.ReferenceDescription)">
            <summary>
            Returns a display name for the target of a reference.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.INodeCache.BuildBrowsePath(Opc.Ua.ILocalNode,System.Collections.Generic.IList{Opc.Ua.QualifiedName})">
            <summary>
            Builds the relative path from a type to a node.
            </summary>
        </member>
        <member name="T:Opc.Ua.Client.RenewUserIdentityEventHandler">
            <summary>
            Used to handle renews of user identity tokens before reconnect.
            </summary>
        </member>
        <member name="T:Opc.Ua.Client.KeepAliveEventHandler">
            <summary>
            The delegate used to receive keep alive notifications.
            </summary>
        </member>
        <member name="T:Opc.Ua.Client.NotificationEventHandler">
            <summary>
            The delegate used to receive publish notifications.
            </summary>
        </member>
        <member name="T:Opc.Ua.Client.PublishErrorEventHandler">
            <summary>
            The delegate used to receive pubish error notifications.
            </summary>
        </member>
        <member name="T:Opc.Ua.Client.ISession">
            <summary>
            Manages a session with a server.
            </summary>
        </member>
        <member name="E:Opc.Ua.Client.ISession.KeepAlive">
            <summary>
            Raised when a keep alive arrives from the server or an error is detected.
            </summary>
            <remarks>
            Once a session is created a timer will periodically read the server state and current time.
            If this read operation succeeds this event will be raised each time the keep alive period elapses.
            If an error is detected (KeepAliveStopped == true) then this event will be raised as well.
            </remarks>
        </member>
        <member name="E:Opc.Ua.Client.ISession.Notification">
            <summary>
            Raised when a notification message arrives in a publish response.
            </summary>
            <remarks>
            All publish requests are managed by the Session object. When a response arrives it is
            validated and passed to the appropriate Subscription object and this event is raised.
            </remarks>
        </member>
        <member name="E:Opc.Ua.Client.ISession.PublishError">
            <summary>
            Raised when an exception occurs while processing a publish response.
            </summary>
            <remarks>
            Exceptions in a publish response are not necessarily fatal and the Session will
            attempt to recover by issuing Republish requests if missing messages are detected.
            That said, timeout errors may be a symptom of a OperationTimeout that is too short
            when compared to the shortest PublishingInterval/KeepAliveCount amount the current
            Subscriptions. The OperationTimeout should be twice the minimum value for
            PublishingInterval*KeepAliveCount.
            </remarks>
        </member>
        <member name="E:Opc.Ua.Client.ISession.SubscriptionsChanged">
            <summary>
            Raised when a subscription is added or removed
            </summary>
        </member>
        <member name="E:Opc.Ua.Client.ISession.SessionClosing">
            <summary>
            Raised to indicate the session is closing.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.ISession.SessionFactory">
            <summary>
            The factory which was used to create the session.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.ISession.ConfiguredEndpoint">
            <summary>
            Gets the endpoint used to connect to the server.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.ISession.SessionName">
            <summary>
            Gets the name assigned to the session.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.ISession.SessionTimeout">
            <summary>
            Gets the period for wich the server will maintain the session if there is no communication from the client.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.ISession.Handle">
            <summary>
            Gets the local handle assigned to the session.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.ISession.Identity">
            <summary>
            Gets the user identity currently used for the session.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.ISession.IdentityHistory">
            <summary>
            Gets a list of user identities that can be used to connect to the server.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.ISession.NamespaceUris">
            <summary>
            Gets the table of namespace uris known to the server.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.ISession.ServerUris">
            <summary>
            Gest the table of remote server uris known to the server.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.ISession.SystemContext">
            <summary>
            Gets the system context for use with the session.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.ISession.Factory">
            <summary>
            Gets the factory used to create encodeable objects that the server understands.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.ISession.TypeTree">
            <summary>
            Gets the cache of the server's type tree.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.ISession.NodeCache">
            <summary>
            Gets the cache of nodes fetched from the server.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.ISession.FilterContext">
            <summary>
            Gets the context to use for filter operations.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.ISession.PreferredLocales">
            <summary>
            Gets the locales that the server should use when returning localized text.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.ISession.DataTypeSystem">
            <summary>
            Gets the data type system dictionaries in use.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.ISession.Subscriptions">
            <summary>
            Gets the subscriptions owned by the session.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.ISession.SubscriptionCount">
            <summary>
            Gets the number of subscriptions owned by the session.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.ISession.DeleteSubscriptionsOnClose">
            <summary>
            If the subscriptions are deleted when a session is closed. 
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.ISession.DefaultSubscription">
            <summary>
            Gets or Sets the default subscription for the session.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.ISession.KeepAliveInterval">
            <summary>
            Gets or Sets how frequently the server is pinged to see if communication is still working.
            </summary>
            <remarks>
            This interval controls how much time elaspes before a communication error is detected.
            If everything is ok the KeepAlive event will be raised each time this period elapses.
            </remarks>
        </member>
        <member name="P:Opc.Ua.Client.ISession.KeepAliveStopped">
            <summary>
            Returns true if the session is not receiving keep alives.
            </summary>
            <remarks>
            Set to true if the server does not respond for 2 times the KeepAliveInterval.
            Set to false is communication recovers.
            </remarks>
        </member>
        <member name="P:Opc.Ua.Client.ISession.LastKeepAliveTime">
            <summary>
            Gets the time of the last keep alive.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.ISession.OutstandingRequestCount">
            <summary>
            Gets the number of outstanding publish or keep alive requests.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.ISession.DefunctRequestCount">
            <summary>
            Gets the number of outstanding publish or keep alive requests which appear to be missing.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.ISession.GoodPublishRequestCount">
            <summary>
            Gets the number of good outstanding publish requests.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.ISession.OperationLimits">
            <summary>
            Stores the operation limits of a OPC UA Server.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.ISession.TransferSubscriptionsOnReconnect">
            <summary>
            If the subscriptions are transferred when a session is reconnected. 
            </summary>
            <remarks>
            Default <c>false</c>, set to <c>true</c> if subscriptions should
            be transferred after reconnect. Service must be supported by server.
            </remarks>   
        </member>
        <member name="E:Opc.Ua.Client.ISession.RenewUserIdentity">
            <summary>
            Raised before a reconnect operation completes.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ISession.Reconnect">
            <summary>
            Reconnects to the server after a network failure.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ISession.Reconnect(Opc.Ua.ITransportWaitingConnection)">
            <summary>
            Reconnects to the server after a network failure using a waiting connection.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ISession.Save(System.String)">
            <summary>
            Saves all the subscriptions of the session.
            </summary>
            <param name="filePath">The file path.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.Save(System.IO.Stream,System.Collections.Generic.IEnumerable{Opc.Ua.Client.Subscription})">
            <summary>
            Saves a set of subscriptions to a stream.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ISession.Save(System.String,System.Collections.Generic.IEnumerable{Opc.Ua.Client.Subscription})">
            <summary>
            Saves a set of subscriptions to a file.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ISession.Load(System.IO.Stream,System.Boolean)">
            <summary>
            Load the list of subscriptions saved in a file.
            </summary>
            <param name="stream">The stream.</param>
            <param name="transferSubscriptions">Load the subscriptions for transfer after load.</param>
            <returns>The list of loaded subscriptions</returns>
        </member>
        <member name="M:Opc.Ua.Client.ISession.Load(System.String,System.Boolean)">
            <summary>
            Load the list of subscriptions saved in a file.
            </summary>
            <param name="filePath">The file path.</param>
            <param name="transferSubscriptions">Load the subscriptions for transfer after load.</param>
            <returns>The list of loaded subscriptions</returns>
        </member>
        <member name="M:Opc.Ua.Client.ISession.FetchNamespaceTables">
            <summary>
            Updates the local copy of the server's namespace uri and server uri tables.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ISession.FetchTypeTree(Opc.Ua.ExpandedNodeId)">
            <summary>
            Updates the cache with the type and its subtypes.
            </summary>
            <remarks>
            This method can be used to ensure the TypeTree is populated.
            </remarks>
        </member>
        <member name="M:Opc.Ua.Client.ISession.FetchTypeTree(Opc.Ua.ExpandedNodeIdCollection)">
            <summary>
            Updates the cache with the types and its subtypes.
            </summary>
            <remarks>
            This method can be used to ensure the TypeTree is populated.
            </remarks>
        </member>
        <member name="M:Opc.Ua.Client.ISession.ReadAvailableEncodings(Opc.Ua.NodeId)">
            <summary>
            Returns the available encodings for a node
            </summary>
            <param name="variableId">The variable node.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.FindDataDescription(Opc.Ua.NodeId)">
            <summary>
            Returns the data description for the encoding.
            </summary>
            <param name="encodingId">The encoding Id.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.FindDataDictionary(Opc.Ua.NodeId)">
            <summary>
             Returns the data dictionary that contains the description.
            </summary>
            <param name="descriptionId">The description id.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.LoadDataDictionary(Opc.Ua.ReferenceDescription,System.Boolean)">
            <summary>
             Returns the data dictionary that contains the description.
            </summary>
            <param name="dictionaryNode">The dictionary id.</param>
            <param name="forceReload"></param>
            <returns>The dictionary.</returns>
        </member>
        <member name="M:Opc.Ua.Client.ISession.LoadDataTypeSystem(Opc.Ua.NodeId)">
            <summary>
            Loads all dictionaries of the OPC binary or Xml schema type system.
            </summary>
            <param name="dataTypeSystem">The type system.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.ReadNode(Opc.Ua.NodeId)">
            <summary>
            Reads the values for the node attributes and returns a node object.
            </summary>
            <param name="nodeId">The nodeId.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.ReadNode(Opc.Ua.NodeId,Opc.Ua.NodeClass,System.Boolean)">
            <summary>
            Reads the values for the node attributes and returns a node object.
            </summary>
            <remarks>
            If the nodeclass is known, only the supported attribute values are read.
            </remarks>
            <param name="nodeId">The nodeId.</param>
            <param name="nodeClass">The nodeclass of the node to read.</param>
            <param name="optionalAttributes">Read optional attributes.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.ReadNodes(System.Collections.Generic.IList{Opc.Ua.NodeId},System.Collections.Generic.IList{Opc.Ua.Node}@,System.Collections.Generic.IList{Opc.Ua.ServiceResult}@,System.Boolean)">
            <summary>
            Reads the values for the node attributes and returns a node object.
            Reads the nodeclass of the nodeIds, then reads
            the values for the node attributes and returns a node object collection.
            </summary>
            <param name="nodeIds">The nodeId collection.</param>
            <param name="nodeCollection">The node collection read from the server.</param>
            <param name="errors">The errors occured reading the nodes.</param>
            <param name="optionalAttributes">Set to <c>true</c> if optional attributes should not be omitted.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.ReadNodes(System.Collections.Generic.IList{Opc.Ua.NodeId},Opc.Ua.NodeClass,System.Collections.Generic.IList{Opc.Ua.Node}@,System.Collections.Generic.IList{Opc.Ua.ServiceResult}@,System.Boolean)">
            <summary>
            Reads the values for the node attributes and returns a node object collection.
            </summary>
            <remarks>
            If the nodeclass for the nodes in nodeIdCollection is already known,
            and passed as nodeClass, reads only values of required attributes.
            Otherwise NodeClass.Unspecified should be used.
            </remarks>
            <param name="nodeIds">The nodeId collection to read.</param>
            <param name="nodeClass">The nodeClass of all nodes in the collection. Set to <c>NodeClass.Unspecified</c> if the nodeclass is unknown.</param>
            <param name="nodeCollection">The node collection that is created from attributes read from the server.</param>
            <param name="errors">The errors that occured reading the nodes.</param>
            <param name="optionalAttributes">Set to <c>true</c> if optional attributes should not be omitted.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.ReadValue(Opc.Ua.NodeId)">
            <summary>
            Reads the value for a node.
            </summary>
            <param name="nodeId">The node Id.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.ReadValue(Opc.Ua.NodeId,System.Type)">
            <summary>
            Reads the value for a node an checks that it is the specified type.
            </summary>
            <param name="nodeId">The node id.</param>
            <param name="expectedType">The expected type.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.ReadValues(System.Collections.Generic.IList{Opc.Ua.NodeId},Opc.Ua.DataValueCollection@,System.Collections.Generic.IList{Opc.Ua.ServiceResult}@)">
            <summary>
            Reads the values for a node collection. Returns diagnostic errors.
            </summary>
            <param name="nodeIds">The node Id.</param>
            <param name="values">The data values read from the server.</param>
            <param name="errors">The errors reported by the server.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.FetchReferences(Opc.Ua.NodeId)">
            <summary>
            Fetches all references for the specified node.
            </summary>
            <param name="nodeId">The node id.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.FetchReferences(System.Collections.Generic.IList{Opc.Ua.NodeId},System.Collections.Generic.IList{Opc.Ua.ReferenceDescriptionCollection}@,System.Collections.Generic.IList{Opc.Ua.ServiceResult}@)">
            <summary>
            Fetches all references for the specified nodes.
            </summary>
            <param name="nodeIds">The node id collection.</param>
            <param name="referenceDescriptions">A list of reference collections.</param>
            <param name="errors">The errors reported by the server.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.Open(System.String,Opc.Ua.IUserIdentity)">
            <summary>
            Establishes a session with the server.
            </summary>
            <param name="sessionName">The name to assign to the session.</param>
            <param name="identity">The user identity.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.Open(System.String,System.UInt32,Opc.Ua.IUserIdentity,System.Collections.Generic.IList{System.String})">
            <summary>
            Establishes a session with the server.
            </summary>
            <param name="sessionName">The name to assign to the session.</param>
            <param name="sessionTimeout">The session timeout.</param>
            <param name="identity">The user identity.</param>
            <param name="preferredLocales">The list of preferred locales.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.Open(System.String,System.UInt32,Opc.Ua.IUserIdentity,System.Collections.Generic.IList{System.String},System.Boolean)">
            <summary>
            Establishes a session with the server.
            </summary>
            <param name="sessionName">The name to assign to the session.</param>
            <param name="sessionTimeout">The session timeout.</param>
            <param name="identity">The user identity.</param>
            <param name="preferredLocales">The list of preferred locales.</param>
            <param name="checkDomain">If set to <c>true</c> then the domain in the certificate must match the endpoint used.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.ChangePreferredLocales(Opc.Ua.StringCollection)">
            <summary>
            Updates the preferred locales used for the session.
            </summary>
            <param name="preferredLocales">The preferred locales.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.UpdateSession(Opc.Ua.IUserIdentity,Opc.Ua.StringCollection)">
            <summary>
            Updates the user identity and/or locales used for the session.
            </summary>
            <param name="identity">The user identity.</param>
            <param name="preferredLocales">The preferred locales.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.FindComponentIds(Opc.Ua.NodeId,System.Collections.Generic.IList{System.String},Opc.Ua.NodeIdCollection@,System.Collections.Generic.List{Opc.Ua.ServiceResult}@)">
            <summary>
            Finds the NodeIds for the components for an instance.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ISession.ReadValues(System.Collections.Generic.IList{Opc.Ua.NodeId},System.Collections.Generic.IList{System.Type},System.Collections.Generic.List{System.Object}@,System.Collections.Generic.List{Opc.Ua.ServiceResult}@)">
            <summary>
            Reads the values for a set of variables.
            </summary>
            <param name="variableIds">The variable ids.</param>
            <param name="expectedTypes">The expected types.</param>
            <param name="values">The list of returned values.</param>
            <param name="errors">The list of returned errors.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.ReadDisplayName(System.Collections.Generic.IList{Opc.Ua.NodeId},System.Collections.Generic.IList{System.String}@,System.Collections.Generic.IList{Opc.Ua.ServiceResult}@)">
            <summary>
            Reads the display name for a set of Nodes.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ISession.ReadNodesAsync(System.Collections.Generic.IList{Opc.Ua.NodeId},Opc.Ua.NodeClass,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Reads the values for the node attributes and returns a node object collection.
            </summary>
            <remarks>
            If the nodeclass for the nodes in nodeIdCollection is already known
            and passed as nodeClass, reads only values of required attributes.
            Otherwise NodeClass.Unspecified should be used.
            </remarks>
            <param name="nodeIds">The nodeId collection to read.</param>
            <param name="nodeClass">The nodeClass of all nodes in the collection. Set to <c>NodeClass.Unspecified</c> if the nodeclass is unknown.</param>
            <param name="optionalAttributes">Set to <c>true</c> if optional attributes should not be omitted.</param>
            <param name="ct">The cancellation token.</param>
            <returns>The node collection and associated errors.</returns>
        </member>
        <member name="M:Opc.Ua.Client.ISession.ReadValueAsync(Opc.Ua.NodeId,System.Threading.CancellationToken)">
            <summary>
            Reads the value for a node.
            </summary>
            <param name="nodeId">The node Id.</param>
            <param name="ct">The cancellation token for the request.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.ReadNodeAsync(Opc.Ua.NodeId,System.Threading.CancellationToken)">
            <summary>
            Reads the values for the node attributes and returns a node object.
            </summary>
            <param name="nodeId">The nodeId.</param>
            <param name="ct">The cancellation token for the request.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.ReadNodeAsync(Opc.Ua.NodeId,Opc.Ua.NodeClass,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Reads the values for the node attributes and returns a node object.
            </summary>
            <remarks>
            If the nodeclass is known, only the supported attribute values are read.
            </remarks>
            <param name="nodeId">The nodeId.</param>
            <param name="nodeClass">The nodeclass of the node to read.</param>
            <param name="optionalAttributes">Read optional attributes.</param>
            <param name="ct">The cancellation token for the request.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.ReadNodesAsync(System.Collections.Generic.IList{Opc.Ua.NodeId},System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Reads the values for the node attributes and returns a node object collection.
            Reads the nodeclass of the nodeIds, then reads
            the values for the node attributes and returns a node collection.
            </summary>
            <param name="nodeIds">The nodeId collection.</param>
            <param name="optionalAttributes">If optional attributes to read.</param>
            <param name="ct">The cancellation token.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.ReadValuesAsync(System.Collections.Generic.IList{Opc.Ua.NodeId},System.Threading.CancellationToken)">
            <summary>
            Reads the values for a node collection. Returns diagnostic errors.
            </summary>
            <param name="nodeIds">The node Id.</param>
            <param name="ct">The cancellation token for the request.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.Close(System.Int32)">
            <summary>
            Disconnects from the server and frees any network resources with the specified timeout.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ISession.Close(System.Boolean)">
            <summary>
            Close the session with the server and optionally closes the channel.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ISession.Close(System.Int32,System.Boolean)">
            <summary>
            Disconnects from the server and frees any network resources with the specified timeout.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ISession.CloseAsync(System.Threading.CancellationToken)">
            <summary>
            Disconnects from the server and frees any network resources with the default timeout.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ISession.CloseAsync(System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Close the session with the server and optionally closes the channel.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ISession.CloseAsync(System.Int32,System.Threading.CancellationToken)">
            <summary>
            Disconnects from the server and frees any network resources with the specified timeout.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ISession.CloseAsync(System.Int32,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Disconnects from the server and frees any network resources with the specified timeout.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ISession.AddSubscription(Opc.Ua.Client.Subscription)">
            <summary>
            Adds a subscription to the session.
            </summary>
            <param name="subscription">The subscription to add.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.RemoveSubscription(Opc.Ua.Client.Subscription)">
            <summary>
            Removes a subscription from the session.
            </summary>
            <param name="subscription">The subscription to remove.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.RemoveSubscriptions(System.Collections.Generic.IEnumerable{Opc.Ua.Client.Subscription})">
            <summary>
            Removes a list of subscriptions from the session.
            </summary>
            <param name="subscriptions">The list of subscriptions to remove.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.TransferSubscriptions(Opc.Ua.Client.SubscriptionCollection,System.Boolean)">
            <summary>
            Transfers a list of Subscriptions from another session.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ISession.RemoveTransferredSubscription(Opc.Ua.Client.Subscription)">
            <summary>
            Removes a transferred subscription from the session.
            Called by the session to which the subscription
            is transferred to obtain ownership. Internal.
            </summary>
            <param name="subscription">The subscription to remove.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.RemoveSubscriptionAsync(Opc.Ua.Client.Subscription)">
            <summary>
            Removes a subscription from the session.
            </summary>
            <param name="subscription">The subscription to remove.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.RemoveSubscriptionsAsync(System.Collections.Generic.IEnumerable{Opc.Ua.Client.Subscription})">
            <summary>
            Removes a list of subscriptions from the sessiont.
            </summary>
            <param name="subscriptions">The list of subscriptions to remove.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.Browse(Opc.Ua.RequestHeader,Opc.Ua.ViewDescription,Opc.Ua.NodeId,System.UInt32,Opc.Ua.BrowseDirection,Opc.Ua.NodeId,System.Boolean,System.UInt32,System.Byte[]@,Opc.Ua.ReferenceDescriptionCollection@)">
            <summary>
            Invokes the Browse service.
            </summary>
            <param name="requestHeader">The request header.</param>
            <param name="view">The view to browse.</param>
            <param name="nodeToBrowse">The node to browse.</param>
            <param name="maxResultsToReturn">The maximum number of returned values.</param>
            <param name="browseDirection">The browse direction.</param>
            <param name="referenceTypeId">The reference type id.</param>
            <param name="includeSubtypes">If set to <c>true</c> the subtypes of the ReferenceType will be included in the browse.</param>
            <param name="nodeClassMask">The node class mask.</param>
            <param name="continuationPoint">The continuation point.</param>
            <param name="references">The list of node references.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.BeginBrowse(Opc.Ua.RequestHeader,Opc.Ua.ViewDescription,Opc.Ua.NodeId,System.UInt32,Opc.Ua.BrowseDirection,Opc.Ua.NodeId,System.Boolean,System.UInt32,System.AsyncCallback,System.Object)">
            <summary>
            Begins an asynchronous invocation of the Browse service.
            </summary>
            <param name="requestHeader">The request header.</param>
            <param name="view">The view to browse.</param>
            <param name="nodeToBrowse">The node to browse.</param>
            <param name="maxResultsToReturn">The maximum number of returned values..</param>
            <param name="browseDirection">The browse direction.</param>
            <param name="referenceTypeId">The reference type id.</param>
            <param name="includeSubtypes">If set to <c>true</c> the subtypes of the ReferenceType will be included in the browse.</param>
            <param name="nodeClassMask">The node class mask.</param>
            <param name="callback">The callback.</param>
            <param name="asyncState"></param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.EndBrowse(System.IAsyncResult,System.Byte[]@,Opc.Ua.ReferenceDescriptionCollection@)">
            <summary>
            Finishes an asynchronous invocation of the Browse service.
            </summary>
            <param name="result">The result.</param>
            <param name="continuationPoint">The continuation point.</param>
            <param name="references">The list of node references.</param>
        </member>
        <member name="M:Opc.Ua.Client.ISession.BrowseNext(Opc.Ua.RequestHeader,System.Boolean,System.Byte[],System.Byte[]@,Opc.Ua.ReferenceDescriptionCollection@)">
            <summary>
            Invokes the BrowseNext service.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ISession.BeginBrowseNext(Opc.Ua.RequestHeader,System.Boolean,System.Byte[],System.AsyncCallback,System.Object)">
            <summary>
            Begins an asynchronous invocation of the BrowseNext service.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ISession.EndBrowseNext(System.IAsyncResult,System.Byte[]@,Opc.Ua.ReferenceDescriptionCollection@)">
            <summary>
            Finishes an asynchronous invocation of the BrowseNext service.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ISession.Call(Opc.Ua.NodeId,Opc.Ua.NodeId,System.Object[])">
            <summary>
            Calls the specified method and returns the output arguments.
            </summary>
            <param name="objectId">The NodeId of the object that provides the method.</param>
            <param name="methodId">The NodeId of the method to call.</param>
            <param name="args">The input arguments.</param>
            <returns>The list of output argument values.</returns>
        </member>
        <member name="M:Opc.Ua.Client.ISession.BeginPublish(System.Int32)">
            <summary>
            Sends an additional publish request.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ISession.Republish(System.UInt32,System.UInt32)">
            <summary>
            Sends a republish request.
            </summary>
        </member>
        <member name="T:Opc.Ua.Client.ISessionFactory">
            <summary>
            Object that creates instances of an ISessions object.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ISessionFactory.CreateAsync(Opc.Ua.ApplicationConfiguration,Opc.Ua.ConfiguredEndpoint,System.Boolean,System.String,System.UInt32,Opc.Ua.IUserIdentity,System.Collections.Generic.IList{System.String})">
            <summary>
            Creates a new communication session with a server by invoking the CreateSession service
            </summary>
            <param name="configuration">The configuration for the client application.</param>
            <param name="endpoint">The endpoint for the server.</param>
            <param name="updateBeforeConnect">If set to <c>true</c> the discovery endpoint is used to update the endpoint description before connecting.</param>
            <param name="sessionName">The name to assign to the session.</param>
            <param name="sessionTimeout">The timeout period for the session.</param>
            <param name="identity">The identity.</param>
            <param name="preferredLocales">The user identity to associate with the session.</param>
            <returns>The new session object</returns>
        </member>
        <member name="M:Opc.Ua.Client.ISessionFactory.CreateAsync(Opc.Ua.ApplicationConfiguration,Opc.Ua.ConfiguredEndpoint,System.Boolean,System.Boolean,System.String,System.UInt32,Opc.Ua.IUserIdentity,System.Collections.Generic.IList{System.String})">
            <summary>
            Creates a new communication session with a server by invoking the CreateSession service
            </summary>
            <param name="configuration">The configuration for the client application.</param>
            <param name="endpoint">The endpoint for the server.</param>
            <param name="updateBeforeConnect">If set to <c>true</c> the discovery endpoint is used to update the endpoint description before connecting.</param>
            <param name="checkDomain">If set to <c>true</c> then the domain in the certificate must match the endpoint used.</param>
            <param name="sessionName">The name to assign to the session.</param>
            <param name="sessionTimeout">The timeout period for the session.</param>
            <param name="identity">The user identity to associate with the session.</param>
            <param name="preferredLocales">The preferred locales.</param>
            <returns>The new session object.</returns>
        </member>
        <member name="M:Opc.Ua.Client.ISessionFactory.CreateAsync(Opc.Ua.ApplicationConfiguration,Opc.Ua.ITransportWaitingConnection,Opc.Ua.ConfiguredEndpoint,System.Boolean,System.Boolean,System.String,System.UInt32,Opc.Ua.IUserIdentity,System.Collections.Generic.IList{System.String})">
            <summary>
            Creates a new communication session with a server using a reverse connection.
            </summary>
            <param name="configuration">The configuration for the client application.</param>
            <param name="connection">The client endpoint for the reverse connect.</param>
            <param name="endpoint">The endpoint for the server.</param>
            <param name="updateBeforeConnect">If set to <c>true</c> the discovery endpoint is used to update the endpoint description before connecting.</param>
            <param name="checkDomain">If set to <c>true</c> then the domain in the certificate must match the endpoint used.</param>
            <param name="sessionName">The name to assign to the session.</param>
            <param name="sessionTimeout">The timeout period for the session.</param>
            <param name="identity">The user identity to associate with the session.</param>
            <param name="preferredLocales">The preferred locales.</param>
            <returns>The new session object.</returns>
        </member>
        <member name="M:Opc.Ua.Client.ISessionFactory.CreateAsync(Opc.Ua.ApplicationConfiguration,Opc.Ua.Client.ReverseConnectManager,Opc.Ua.ConfiguredEndpoint,System.Boolean,System.Boolean,System.String,System.UInt32,Opc.Ua.IUserIdentity,System.Collections.Generic.IList{System.String},System.Threading.CancellationToken)">
            <summary>
            Creates a new communication session with a server using a reverse connect manager.
            </summary>
            <param name="configuration">The configuration for the client application.</param>
            <param name="reverseConnectManager">The reverse connect manager for the client connection.</param>
            <param name="endpoint">The endpoint for the server.</param>
            <param name="updateBeforeConnect">If set to <c>true</c> the discovery endpoint is used to update the endpoint description before connecting.</param>
            <param name="checkDomain">If set to <c>true</c> then the domain in the certificate must match the endpoint used.</param>
            <param name="sessionName">The name to assign to the session.</param>
            <param name="sessionTimeout">The timeout period for the session.</param>
            <param name="userIdentity">The user identity to associate with the session.</param>
            <param name="preferredLocales">The preferred locales.</param>
            <param name="ct">The cancellation token.</param>
            <returns>The new session object.</returns>
        </member>
        <member name="M:Opc.Ua.Client.ISessionFactory.RecreateAsync(Opc.Ua.Client.ISession)">
            <summary>
            Recreates a session based on a specified template.
            </summary>
            <param name="template">The ISession object to use as template</param>
            <returns>The new session object.</returns>
        </member>
        <member name="M:Opc.Ua.Client.ISessionFactory.RecreateAsync(Opc.Ua.Client.ISession,Opc.Ua.ITransportWaitingConnection)">
            <summary>
            Recreates a session based on a specified template.
            </summary>
            <param name="template">The ISession object to use as template</param>
            <param name="connection">The waiting reverse connection.</param>
            <returns>The new session object.</returns>
        </member>
        <member name="T:Opc.Ua.Client.MonitoredItem">
            <summary>
            A monitored item.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItem.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Opc.Ua.Client.MonitoredItem"/> class.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItem.#ctor(System.UInt32)">
            <summary>
            Initializes a new instance of the <see cref="T:Opc.Ua.Client.MonitoredItem"/> class.
            </summary>
            <param name="clientHandle">The client handle. The caller must ensure it uniquely identifies the monitored item.</param>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItem.#ctor(Opc.Ua.Client.MonitoredItem)">
            <summary>
            Initializes a new instance of the <see cref="T:Opc.Ua.Client.MonitoredItem"/> class.
            </summary>
            <param name="template">The template used to specify the monitoring parameters.</param>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItem.#ctor(Opc.Ua.Client.MonitoredItem,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Opc.Ua.Client.MonitoredItem"/> class.
            </summary>
            <param name="template">The template used to specify the monitoring parameters.</param>
            <param name="copyEventHandlers">if set to <c>true</c> the event handlers are copied.</param>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItem.#ctor(Opc.Ua.Client.MonitoredItem,System.Boolean,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Opc.Ua.Client.MonitoredItem"/> class.
            </summary>
            <param name="template">The template used to specify the monitoring parameters.</param>
            <param name="copyEventHandlers">if set to <c>true</c> the event handlers are copied.</param>
            <param name="copyClientHandle">if set to <c>true</c> the clientHandle is of the template copied.</param>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItem.Initialize(System.Runtime.Serialization.StreamingContext)">
            <summary>
            Called by the .NET framework during deserialization.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItem.Initialize">
            <summary>
            Sets the private members to default values.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItem.DisplayName">
            <summary>
            A display name for the monitored item.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItem.StartNodeId">
            <summary>
            The start node for the browse path that identifies the node to monitor.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItem.RelativePath">
            <summary>
            The relative path from the browse path to the node to monitor.
            </summary>
            <remarks>
            A null or empty string specifies that the start node id should be monitored.
            </remarks>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItem.NodeClass">
            <summary>
            The node class of the node being monitored (affects the type of filter available).
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItem.AttributeId">
            <summary>
            The attribute to monitor.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItem.IndexRange">
            <summary>
            The range of array indexes to monitor.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItem.Encoding">
            <summary>
            The encoding to use when returning notifications.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItem.MonitoringMode">
            <summary>
            The monitoring mode.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItem.SamplingInterval">
            <summary>
            The sampling interval.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItem.Filter">
            <summary>
            The filter to use to select values to return.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItem.QueueSize">
            <summary>
            The length of the queue used to buffer values.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItem.DiscardOldest">
            <summary>
            Whether to discard the oldest entries in the queue when it is full.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItem.ServerId">
            <summary>
            Server-assigned id for the MonitoredItem.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItem.Subscription">
            <summary>
            The subscription that owns the monitored item.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItem.Handle">
            <summary>
            A local handle assigned to the monitored item.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItem.Created">
            <summary>
            Whether the item has been created on the server.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItem.ClientHandle">
            <summary>
            The identifier assigned by the client.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItem.ResolvedNodeId">
            <summary>
            The node id to monitor after applying any relative path.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItem.AttributesModified">
            <summary>
            Whether the monitoring attributes have been modified since the item was created.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItem.Status">
            <summary>
            The status associated with the monitored item.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItem.CacheQueueSize">
            <summary>
            Returns the queue size used by the cache.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItem.LastValue">
            <summary>
            The last value or event received from the server.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItem.DequeueValues">
            <summary>
            Read all values in the cache queue.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItem.DequeueEvents">
            <summary>
            Read all events in the cache queue.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItem.LastMessage">
            <summary>
            The last message containing a notification for the item.
            </summary>
        </member>
        <member name="E:Opc.Ua.Client.MonitoredItem.Notification">
            <summary>
            Raised when a new notification arrives.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItem.SaveValueInCache(Opc.Ua.IEncodeable)">
            <summary>
            Saves a data change or event in the cache.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItem.MemberwiseClone">
            <summary>
            Creates a deep copy of the object.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItem.SetError(Opc.Ua.ServiceResult)">
            <summary>
            Sets the error status for the monitored item.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItem.SetResolvePathResult(Opc.Ua.BrowsePathResult,System.Int32,Opc.Ua.DiagnosticInfoCollection,Opc.Ua.ResponseHeader)">
            <summary>
            Updates the object with the results of a translate browse path request.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItem.SetCreateResult(Opc.Ua.MonitoredItemCreateRequest,Opc.Ua.MonitoredItemCreateResult,System.Int32,Opc.Ua.DiagnosticInfoCollection,Opc.Ua.ResponseHeader)">
            <summary>
            Updates the object with the results of a create monitored item request.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItem.SetModifyResult(Opc.Ua.MonitoredItemModifyRequest,Opc.Ua.MonitoredItemModifyResult,System.Int32,Opc.Ua.DiagnosticInfoCollection,Opc.Ua.ResponseHeader)">
            <summary>
            Updates the object with the results of a modify monitored item request.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItem.SetTransferResult(System.UInt32)">
            <summary>
            Updates the object with the results of a transfer subscription request.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItem.SetDeleteResult(Opc.Ua.StatusCode,System.Int32,Opc.Ua.DiagnosticInfoCollection,Opc.Ua.ResponseHeader)">
            <summary>
            Updates the object with the results of a delete monitored item request.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItem.GetFieldName(System.Int32)">
            <summary>
            Returns the field name the specified SelectClause in the EventFilter.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItem.GetFieldValue(Opc.Ua.EventFieldList,Opc.Ua.NodeId,System.String,System.UInt32)">
            <summary>
            Returns value of the field name containing the event type.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItem.GetFieldValue(Opc.Ua.EventFieldList,Opc.Ua.NodeId,Opc.Ua.QualifiedName)">
            <summary>
            Returns value of the field name containing the event type.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItem.GetFieldValue(Opc.Ua.EventFieldList,Opc.Ua.NodeId,System.Collections.Generic.IList{Opc.Ua.QualifiedName},System.UInt32)">
            <summary>
            Returns value of the field name containing the event type.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItem.GetEventType(Opc.Ua.EventFieldList)">
            <summary>
            Returns value of the field name containing the event type.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItem.GetEventTime(Opc.Ua.EventFieldList)">
            <summary>
            Returns value of the field name containing the event type.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItem.GetServiceResult(Opc.Ua.IEncodeable)">
            <summary>
            The service result for a data change notification.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItem.GetServiceResult(Opc.Ua.IEncodeable,System.Int32)">
            <summary>
            The service result for a field in an notification (the field must contain a Status object).
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItem.ValidateFilter(Opc.Ua.NodeClass,Opc.Ua.MonitoringFilter)">
            <summary>
            Throws an exception if the flter cannot be used with the node class.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItem.UseDefaultEventFilter">
            <summary>
            Sets the default event filter.
            </summary>
        </member>
        <member name="T:Opc.Ua.Client.MonitoredItemNotificationEventArgs">
            <summary>
            The event arguments provided when a new notification message arrives.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItemNotificationEventArgs.#ctor(Opc.Ua.IEncodeable)">
            <summary>
            Creates a new instance.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItemNotificationEventArgs.NotificationValue">
            <summary>
            The new notification.
            </summary>
        </member>
        <member name="T:Opc.Ua.Client.MonitoredItemNotificationEventHandler">
            <summary>
            The delegate used to receive monitored item value notifications.
            </summary>
        </member>
        <member name="T:Opc.Ua.Client.MonitoredItemDataCache">
            <summary>
            An item in the cache
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItemDataCache.#ctor(System.Int32)">
            <summary>
            Constructs a cache for a monitored item.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItemDataCache.QueueSize">
            <summary>
            The size of the queue to maintain.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItemDataCache.LastValue">
            <summary>
            The last value received from the server.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItemDataCache.Publish">
            <summary>
            Returns all values in the queue.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItemDataCache.OnNotification(Opc.Ua.MonitoredItemNotification)">
            <summary>
            Saves a notification in the cache.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItemDataCache.SetQueueSize(System.Int32)">
            <summary>
            Changes the queue size.
            </summary>
        </member>
        <member name="T:Opc.Ua.Client.MonitoredItemEventCache">
            <summary>
            Saves the events received from the srever.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItemEventCache.#ctor(System.Int32)">
            <summary>
            Constructs a cache for a monitored item.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItemEventCache.QueueSize">
            <summary>
            The size of the queue to maintain.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItemEventCache.LastEvent">
            <summary>
            The last event received.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItemEventCache.Publish">
            <summary>
            Returns all events in the queue.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItemEventCache.OnNotification(Opc.Ua.EventFieldList)">
            <summary>
            Saves a notification in the cache.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItemEventCache.SetQueueSize(System.Int32)">
            <summary>
            Changes the queue size.
            </summary>
        </member>
        <member name="T:Opc.Ua.Client.MonitoredItemStatus">
            <summary>
            The current status of monitored item.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItemStatus.#ctor">
            <summary>
            Creates a empty object.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItemStatus.Id">
            <summary>
            The identifier assigned by the server.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItemStatus.Created">
            <summary>
            Whether the item has been created on the server.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItemStatus.Error">
            <summary>
            Any error condition associated with the monitored item.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItemStatus.NodeId">
            <summary>
            The node id being monitored.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItemStatus.AttributeId">
            <summary>
            The attribute being monitored.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItemStatus.IndexRange">
            <summary>
            The range of array indexes to being monitored.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItemStatus.DataEncoding">
            <summary>
            The encoding to use when returning notifications.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItemStatus.MonitoringMode">
            <summary>
            The monitoring mode.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItemStatus.ClientHandle">
            <summary>
            The identifier assigned by the client.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItemStatus.SamplingInterval">
            <summary>
            The sampling interval.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItemStatus.Filter">
            <summary>
            The filter to use to select values to return.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItemStatus.QueueSize">
            <summary>
            The length of the queue used to buffer values.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.MonitoredItemStatus.DiscardOldest">
            <summary>
            Whether to discard the oldest entries in the queue when it is full.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItemStatus.SetMonitoringMode(Opc.Ua.MonitoringMode)">
            <summary>
            Updates the monitoring mode.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItemStatus.SetResolvePathResult(Opc.Ua.BrowsePathResult,Opc.Ua.ServiceResult)">
            <summary>
            Updates the object with the results of a translate browse paths request.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItemStatus.SetCreateResult(Opc.Ua.MonitoredItemCreateRequest,Opc.Ua.MonitoredItemCreateResult,Opc.Ua.ServiceResult)">
            <summary>
            Updates the object with the results of a create monitored item request.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItemStatus.SetTransferResult(Opc.Ua.Client.MonitoredItem)">
            <summary>
            Updates the object with the results of a transfer monitored item request.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItemStatus.SetModifyResult(Opc.Ua.MonitoredItemModifyRequest,Opc.Ua.MonitoredItemModifyResult,Opc.Ua.ServiceResult)">
            <summary>
            Updates the object with the results of a modify monitored item request.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItemStatus.SetDeleteResult(Opc.Ua.ServiceResult)">
            <summary>
            Updates the object with the results of a delete item request.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.MonitoredItemStatus.SetError(Opc.Ua.ServiceResult)">
            <summary>
            Sets the error state for the monitored item status.
            </summary>
        </member>
        <member name="T:Opc.Ua.Client.NodeCache">
            <summary>
            An implementation of a client side nodecache.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.NodeCache.#ctor(Opc.Ua.Client.ISession)">
            <summary>
            Initializes the object with default values.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.NodeCache.NamespaceUris">
            <inheritdoc/>
        </member>
        <member name="P:Opc.Ua.Client.NodeCache.ServerUris">
            <inheritdoc/>
        </member>
        <member name="P:Opc.Ua.Client.NodeCache.TypeTree">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.NodeCache.Exists(Opc.Ua.ExpandedNodeId)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.NodeCache.Find(Opc.Ua.ExpandedNodeId)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.NodeCache.Find(System.Collections.Generic.IList{Opc.Ua.ExpandedNodeId})">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.NodeCache.Find(Opc.Ua.ExpandedNodeId,Opc.Ua.NodeId,System.Boolean,System.Boolean,Opc.Ua.QualifiedName)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.NodeCache.Find(Opc.Ua.ExpandedNodeId,Opc.Ua.NodeId,System.Boolean,System.Boolean)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.NodeCache.IsKnown(Opc.Ua.ExpandedNodeId)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.NodeCache.IsKnown(Opc.Ua.NodeId)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.NodeCache.FindSuperType(Opc.Ua.ExpandedNodeId)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.NodeCache.FindSuperType(Opc.Ua.NodeId)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.NodeCache.FindSubTypes(Opc.Ua.ExpandedNodeId)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.NodeCache.IsTypeOf(Opc.Ua.ExpandedNodeId,Opc.Ua.ExpandedNodeId)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.NodeCache.IsTypeOf(Opc.Ua.NodeId,Opc.Ua.NodeId)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.NodeCache.FindReferenceTypeName(Opc.Ua.NodeId)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.NodeCache.FindReferenceType(Opc.Ua.QualifiedName)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.NodeCache.IsEncodingOf(Opc.Ua.ExpandedNodeId,Opc.Ua.ExpandedNodeId)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.NodeCache.IsEncodingFor(Opc.Ua.NodeId,Opc.Ua.ExtensionObject)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.NodeCache.IsEncodingFor(Opc.Ua.NodeId,System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.NodeCache.FindDataTypeId(Opc.Ua.ExpandedNodeId)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.NodeCache.FindDataTypeId(Opc.Ua.NodeId)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.NodeCache.LoadUaDefinedTypes(Opc.Ua.ISystemContext)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.NodeCache.Clear">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.NodeCache.FetchNode(Opc.Ua.ExpandedNodeId)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.NodeCache.FetchNodes(System.Collections.Generic.IList{Opc.Ua.ExpandedNodeId})">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.NodeCache.FetchSuperTypes(Opc.Ua.ExpandedNodeId)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.NodeCache.FindReferences(Opc.Ua.ExpandedNodeId,Opc.Ua.NodeId,System.Boolean,System.Boolean)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.NodeCache.FindReferences(System.Collections.Generic.IList{Opc.Ua.ExpandedNodeId},System.Collections.Generic.IList{Opc.Ua.NodeId},System.Boolean,System.Boolean)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.NodeCache.GetDisplayText(Opc.Ua.INode)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.NodeCache.GetDisplayText(Opc.Ua.ExpandedNodeId)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.NodeCache.GetDisplayText(Opc.Ua.ReferenceDescription)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.NodeCache.BuildBrowsePath(Opc.Ua.ILocalNode,System.Collections.Generic.IList{Opc.Ua.QualifiedName})">
            <inheritdoc/>
        </member>
        <member name="T:Opc.Ua.Client.OpcUaClientEventSource">
            <summary>
            Event source for high performance logging.
            </summary>
        </member>
        <member name="F:Opc.Ua.Client.OpcUaClientEventSource.SubscriptionStateMessage">
            <summary>
            The client messages.
            </summary>
        </member>
        <member name="F:Opc.Ua.Client.OpcUaClientEventSource.SubscriptionStateMessageEventId">
            <summary>
            The Client Event Ids used for event messages, when calling ILogger.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.OpcUaClientEventSource.SubscriptionState(System.String,System.UInt32,System.DateTime,System.Int32,System.Double,System.UInt32,System.Boolean,System.UInt32)">
            <summary>
            The state of the client subscription.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.OpcUaClientEventSource.Notification(System.Int32,System.String)">
            <summary>
            The notification message. Called internally to convert wrapped value.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.OpcUaClientEventSource.NotificationReceived(System.Int32,System.Int32)">
            <summary>
            A notification received in Publish complete.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.OpcUaClientEventSource.PublishStart(System.Int32)">
            <summary>
            A Publish begin received.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.OpcUaClientEventSource.PublishStop(System.Int32)">
            <summary>
            A Publish complete received.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.OpcUaClientEventSource.NotificationValue(System.UInt32,Opc.Ua.Variant)">
            <summary>
            Log a Notification.
            </summary>
        </member>
        <member name="T:Opc.Ua.Client.ReverseConnectManager">
            <summary>
            The implementation of a reverse connect client manager.
            </summary>
            <remarks>
            This reverse connect manager allows to register for reverse connections
            with various strategies:
            i) take any connection.
            ii) filter for a specific application Uri and Url scheme.
            iii) filter for the Url.
            Second, any filter can be combined with the Once or Always flag.
            </remarks>
        </member>
        <member name="F:Opc.Ua.Client.ReverseConnectManager.DefaultWaitTimeout">
            <summary>
            A default value for reverse hello configurations, if undefined.
            </summary>
            <remarks>
            This value is used as wait timeout if the value is undefined by a caller.
            </remarks>
        </member>
        <member name="T:Opc.Ua.Client.ReverseConnectManager.ReverseConnectManagerState">
            <summary>
            Internal state of the reverse connect manager.
            </summary>
        </member>
        <member name="T:Opc.Ua.Client.ReverseConnectManager.ReverseConnectHostState">
            <summary>
            Internal state of the reverse connect host.
            </summary>
        </member>
        <member name="T:Opc.Ua.Client.ReverseConnectManager.ReverseConnectStrategy">
            <summary>
            Specify the strategy for the reverse connect registration.
            </summary>
        </member>
        <member name="F:Opc.Ua.Client.ReverseConnectManager.ReverseConnectStrategy.Undefined">
            <summary>
            Undefined strategy, defaults to Once.
            </summary>
        </member>
        <member name="F:Opc.Ua.Client.ReverseConnectManager.ReverseConnectStrategy.Once">
            <summary>
            Remove entry after reverse connect callback.
            </summary>
        </member>
        <member name="F:Opc.Ua.Client.ReverseConnectManager.ReverseConnectStrategy.Always">
            <summary>
            Always callback on matching url or uri.
            </summary>
        </member>
        <member name="F:Opc.Ua.Client.ReverseConnectManager.ReverseConnectStrategy.Any">
            <summary>
            Flag for masking any connection.
            </summary>
        </member>
        <member name="F:Opc.Ua.Client.ReverseConnectManager.ReverseConnectStrategy.AnyOnce">
            <summary>
            Respond to any incoming reverse connection,
            remove entry after reverse connect callback.
            </summary>
        </member>
        <member name="F:Opc.Ua.Client.ReverseConnectManager.ReverseConnectStrategy.AnyAlways">
            <summary>
            Respond to any incoming reverse connection,
            always callback.
            </summary>
        </member>
        <member name="T:Opc.Ua.Client.ReverseConnectManager.ReverseConnectInfo">
            <summary>
            Entry for a client reverse connect registration.
            </summary>
        </member>
        <member name="T:Opc.Ua.Client.ReverseConnectManager.Registration">
            <summary>
            Record to store information on a client
            registration for a reverse connect event.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ReverseConnectManager.Registration.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2,System.Uri,System.EventHandler{Opc.Ua.ConnectionWaitingEventArgs})">
            <summary>
            Register with the server certificate.
            </summary>
            <param name="serverCertificate"></param>
            <param name="endpointUrl"></param>
            <param name="onConnectionWaiting"></param>
        </member>
        <member name="M:Opc.Ua.Client.ReverseConnectManager.#ctor">
            <summary>
            Initializes the object with default values.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ReverseConnectManager.Dispose">
            <summary>
            Dispose implementation.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ReverseConnectManager.Dispose(System.Boolean)">
            <summary>
            An overrideable version of the Dispose.
            </summary>
            <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="M:Opc.Ua.Client.ReverseConnectManager.OnConfigurationChanged(System.Object,Opc.Ua.ConfigurationWatcherEventArgs)">
            <summary>
            Raised when the configuration changes.
            </summary>
            <param name="sender">The sender.</param>
            <param name="args">The <see cref="T:Opc.Ua.ConfigurationWatcherEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Opc.Ua.Client.ReverseConnectManager.OnUpdateConfiguration(Opc.Ua.ApplicationConfiguration)">
            <summary>
            Called when the configuration is changed on disk.
            </summary>
            <param name="configuration">The configuration.</param>
        </member>
        <member name="M:Opc.Ua.Client.ReverseConnectManager.OnUpdateConfiguration(Opc.Ua.ReverseConnectClientConfiguration)">
            <summary>
            Called when the reverse connect configuration is changed.
            </summary>
            <remarks>
             An empty configuration or null stops service on all configured endpoints.
            </remarks>
            <param name="configuration">The client endpoint configuration.</param>
        </member>
        <member name="M:Opc.Ua.Client.ReverseConnectManager.OpenHosts">
            <summary>
            Open host ports.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ReverseConnectManager.CloseHosts">
            <summary>
            Close host ports.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ReverseConnectManager.DisposeHosts">
            <summary>
            Dispose the hosts;
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ReverseConnectManager.AddEndpoint(System.Uri)">
            <summary>
            Add endpoint for reverse connection.
            </summary>
            <param name="endpointUrl"></param>
        </member>
        <member name="M:Opc.Ua.Client.ReverseConnectManager.StartService(Opc.Ua.ApplicationConfiguration)">
            <summary>
            Starts the server application.
            </summary>
            <param name="configuration">The configuration.</param>
        </member>
        <member name="M:Opc.Ua.Client.ReverseConnectManager.StartService(Opc.Ua.ReverseConnectClientConfiguration)">
            <summary>
            Starts the server application.
            </summary>
            <param name="configuration">The configuration.</param>
        </member>
        <member name="M:Opc.Ua.Client.ReverseConnectManager.ClearWaitingConnections">
            <summary>
            Clears all waiting reverse connectino handlers.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ReverseConnectManager.WaitForConnection(System.Uri,System.String,System.Threading.CancellationToken)">
            <summary>
            Helper to wait for a reverse connection.
            </summary>
            <param name="endpointUrl"></param>
            <param name="serverUri"></param>
            <param name="ct"></param>
        </member>
        <member name="M:Opc.Ua.Client.ReverseConnectManager.RegisterWaitingConnection(System.Uri,System.String,System.EventHandler{Opc.Ua.ConnectionWaitingEventArgs},Opc.Ua.Client.ReverseConnectManager.ReverseConnectStrategy)">
            <summary>
            Register for a waiting reverse connection.
            </summary>
            <param name="endpointUrl">The endpoint Url of the reverse connection.</param>
            <param name="serverUri">Optional. The server application Uri of the reverse connection.</param>
            <param name="onConnectionWaiting">The callback</param>
            <param name="reverseConnectStrategy">The reverse connect callback strategy.</param>
        </member>
        <member name="M:Opc.Ua.Client.ReverseConnectManager.UnregisterWaitingConnection(System.Int32)">
            <summary>
            Unregister reverse connection callback.
            </summary>
            <param name="hashCode">The hashcode returned by the registration.</param>
        </member>
        <member name="M:Opc.Ua.Client.ReverseConnectManager.StopService">
            <summary>
            Called before the server stops
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ReverseConnectManager.StartService">
            <summary>
            Called to start hosting the reverse connect ports.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ReverseConnectManager.ClearEndpoints(System.Boolean)">
            <summary>
            Remove configuration endpoints from list.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ReverseConnectManager.AddEndpointInternal(System.Uri,System.Boolean)">
            <summary>
            Add endpoint for reverse connection.
            </summary>
            <param name="endpointUrl">The endpoint Url of the reverse connect client endpoint.</param>
            <param name="configEntry">Tf this is an entry in the application configuration.</param>
        </member>
        <member name="M:Opc.Ua.Client.ReverseConnectManager.OnConnectionWaiting(System.Object,Opc.Ua.ConnectionWaitingEventArgs)">
            <summary>
            Raised when a reverse connection is waiting,
            finds and calls a waiting connection.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ReverseConnectManager.MatchRegistration(System.Object,Opc.Ua.ConnectionWaitingEventArgs)">
            <summary>
            Match the waiting connection with a registration, callback registration,
            return if connection is accepted in event.
            </summary>
            <returns>true if a match was found.</returns>
        </member>
        <member name="M:Opc.Ua.Client.ReverseConnectManager.OnConnectionStatusChanged(System.Object,Opc.Ua.ConnectionStatusEventArgs)">
            <summary>
            Raised when a connection status changes.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.ReverseConnectManager.CancelAndRenewTokenSource">
            <summary>
            Renew the cancellation token after use.
            </summary>
        </member>
        <member name="T:Opc.Ua.Client.Session">
            <summary>
            Manages a session with a server.
            </summary>
            <summary>
            Manages a session with a server.
            Contains the async versions of the public session api.
            </summary>
            <summary>
            Obsolete warnings for service calls which should not be used when using the Session API.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.#ctor(Opc.Ua.ISessionChannel,Opc.Ua.ApplicationConfiguration,Opc.Ua.ConfiguredEndpoint)">
            <summary>
            Constructs a new instance of the <see cref="T:Opc.Ua.Client.Session"/> class.
            </summary>
            <param name="channel">The channel used to communicate with the server.</param>
            <param name="configuration">The configuration for the client application.</param>
            <param name="endpoint">The endpoint use to initialize the channel.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.#ctor(Opc.Ua.ITransportChannel,Opc.Ua.ApplicationConfiguration,Opc.Ua.ConfiguredEndpoint,System.Security.Cryptography.X509Certificates.X509Certificate2,Opc.Ua.EndpointDescriptionCollection,Opc.Ua.StringCollection)">
            <summary>
            Constructs a new instance of the <see cref="T:Opc.Ua.Client.ISession"/> class.
            </summary>
            <param name="channel">The channel used to communicate with the server.</param>
            <param name="configuration">The configuration for the client application.</param>
            <param name="endpoint">The endpoint used to initialize the channel.</param>
            <param name="clientCertificate">The certificate to use for the client.</param>
            <param name="availableEndpoints">The list of available endpoints returned by server in GetEndpoints() response.</param>
            <param name="discoveryProfileUris">The value of profileUris used in GetEndpoints() request.</param>
            <remarks>
            The application configuration is used to look up the certificate if none is provided.
            The clientCertificate must have the private key. This will require that the certificate
            be loaded from a certicate store. Converting a DER encoded blob to a X509Certificate2
            will not include a private key.
            The <i>availableEndpoints</i> and <i>discoveryProfileUris</i> parameters are used to validate
            that the list of EndpointDescriptions returned at GetEndpoints matches the list returned at CreateSession.
            </remarks>
        </member>
        <member name="M:Opc.Ua.Client.Session.#ctor(Opc.Ua.ITransportChannel,Opc.Ua.Client.Session,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Opc.Ua.Client.ISession"/> class.
            </summary>
            <param name="channel">The channel.</param>
            <param name="template">The template session.</param>
            <param name="copyEventHandlers">if set to <c>true</c> the event handlers are copied.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.Initialize(Opc.Ua.ITransportChannel,Opc.Ua.ApplicationConfiguration,Opc.Ua.ConfiguredEndpoint,System.Security.Cryptography.X509Certificates.X509Certificate2)">
            <summary>
            Initializes the channel.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.Initialize">
            <summary>
            Sets the object members to default values.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.ValidateClientConfiguration(Opc.Ua.ApplicationConfiguration)">
            <summary>
            Check if all required configuration fields are populated.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.ValidateServerNonce(Opc.Ua.IUserIdentity,System.Byte[],System.String,System.Byte[],Opc.Ua.MessageSecurityMode)">
            <summary>
            Validates the server nonce and security parameters of user identity.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.Dispose(System.Boolean)">
            <summary>
            Closes the session and the underlying channel.
            </summary>
        </member>
        <member name="E:Opc.Ua.Client.Session.KeepAlive">
            <summary>
            Raised when a keep alive arrives from the server or an error is detected.
            </summary>
            <remarks>
            Once a session is created a timer will periodically read the server state and current time.
            If this read operation succeeds this event will be raised each time the keep alive period elapses.
            If an error is detected (KeepAliveStopped == true) then this event will be raised as well.
            </remarks>
        </member>
        <member name="E:Opc.Ua.Client.Session.Notification">
            <summary>
            Raised when a notification message arrives in a publish response.
            </summary>
            <remarks>
            All publish requests are managed by the Session object. When a response arrives it is
            validated and passed to the appropriate Subscription object and this event is raised.
            </remarks>
        </member>
        <member name="E:Opc.Ua.Client.Session.PublishError">
            <summary>
            Raised when an exception occurs while processing a publish response.
            </summary>
            <remarks>
            Exceptions in a publish response are not necessarily fatal and the Session will
            attempt to recover by issuing Republish requests if missing messages are detected.
            That said, timeout errors may be a symptom of a OperationTimeout that is too short
            when compared to the shortest PublishingInterval/KeepAliveCount amount the current
            Subscriptions. The OperationTimeout should be twice the minimum value for
            PublishingInterval*KeepAliveCount.
            </remarks>
        </member>
        <member name="E:Opc.Ua.Client.Session.SubscriptionsChanged">
            <summary>
            Raised when a subscription is added or removed
            </summary>
        </member>
        <member name="E:Opc.Ua.Client.Session.SessionClosing">
            <summary>
            Raised to indicate the session is closing.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Session.SessionFactory">
            <summary>
            A session factory that was used to create the session.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Session.ConfiguredEndpoint">
            <summary>
            Gets the endpoint used to connect to the server.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Session.SessionName">
            <summary>
            Gets the name assigned to the session.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Session.SessionTimeout">
            <summary>
            Gets the period for wich the server will maintain the session if there is no communication from the client.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Session.Handle">
            <summary>
            Gets the local handle assigned to the session.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Session.Identity">
            <summary>
            Gets the user identity currently used for the session.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Session.IdentityHistory">
            <summary>
            Gets a list of user identities that can be used to connect to the server.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Session.NamespaceUris">
            <summary>
            Gets the table of namespace uris known to the server.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Session.ServerUris">
            <summary>
            Gets the table of remote server uris known to the server.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Session.SystemContext">
            <summary>
            Gets the system context for use with the session.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Session.Factory">
            <summary>
            Gets the factory used to create encodeable objects that the server understands.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Session.TypeTree">
            <summary>
            Gets the cache of the server's type tree.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Session.NodeCache">
            <summary>
            Gets the cache of nodes fetched from the server.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Session.FilterContext">
            <summary>
            Gets the context to use for filter operations.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Session.PreferredLocales">
            <summary>
            Gets the locales that the server should use when returning localized text.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Session.DataTypeSystem">
            <summary>
            Gets the data type system dictionaries in use.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Session.Subscriptions">
            <summary>
            Gets the subscriptions owned by the session.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Session.SubscriptionCount">
            <summary>
            Gets the number of subscriptions owned by the session.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Session.DeleteSubscriptionsOnClose">
            <summary>
            If the subscriptions are deleted when a session is closed. 
            </summary>
            <remarks>
            Default <c>true</c>, set to <c>false</c> if subscriptions need to
            be transferred or for durable subscriptions.
            </remarks>   
        </member>
        <member name="P:Opc.Ua.Client.Session.TransferSubscriptionsOnReconnect">
            <summary>
            If the subscriptions are transferred when a session is reconnected. 
            </summary>
            <remarks>
            Default <c>false</c>, set to <c>true</c> if subscriptions should
            be transferred after reconnect. Service must be supported by server.
            </remarks>   
        </member>
        <member name="P:Opc.Ua.Client.Session.DefaultSubscription">
            <summary>
            Gets or Sets the default subscription for the session.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Session.KeepAliveInterval">
            <summary>
            Gets or Sets how frequently the server is pinged to see if communication is still working.
            </summary>
            <remarks>
            This interval controls how much time elaspes before a communication error is detected.
            If everything is ok the KeepAlive event will be raised each time this period elapses.
            </remarks>
        </member>
        <member name="P:Opc.Ua.Client.Session.KeepAliveStopped">
            <summary>
            Returns true if the session is not receiving keep alives.
            </summary>
            <remarks>
            Set to true if the server does not respond for 2 times the KeepAliveInterval.
            Set to false is communication recovers.
            </remarks>
        </member>
        <member name="P:Opc.Ua.Client.Session.LastKeepAliveTime">
            <summary>
            Gets the time of the last keep alive.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Session.OutstandingRequestCount">
            <summary>
            Gets the number of outstanding publish or keep alive requests.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Session.DefunctRequestCount">
            <summary>
            Gets the number of outstanding publish or keep alive requests which appear to be missing.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Session.GoodPublishRequestCount">
            <summary>
            Gets the number of good outstanding publish requests.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.Create(Opc.Ua.ApplicationConfiguration,Opc.Ua.ConfiguredEndpoint,System.Boolean,System.String,System.UInt32,Opc.Ua.IUserIdentity,System.Collections.Generic.IList{System.String})">
            <summary>
            Creates a new communication session with a server by invoking the CreateSession service
            </summary>
            <param name="configuration">The configuration for the client application.</param>
            <param name="endpoint">The endpoint for the server.</param>
            <param name="updateBeforeConnect">If set to <c>true</c> the discovery endpoint is used to update the endpoint description before connecting.</param>
            <param name="sessionName">The name to assign to the session.</param>
            <param name="sessionTimeout">The timeout period for the session.</param>
            <param name="identity">The identity.</param>
            <param name="preferredLocales">The user identity to associate with the session.</param>
            <returns>The new session object</returns>
        </member>
        <member name="M:Opc.Ua.Client.Session.Create(Opc.Ua.ApplicationConfiguration,Opc.Ua.ConfiguredEndpoint,System.Boolean,System.Boolean,System.String,System.UInt32,Opc.Ua.IUserIdentity,System.Collections.Generic.IList{System.String})">
            <summary>
            Creates a new communication session with a server by invoking the CreateSession service
            </summary>
            <param name="configuration">The configuration for the client application.</param>
            <param name="endpoint">The endpoint for the server.</param>
            <param name="updateBeforeConnect">If set to <c>true</c> the discovery endpoint is used to update the endpoint description before connecting.</param>
            <param name="checkDomain">If set to <c>true</c> then the domain in the certificate must match the endpoint used.</param>
            <param name="sessionName">The name to assign to the session.</param>
            <param name="sessionTimeout">The timeout period for the session.</param>
            <param name="identity">The user identity to associate with the session.</param>
            <param name="preferredLocales">The preferred locales.</param>
            <returns>The new session object.</returns>
        </member>
        <member name="M:Opc.Ua.Client.Session.Create(Opc.Ua.ApplicationConfiguration,Opc.Ua.ITransportChannel,Opc.Ua.ConfiguredEndpoint,System.Security.Cryptography.X509Certificates.X509Certificate2,Opc.Ua.EndpointDescriptionCollection,Opc.Ua.StringCollection)">
            <summary>
            Creates a new session with a server using the specified channel by invoking the CreateSession service
            </summary>
            <param name="configuration">The configuration for the client application.</param>
            <param name="channel">The channel for the server.</param>
            <param name="endpoint">The endpoint for the server.</param>
            <param name="clientCertificate">The certificate to use for the client.</param>
            <param name="availableEndpoints">The list of available endpoints returned by server in GetEndpoints() response.</param>
            <param name="discoveryProfileUris">The value of profileUris used in GetEndpoints() request.</param>
            <returns></returns>
        </member>
        <member name="M:Opc.Ua.Client.Session.CreateChannelAsync(Opc.Ua.ApplicationConfiguration,Opc.Ua.ITransportWaitingConnection,Opc.Ua.ConfiguredEndpoint,System.Boolean,System.Boolean)">
            <summary>
            Creates a secure channel to the specified endpoint.
            </summary>
            <param name="configuration">The application configuration.</param>
            <param name="connection">The client endpoint for the reverse connect.</param>
            <param name="endpoint">A configured endpoint to connect to.</param> 
            <param name="updateBeforeConnect">Update configuration based on server prior connect.</param>
            <param name="checkDomain">Check that the certificate specifies a valid domain (computer) name.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> representing the asynchronous operation.</returns>
        </member>
        <member name="M:Opc.Ua.Client.Session.Create(Opc.Ua.ApplicationConfiguration,Opc.Ua.ITransportWaitingConnection,Opc.Ua.ConfiguredEndpoint,System.Boolean,System.Boolean,System.String,System.UInt32,Opc.Ua.IUserIdentity,System.Collections.Generic.IList{System.String})">
            <summary>
            Creates a new communication session with a server using a reverse connection.
            </summary>
            <param name="configuration">The configuration for the client application.</param>
            <param name="connection">The client endpoint for the reverse connect.</param>
            <param name="endpoint">The endpoint for the server.</param>
            <param name="updateBeforeConnect">If set to <c>true</c> the discovery endpoint is used to update the endpoint description before connecting.</param>
            <param name="checkDomain">If set to <c>true</c> then the domain in the certificate must match the endpoint used.</param>
            <param name="sessionName">The name to assign to the session.</param>
            <param name="sessionTimeout">The timeout period for the session.</param>
            <param name="identity">The user identity to associate with the session.</param>
            <param name="preferredLocales">The preferred locales.</param>
            <returns>The new session object.</returns>
        </member>
        <member name="M:Opc.Ua.Client.Session.Create(Opc.Ua.ApplicationConfiguration,Opc.Ua.Client.ReverseConnectManager,Opc.Ua.ConfiguredEndpoint,System.Boolean,System.Boolean,System.String,System.UInt32,Opc.Ua.IUserIdentity,System.Collections.Generic.IList{System.String},System.Threading.CancellationToken)">
            <summary>
            Creates a new communication session with a server using a reverse connect manager.
            </summary>
            <param name="configuration">The configuration for the client application.</param>
            <param name="reverseConnectManager">The reverse connect manager for the client connection.</param>
            <param name="endpoint">The endpoint for the server.</param>
            <param name="updateBeforeConnect">If set to <c>true</c> the discovery endpoint is used to update the endpoint description before connecting.</param>
            <param name="checkDomain">If set to <c>true</c> then the domain in the certificate must match the endpoint used.</param>
            <param name="sessionName">The name to assign to the session.</param>
            <param name="sessionTimeout">The timeout period for the session.</param>
            <param name="userIdentity">The user identity to associate with the session.</param>
            <param name="preferredLocales">The preferred locales.</param>
            <param name="ct">The cancellation token.</param>
            <returns>The new session object.</returns>
        </member>
        <member name="M:Opc.Ua.Client.Session.Recreate(Opc.Ua.Client.Session)">
            <summary>
            Recreates a session based on a specified template.
            </summary>
            <param name="template">The Session object to use as template</param>
            <returns>The new session object.</returns>
        </member>
        <member name="M:Opc.Ua.Client.Session.Recreate(Opc.Ua.Client.Session,Opc.Ua.ITransportWaitingConnection)">
            <summary>
            Recreates a session based on a specified template.
            </summary>
            <param name="template">The Session object to use as template</param>
            <param name="connection">The waiting reverse connection.</param>
            <returns>The new session object.</returns>
        </member>
        <member name="M:Opc.Ua.Client.Session.Recreate(Opc.Ua.Client.Session,Opc.Ua.ITransportChannel)">
            <summary>
            Recreates a session based on a specified template using the provided channel.
            </summary>
            <param name="template">The Session object to use as template</param>
            <param name="transportChannel">The waiting reverse connection.</param>
            <returns>The new session object.</returns>
        </member>
        <member name="E:Opc.Ua.Client.Session.RenewUserIdentity">
            <summary>
            Raised before a reconnect operation completes.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.Reconnect">
            <summary>
            Reconnects to the server after a network failure.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.Reconnect(Opc.Ua.ITransportWaitingConnection)">
            <summary>
            Reconnects to the server after a network failure using a waiting connection.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.Reconnect(Opc.Ua.ITransportWaitingConnection,Opc.Ua.ITransportChannel)">
            <summary>
            Reconnects to the server after a network failure using a waiting connection.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.Save(System.String)">
            <summary>
            Saves all the subscriptions of the session.
            </summary>
            <param name="filePath">The file path.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.Save(System.IO.Stream,System.Collections.Generic.IEnumerable{Opc.Ua.Client.Subscription})">
            <summary>
            Saves a set of subscriptions to a stream.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.Save(System.String,System.Collections.Generic.IEnumerable{Opc.Ua.Client.Subscription})">
            <summary>
            Saves a set of subscriptions to a file.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.Load(System.IO.Stream,System.Boolean)">
            <summary>
            Load the list of subscriptions saved in a stream.
            </summary>
            <param name="stream">The stream.</param>
            <param name="transferSubscriptions">Load the subscriptions for transfer after load.</param>
            <returns>The list of loaded subscriptions</returns>
        </member>
        <member name="M:Opc.Ua.Client.Session.Load(System.String,System.Boolean)">
            <summary>
            Load the list of subscriptions saved in a file.
            </summary>
            <param name="filePath">The file path.</param>
            <param name="transferSubscriptions">Load the subscriptions for transfer after load.</param>
            <returns>The list of loaded subscriptions</returns>
        </member>
        <member name="M:Opc.Ua.Client.Session.FetchNamespaceTables">
            <summary>
            Updates the local copy of the server's namespace uri and server uri tables.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.FetchOperationLimits">
            <summary>
            Fetch the operation limits of the server.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.FetchTypeTree(Opc.Ua.ExpandedNodeId)">
            <summary>
            Updates the cache with the type and its subtypes.
            </summary>
            <remarks>
            This method can be used to ensure the TypeTree is populated.
            </remarks>
        </member>
        <member name="M:Opc.Ua.Client.Session.FetchTypeTree(Opc.Ua.ExpandedNodeIdCollection)">
            <summary>
            Updates the cache with the types and its subtypes.
            </summary>
            <remarks>
            This method can be used to ensure the TypeTree is populated.
            </remarks>
        </member>
        <member name="M:Opc.Ua.Client.Session.ReadAvailableEncodings(Opc.Ua.NodeId)">
            <summary>
            Returns the available encodings for a node
            </summary>
            <param name="variableId">The variable node.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.FindDataDescription(Opc.Ua.NodeId)">
            <summary>
            Returns the data description for the encoding.
            </summary>
            <param name="encodingId">The encoding Id.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.FindDataDictionary(Opc.Ua.NodeId)">
            <summary>
             Returns the data dictionary that contains the description.
            </summary>
            <param name="descriptionId">The description id.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.LoadDataDictionary(Opc.Ua.ReferenceDescription,System.Boolean)">
            <summary>
             Returns the data dictionary that contains the description.
            </summary>
            <param name="dictionaryNode">The dictionary id.</param>
            <param name="forceReload"></param>
            <returns>The dictionary.</returns>
        </member>
        <member name="M:Opc.Ua.Client.Session.LoadDataTypeSystem(Opc.Ua.NodeId)">
            <summary>
            Loads all dictionaries of the OPC binary or Xml schema type system.
            </summary>
            <param name="dataTypeSystem">The type system.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.ReadNodes(System.Collections.Generic.IList{Opc.Ua.NodeId},Opc.Ua.NodeClass,System.Collections.Generic.IList{Opc.Ua.Node}@,System.Collections.Generic.IList{Opc.Ua.ServiceResult}@,System.Boolean)">
            <summary>
            Reads the values for the node attributes and returns a node object collection.
            </summary>
            <remarks>
            If the nodeclass for the nodes in nodeIdCollection is already known,
            and passed as nodeClass, reads only values of required attributes.
            Otherwise NodeClass.Unspecified should be used.
            </remarks>
            <param name="nodeIds">The nodeId collection to read.</param>
            <param name="nodeClass">The nodeClass of all nodes in the collection. Set to <c>NodeClass.Unspecified</c> if the nodeclass is unknown.</param>
            <param name="nodeCollection">The node collection that is created from attributes read from the server.</param>
            <param name="errors">The errors that occured reading the nodes.</param>
            <param name="optionalAttributes">Set to <c>true</c> if optional attributes should not be omitted.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.ReadNodes(System.Collections.Generic.IList{Opc.Ua.NodeId},System.Collections.Generic.IList{Opc.Ua.Node}@,System.Collections.Generic.IList{Opc.Ua.ServiceResult}@,System.Boolean)">
            <summary>
            Reads the values for the node attributes and returns a node object.
            Reads the nodeclass of the nodeIds, then reads
            the values for the node attributes and returns a node object collection.
            </summary>
            <param name="nodeIds">The nodeId collection.</param>
            <param name="nodeCollection">The node collection read from the server.</param>
            <param name="errors">The errors occured reading the nodes.</param>
            <param name="optionalAttributes">Set to <c>true</c> if optional attributes should not be omitted.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.ReadNode(Opc.Ua.NodeId)">
            <summary>
            Reads the values for the node attributes and returns a node object.
            </summary>
            <param name="nodeId">The nodeId.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.ReadNode(Opc.Ua.NodeId,Opc.Ua.NodeClass,System.Boolean)">
            <summary>
            Reads the values for the node attributes and returns a node object.
            </summary>
            <remarks>
            If the nodeclass is known, only the supported attribute values are read.
            </remarks>
            <param name="nodeId">The nodeId.</param>
            <param name="nodeClass">The nodeclass of the node to read.</param>
            <param name="optionalAttributes">Read optional attributes.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.ReadValue(Opc.Ua.NodeId)">
            <summary>
            Reads the value for a node.
            </summary>
            <param name="nodeId">The node Id.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.ReadValues(System.Collections.Generic.IList{Opc.Ua.NodeId},Opc.Ua.DataValueCollection@,System.Collections.Generic.IList{Opc.Ua.ServiceResult}@)">
            <summary>
            Reads the values for a node collection. Returns diagnostic errors.
            </summary>
            <param name="nodeIds">The node Id.</param>
            <param name="values">The data values read from the server.</param>
            <param name="errors">The errors reported by the server.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.ReadValue(Opc.Ua.NodeId,System.Type)">
            <summary>
            Reads the value for a node an checks that it is the specified type.
            </summary>
            <param name="nodeId">The node id.</param>
            <param name="expectedType">The expected type.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.FetchReferences(Opc.Ua.NodeId)">
            <summary>
            Fetches all references for the specified node.
            </summary>
            <param name="nodeId">The node id.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.FetchReferences(System.Collections.Generic.IList{Opc.Ua.NodeId},System.Collections.Generic.IList{Opc.Ua.ReferenceDescriptionCollection}@,System.Collections.Generic.IList{Opc.Ua.ServiceResult}@)">
            <summary>
            Fetches all references for the specified nodes.
            </summary>
            <param name="nodeIds">The node id collection.</param>
            <param name="referenceDescriptions">A list of reference collections.</param>
            <param name="errors">The errors reported by the server.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.Open(System.String,Opc.Ua.IUserIdentity)">
            <summary>
            Establishes a session with the server.
            </summary>
            <param name="sessionName">The name to assign to the session.</param>
            <param name="identity">The user identity.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.Open(System.String,System.UInt32,Opc.Ua.IUserIdentity,System.Collections.Generic.IList{System.String})">
            <summary>
            Establishes a session with the server.
            </summary>
            <param name="sessionName">The name to assign to the session.</param>
            <param name="sessionTimeout">The session timeout.</param>
            <param name="identity">The user identity.</param>
            <param name="preferredLocales">The list of preferred locales.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.Open(System.String,System.UInt32,Opc.Ua.IUserIdentity,System.Collections.Generic.IList{System.String},System.Boolean)">
            <summary>
            Establishes a session with the server.
            </summary>
            <param name="sessionName">The name to assign to the session.</param>
            <param name="sessionTimeout">The session timeout.</param>
            <param name="identity">The user identity.</param>
            <param name="preferredLocales">The list of preferred locales.</param>
            <param name="checkDomain">If set to <c>true</c> then the domain in the certificate must match the endpoint used.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.ChangePreferredLocales(Opc.Ua.StringCollection)">
            <summary>
            Updates the preferred locales used for the session.
            </summary>
            <param name="preferredLocales">The preferred locales.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.UpdateSession(Opc.Ua.IUserIdentity,Opc.Ua.StringCollection)">
            <summary>
            Updates the user identity and/or locales used for the session.
            </summary>
            <param name="identity">The user identity.</param>
            <param name="preferredLocales">The preferred locales.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.FindComponentIds(Opc.Ua.NodeId,System.Collections.Generic.IList{System.String},Opc.Ua.NodeIdCollection@,System.Collections.Generic.List{Opc.Ua.ServiceResult}@)">
            <summary>
            Finds the NodeIds for the components for an instance.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.ReadValues(System.Collections.Generic.IList{Opc.Ua.NodeId},System.Collections.Generic.IList{System.Type},System.Collections.Generic.List{System.Object}@,System.Collections.Generic.List{Opc.Ua.ServiceResult}@)">
            <summary>
            Reads the values for a set of variables.
            </summary>
            <param name="variableIds">The variable ids.</param>
            <param name="expectedTypes">The expected types.</param>
            <param name="values">The list of returned values.</param>
            <param name="errors">The list of returned errors.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.ReadDisplayName(System.Collections.Generic.IList{Opc.Ua.NodeId},System.Collections.Generic.IList{System.String}@,System.Collections.Generic.IList{Opc.Ua.ServiceResult}@)">
            <summary>
            Reads the display name for a set of Nodes.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.Close">
            <summary>
            Disconnects from the server and frees any network resources.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.Close(System.Boolean)">
            <summary>
            Close the session with the server and optionally closes the channel.
            </summary>
            <param name="closeChannel"></param>
            <returns></returns>
        </member>
        <member name="M:Opc.Ua.Client.Session.Close(System.Int32)">
            <summary>
            Disconnects from the server and frees any network resources with the specified timeout.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.Close(System.Int32,System.Boolean)">
            <summary>
            Disconnects from the server and frees any network resources with the specified timeout.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.AddSubscription(Opc.Ua.Client.Subscription)">
            <summary>
            Adds a subscription to the session.
            </summary>
            <param name="subscription">The subscription to add.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.RemoveSubscription(Opc.Ua.Client.Subscription)">
            <summary>
            Removes a subscription from the session.
            </summary>
            <param name="subscription">The subscription to remove.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.RemoveSubscriptions(System.Collections.Generic.IEnumerable{Opc.Ua.Client.Subscription})">
            <summary>
            Removes a list of subscriptions from the session.
            </summary>
            <param name="subscriptions">The list of subscriptions to remove.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.RemoveTransferredSubscription(Opc.Ua.Client.Subscription)">
            <summary>
            Removes a transferred subscription from the session.
            Called by the session to which the subscription
            is transferred to obtain ownership. Internal.
            </summary>
            <param name="subscription">The subscription to remove.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.TransferSubscriptions(Opc.Ua.Client.SubscriptionCollection,System.Boolean)">
            <summary>
            Transfers a list of Subscriptions from another session.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.Browse(Opc.Ua.RequestHeader,Opc.Ua.ViewDescription,Opc.Ua.NodeId,System.UInt32,Opc.Ua.BrowseDirection,Opc.Ua.NodeId,System.Boolean,System.UInt32,System.Byte[]@,Opc.Ua.ReferenceDescriptionCollection@)">
            <summary>
            Invokes the Browse service.
            </summary>
            <param name="requestHeader">The request header.</param>
            <param name="view">The view to browse.</param>
            <param name="nodeToBrowse">The node to browse.</param>
            <param name="maxResultsToReturn">The maximum number of returned values.</param>
            <param name="browseDirection">The browse direction.</param>
            <param name="referenceTypeId">The reference type id.</param>
            <param name="includeSubtypes">If set to <c>true</c> the subtypes of the ReferenceType will be included in the browse.</param>
            <param name="nodeClassMask">The node class mask.</param>
            <param name="continuationPoint">The continuation point.</param>
            <param name="references">The list of node references.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.Browse(Opc.Ua.RequestHeader,Opc.Ua.ViewDescription,System.Collections.Generic.IList{Opc.Ua.NodeId},System.UInt32,Opc.Ua.BrowseDirection,Opc.Ua.NodeId,System.Boolean,System.UInt32,Opc.Ua.ByteStringCollection@,System.Collections.Generic.IList{Opc.Ua.ReferenceDescriptionCollection}@,System.Collections.Generic.IList{Opc.Ua.ServiceResult}@)">
            <summary>
            Invokes the Browse service. Handles multiple nodes for browse request.
            </summary>
            <param name="requestHeader">The request header.</param>
            <param name="view">The view to browse.</param>
            <param name="nodesToBrowse">The nodes to browse.</param>
            <param name="maxResultsToReturn">The maximum number of returned values.</param>
            <param name="browseDirection">The browse direction.</param>
            <param name="referenceTypeId">The reference type id.</param>
            <param name="includeSubtypes">If set to <c>true</c> the subtypes of the ReferenceType will be included in the browse.</param>
            <param name="nodeClassMask">The node class mask.</param>
            <param name="continuationPoints">The continuation points per browse.</param>
            <param name="referencesList">The list of node references collections.</param>
            <param name="errors"></param>
        </member>
        <member name="M:Opc.Ua.Client.Session.BeginBrowse(Opc.Ua.RequestHeader,Opc.Ua.ViewDescription,Opc.Ua.NodeId,System.UInt32,Opc.Ua.BrowseDirection,Opc.Ua.NodeId,System.Boolean,System.UInt32,System.AsyncCallback,System.Object)">
            <summary>
            Begins an asynchronous invocation of the Browse service.
            </summary>
            <param name="requestHeader">The request header.</param>
            <param name="view">The view to browse.</param>
            <param name="nodeToBrowse">The node to browse.</param>
            <param name="maxResultsToReturn">The maximum number of returned values..</param>
            <param name="browseDirection">The browse direction.</param>
            <param name="referenceTypeId">The reference type id.</param>
            <param name="includeSubtypes">If set to <c>true</c> the subtypes of the ReferenceType will be included in the browse.</param>
            <param name="nodeClassMask">The node class mask.</param>
            <param name="callback">The callback.</param>
            <param name="asyncState"></param>
        </member>
        <member name="M:Opc.Ua.Client.Session.EndBrowse(System.IAsyncResult,System.Byte[]@,Opc.Ua.ReferenceDescriptionCollection@)">
            <summary>
            Finishes an asynchronous invocation of the Browse service.
            </summary>
            <param name="result">The result.</param>
            <param name="continuationPoint">The continuation point.</param>
            <param name="references">The list of node references.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.BrowseNext(Opc.Ua.RequestHeader,System.Boolean,System.Byte[],System.Byte[]@,Opc.Ua.ReferenceDescriptionCollection@)">
            <summary>
            Invokes the BrowseNext service.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.BrowseNext(Opc.Ua.RequestHeader,System.Boolean,Opc.Ua.ByteStringCollection,Opc.Ua.ByteStringCollection@,System.Collections.Generic.IList{Opc.Ua.ReferenceDescriptionCollection}@,System.Collections.Generic.IList{Opc.Ua.ServiceResult}@)">
            <summary>
            Invokes the BrowseNext service. Handles multiple continuation points.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.BeginBrowseNext(Opc.Ua.RequestHeader,System.Boolean,System.Byte[],System.AsyncCallback,System.Object)">
            <summary>
            Begins an asynchronous invocation of the BrowseNext service.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.EndBrowseNext(System.IAsyncResult,System.Byte[]@,Opc.Ua.ReferenceDescriptionCollection@)">
            <summary>
            Finishes an asynchronous invocation of the BrowseNext service.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.Call(Opc.Ua.NodeId,Opc.Ua.NodeId,System.Object[])">
            <summary>
            Calls the specified method and returns the output arguments.
            </summary>
            <param name="objectId">The NodeId of the object that provides the method.</param>
            <param name="methodId">The NodeId of the method to call.</param>
            <param name="args">The input arguments.</param>
            <returns>The list of output argument values.</returns>
        </member>
        <member name="M:Opc.Ua.Client.Session.GetSoftwareCertificates">
            <summary>
            Returns the software certificates assigned to the application.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.OnApplicationCertificateError(System.Byte[],Opc.Ua.ServiceResult)">
            <summary>
            Handles an error when validating the application instance certificate provided by the server.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.OnSoftwareCertificateError(Opc.Ua.SignedSoftwareCertificate,Opc.Ua.ServiceResult)">
            <summary>
            Handles an error when validating software certificates provided by the server.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.ValidateSoftwareCertificates(System.Collections.Generic.List{Opc.Ua.SoftwareCertificate})">
            <summary>
            Inspects the software certificates provided by the server.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.StartKeepAliveTimer">
            <summary>
            Starts a timer to check that the connection to the server is still available.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.RemoveRequest(System.IAsyncResult,System.UInt32,System.UInt32)">
            <summary>
            Removes a completed async request.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.AsyncRequestStarted(System.IAsyncResult,System.UInt32,System.UInt32)">
            <summary>
            Adds a new async request.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.AsyncRequestCompleted(System.IAsyncResult,System.UInt32,System.UInt32)">
            <summary>
            Removes a completed async request.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.OnKeepAlive(System.Object)">
            <summary>
            Sends a keep alive by reading from the server.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.OnKeepAliveComplete(System.IAsyncResult)">
            <summary>
            Checks if a notification has arrived. Sends a publish if it has not.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.OnKeepAlive(Opc.Ua.ServerState,System.DateTime)">
            <summary>
            Called when the server returns a keep alive response.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.OnKeepAliveError(Opc.Ua.ServiceResult)">
            <summary>
            Called when a error occurs during a keep alive.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.PrepareSubscriptionsToDelete(System.Collections.Generic.IEnumerable{Opc.Ua.Client.Subscription},System.Collections.Generic.IList{Opc.Ua.Client.Subscription})">
            <summary>
            Prepare a list of subscriptions to delete.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.CreateNodeClassAttributesReadNodesRequest(System.Collections.Generic.IList{Opc.Ua.NodeId},Opc.Ua.NodeClass,Opc.Ua.ReadValueIdCollection,System.Collections.Generic.IList{System.Collections.Generic.IDictionary{System.UInt32,Opc.Ua.DataValue}},System.Collections.Generic.IList{Opc.Ua.Node},System.Boolean)">
            <summary>
            Creates a read request with attributes determined by the NodeClass.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.CreateAttributesReadNodesRequest(Opc.Ua.ResponseHeader,Opc.Ua.ReadValueIdCollection,Opc.Ua.DataValueCollection,Opc.Ua.DiagnosticInfoCollection,Opc.Ua.ReadValueIdCollection,System.Collections.Generic.IList{System.Collections.Generic.IDictionary{System.UInt32,Opc.Ua.DataValue}},System.Collections.Generic.IList{Opc.Ua.Node},System.Collections.Generic.IList{Opc.Ua.ServiceResult},System.Boolean)">
            <summary>
            Creates a read request with attributes determined by the NodeClass.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.ProcessAttributesReadNodesResponse(Opc.Ua.ResponseHeader,Opc.Ua.ReadValueIdCollection,System.Collections.Generic.IList{System.Collections.Generic.IDictionary{System.UInt32,Opc.Ua.DataValue}},Opc.Ua.DataValueCollection,Opc.Ua.DiagnosticInfoCollection,System.Collections.Generic.IList{Opc.Ua.Node},System.Collections.Generic.IList{Opc.Ua.ServiceResult})">
            <summary>
            Builds the node collection results based on the attribute values of the read response. 
            </summary>
            <param name="attributesToRead">The collection of all attributes to read passed in the read request.</param>
            <param name="attributesPerNodeId">The attributes requested per NodeId</param>
            <param name="values">The attribute values returned by the read request.</param>
            <param name="diagnosticInfos">The diagnostic info returned by the read request.</param>
            <param name="responseHeader">The response header of the read request.</param>
            <param name="nodeCollection">The node collection which holds the results.</param>
            <param name="errors">The service results for each node.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.ProcessReadResponse(Opc.Ua.ResponseHeader,System.Collections.Generic.IDictionary{System.UInt32,Opc.Ua.DataValue},Opc.Ua.ReadValueIdCollection,Opc.Ua.DataValueCollection,Opc.Ua.DiagnosticInfoCollection)">
            <summary>
            Creates a Node based on the read response.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.CreateAttributes(Opc.Ua.NodeClass,System.Boolean)">
            <summary>
            Create a dictionary of attributes to read for a nodeclass.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.BeginPublish(System.Int32)">
            <summary>
            Sends an additional publish request.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.OnPublishComplete(System.IAsyncResult)">
            <summary>
            Completes an asynchronous publish operation.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.Republish(System.UInt32,System.UInt32)">
            <summary>
            Sends a republish request.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.ProcessPublishResponse(Opc.Ua.ResponseHeader,System.UInt32,Opc.Ua.UInt32Collection,System.Boolean,Opc.Ua.NotificationMessage)">
            <summary>
            Processes the response from a publish request.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.RecreateSubscriptions(System.Collections.Generic.IEnumerable{Opc.Ua.Client.Subscription})">
            <summary>
            Recreate the subscriptions in a reconnected session.
            Uses Transfer service if <see cref="P:Opc.Ua.Client.Session.TransferSubscriptionsOnReconnect"/> is set to <c>true</c>.
            </summary>
            <param name="subscriptionsTemplate">The template for the subscriptions.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.OnRaisePublishNotification(System.Object)">
            <summary>
            Raises an event indicating that publish has returned a notification.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.DeleteSubscription(System.UInt32)">
            <summary>
            Invokes a DeleteSubscriptions call for the specified subscriptionId.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.LoadCertificate(Opc.Ua.ApplicationConfiguration)">
            <summary>
            Load certificate for connection.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.LoadCertificateChain(Opc.Ua.ApplicationConfiguration,System.Security.Cryptography.X509Certificates.X509Certificate2)">
            <summary>
            Load certificate chain for connection.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.HasAnyContinuationPoint(Opc.Ua.ByteStringCollection)">
            <summary>
            Helper to determine if a continuation point needs to be processed.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.BelowPublishRequestLimit(System.Int32)">
            <summary>
            Returns true if the Bad_TooManyPublishRequests limit
            has not been reached.
            </summary>
            <param name="requestCount">The actual number of publish requests.</param>
            <returns>If the publish request limit was reached.</returns>
        </member>
        <member name="F:Opc.Ua.Client.Session.m_sessionTimeout">
            <summary>
            The period for which the server will maintain the session if there is no communication from the client.
            </summary>
        </member>
        <member name="F:Opc.Ua.Client.Session.m_preferredLocales">
            <summary>
            The locales that the server should use when returning localized text.
            </summary>
        </member>
        <member name="F:Opc.Ua.Client.Session.m_configuration">
            <summary>
            The Application Configuration.
            </summary>
        </member>
        <member name="F:Opc.Ua.Client.Session.m_endpoint">
            <summary>
            The endpoint used to connect to the server.
            </summary>
        </member>
        <member name="F:Opc.Ua.Client.Session.m_instanceCertificate">
            <summary>
            The Instance Certificate.
            </summary>
        </member>
        <member name="F:Opc.Ua.Client.Session.m_instanceCertificateChain">
            <summary>
            The Instance Certificate Chain.
            </summary>
        </member>
        <member name="F:Opc.Ua.Client.Session.m_checkDomain">
            <summary>
            If set to<c>true</c> then the domain in the certificate must match the endpoint used.
            </summary>
        </member>
        <member name="F:Opc.Ua.Client.Session.m_sessionName">
            <summary>
            The name assigned to the session.
            </summary>
        </member>
        <member name="F:Opc.Ua.Client.Session.m_identity">
            <summary>
            The user identity currently used for the session.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.RemoveSubscriptionAsync(Opc.Ua.Client.Subscription)">
            <summary>
            Removes a subscription from the session.
            </summary>
            <param name="subscription">The subscription to remove.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.RemoveSubscriptionsAsync(System.Collections.Generic.IEnumerable{Opc.Ua.Client.Subscription})">
            <summary>
            Removes a list of subscriptions from the sessiont.
            </summary>
            <param name="subscriptions">The list of subscriptions to remove.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.ReadNodesAsync(System.Collections.Generic.IList{Opc.Ua.NodeId},Opc.Ua.NodeClass,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Reads the values for the node attributes and returns a node object collection.
            </summary>
            <remarks>
            If the nodeclass for the nodes in nodeIdCollection is already known
            and passed as nodeClass, reads only values of required attributes.
            Otherwise NodeClass.Unspecified should be used.
            </remarks>
            <param name="nodeIds">The nodeId collection to read.</param>
            <param name="nodeClass">The nodeClass of all nodes in the collection. Set to <c>NodeClass.Unspecified</c> if the nodeclass is unknown.</param>
            <param name="optionalAttributes">Set to <c>true</c> if optional attributes should not be omitted.</param>
            <param name="ct">The cancellation token.</param>
            <returns>The node collection and associated errors.</returns>
        </member>
        <member name="M:Opc.Ua.Client.Session.ReadNodesAsync(System.Collections.Generic.IList{Opc.Ua.NodeId},System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Reads the values for the node attributes and returns a node object collection.
            Reads the nodeclass of the nodeIds, then reads
            the values for the node attributes and returns a node collection.
            </summary>
            <param name="nodeIds">The nodeId collection.</param>
            <param name="optionalAttributes">If optional attributes to read.</param>
            <param name="ct">The cancellation token.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.ReadNodeAsync(Opc.Ua.NodeId,System.Threading.CancellationToken)">
            <summary>
            Reads the values for the node attributes and returns a node object.
            </summary>
            <param name="nodeId">The nodeId.</param>
            <param name="ct">The cancellation token for the request.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.ReadNodeAsync(Opc.Ua.NodeId,Opc.Ua.NodeClass,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Reads the values for the node attributes and returns a node object.
            </summary>
            <remarks>
            If the nodeclass is known, only the supported attribute values are read.
            </remarks>
            <param name="nodeId">The nodeId.</param>
            <param name="nodeClass">The nodeclass of the node to read.</param>
            <param name="optionalAttributes">Read optional attributes.</param>
            <param name="ct">The cancellation token for the request.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.ReadValueAsync(Opc.Ua.NodeId,System.Threading.CancellationToken)">
            <summary>
            Reads the value for a node.
            </summary>
            <param name="nodeId">The node Id.</param>
            <param name="ct">The cancellation token for the request.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.ReadValuesAsync(System.Collections.Generic.IList{Opc.Ua.NodeId},System.Threading.CancellationToken)">
            <summary>
            Reads the values for a node collection. Returns diagnostic errors.
            </summary>
            <param name="nodeIds">The node Id.</param>
            <param name="ct">The cancellation token for the request.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.CloseAsync(System.Threading.CancellationToken)">
            <summary>
            Disconnects from the server and frees any network resources.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.CloseAsync(System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Close the session with the server and optionally closes the channel.
            </summary>
            <param name="closeChannel"></param>
            <param name="ct">The cancellation token.</param>
        </member>
        <member name="M:Opc.Ua.Client.Session.CloseAsync(System.Int32,System.Threading.CancellationToken)">
            <summary>
            Disconnects from the server and frees any network resources with the specified timeout.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.CloseAsync(System.Int32,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Disconnects from the server and frees any network resources with the specified timeout.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Session.CreateSession(Opc.Ua.RequestHeader,Opc.Ua.ApplicationDescription,System.String,System.String,System.String,System.Byte[],System.Byte[],System.Double,System.UInt32,Opc.Ua.NodeId@,Opc.Ua.NodeId@,System.Double@,System.Byte[]@,System.Byte[]@,Opc.Ua.EndpointDescriptionCollection@,Opc.Ua.SignedSoftwareCertificateCollection@,Opc.Ua.SignatureData@,System.UInt32@)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.Session.CreateSessionAsync(Opc.Ua.RequestHeader,Opc.Ua.ApplicationDescription,System.String,System.String,System.String,System.Byte[],System.Byte[],System.Double,System.UInt32,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.Session.CloseSession(Opc.Ua.RequestHeader,System.Boolean)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.Client.Session.CloseSessionAsync(Opc.Ua.RequestHeader,System.Boolean,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="T:Opc.Ua.Client.KeepAliveEventArgs">
            <summary>
            The event arguments provided when a keep alive response arrives.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.KeepAliveEventArgs.#ctor(Opc.Ua.ServiceResult,Opc.Ua.ServerState,System.DateTime)">
            <summary>
            Creates a new instance.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.KeepAliveEventArgs.Status">
            <summary>
            Gets the status associated with the keep alive operation.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.KeepAliveEventArgs.CurrentState">
            <summary>
            Gets the current server state.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.KeepAliveEventArgs.CurrentTime">
            <summary>
            Gets the current server time.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.KeepAliveEventArgs.CancelKeepAlive">
            <summary>
            Gets or sets a flag indicating whether the session should send another keep alive.
            </summary>
        </member>
        <member name="T:Opc.Ua.Client.NotificationEventArgs">
            <summary>
            Represents the event arguments provided when a new notification message arrives.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.NotificationEventArgs.#ctor(Opc.Ua.Client.Subscription,Opc.Ua.NotificationMessage,System.Collections.Generic.IList{System.String})">
            <summary>
            Creates a new instance.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.NotificationEventArgs.Subscription">
            <summary>
            Gets the subscription that the notification applies to.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.NotificationEventArgs.NotificationMessage">
            <summary>
            Gets the notification message.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.NotificationEventArgs.StringTable">
            <summary>
            Gets the string table returned with the notification message.
            </summary>
        </member>
        <member name="T:Opc.Ua.Client.PublishErrorEventArgs">
            <summary>
            Represents the event arguments provided when a publish error occurs.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.PublishErrorEventArgs.#ctor(Opc.Ua.ServiceResult)">
            <summary>
            Creates a new instance.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.PublishErrorEventArgs.#ctor(Opc.Ua.ServiceResult,System.UInt32,System.UInt32)">
            <summary>
            Creates a new instance.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.PublishErrorEventArgs.Status">
            <summary>
            Gets the status associated with the keep alive operation.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.PublishErrorEventArgs.SubscriptionId">
            <summary>
            Gets the subscription with the message that could not be republished.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.PublishErrorEventArgs.SequenceNumber">
            <summary>
            Gets the sequence number for the message that could not be republished.
            </summary>
        </member>
        <member name="T:Opc.Ua.Client.SessionReconnectHandler">
            <summary>
            Attempts to reconnect to the server.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.SessionReconnectHandler.#ctor(System.Boolean)">
            <summary>
            Create a reconnect handler.
            </summary>
            <param name="reconnectAbort">Set to <c>true</c> to allow reconnect abort if keep alive recovered.</param>
        </member>
        <member name="M:Opc.Ua.Client.SessionReconnectHandler.Dispose">
            <summary>
            Frees any unmanaged resources.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.SessionReconnectHandler.Dispose(System.Boolean)">
            <summary>
            An overrideable version of the Dispose.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.SessionReconnectHandler.Session">
            <summary>
            Gets the session managed by the handler.
            </summary>
            <value>The session.</value>
        </member>
        <member name="M:Opc.Ua.Client.SessionReconnectHandler.BeginReconnect(Opc.Ua.Client.ISession,System.Int32,System.EventHandler)">
            <summary>
            Begins the reconnect process.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.SessionReconnectHandler.BeginReconnect(Opc.Ua.Client.ISession,Opc.Ua.Client.ReverseConnectManager,System.Int32,System.EventHandler)">
            <summary>
            Begins the reconnect process using a reverse connection.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.SessionReconnectHandler.OnReconnect(System.Object)">
            <summary>
            Called when the reconnect timer expires.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.SessionReconnectHandler.DoReconnect">
            <summary>
            Reconnects to the server.
            </summary>
        </member>
        <member name="T:Opc.Ua.Client.Subscription">
            <summary>
            A subscription.
            </summary>
            <summary>
            The async interface for a subscription.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.#ctor">
            <summary>
            Creates a empty object.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.#ctor(Opc.Ua.Client.Subscription)">
            <summary>
            Initializes the subscription from a template.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.#ctor(Opc.Ua.Client.Subscription,System.Boolean)">
            <summary>
            Initializes the subscription from a template.
            </summary>
            <param name="template">The template.</param>
            <param name="copyEventHandlers">if set to <c>true</c> the event handlers are copied.</param>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.Initialize(System.Runtime.Serialization.StreamingContext)">
            <summary>
            Called by the .NET framework during deserialization.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.Initialize">
            <summary>
            Sets the private members to default values.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.Dispose">
            <summary>
            Frees any unmanaged resources.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.Dispose(System.Boolean)">
            <summary>
            An overrideable version of the Dispose.
            </summary>
        </member>
        <member name="E:Opc.Ua.Client.Subscription.StateChanged">
            <summary>
            Raised to indicate that the state of the subscription has changed.
            </summary>
        </member>
        <member name="E:Opc.Ua.Client.Subscription.PublishStatusChanged">
            <summary>
            Raised to indicate the publishing state for the subscription has stopped or resumed (see PublishingStopped property).
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.DisplayName">
            <summary>
            A display name for the subscription.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.PublishingInterval">
            <summary>
            The publishing interval.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.KeepAliveCount">
            <summary>
            The keep alive count.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.LifetimeCount">
            <summary>
            The life time of of the subscription in counts of
            publish interval.
            LifetimeCount shall be at least 3*KeepAliveCount.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.MaxNotificationsPerPublish">
            <summary>
            The maximum number of notifications per publish request.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.PublishingEnabled">
            <summary>
            Whether publishing is enabled.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.Priority">
            <summary>
            The priority assigned to subscription.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.TimestampsToReturn">
            <summary>
            The timestamps to return with the notification messages.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.MaxMessageCount">
            <summary>
            The maximum number of messages to keep in the internal cache.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.DefaultItem">
            <summary>
            The default monitored item.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.MinLifetimeInterval">
            <summary>
            The minimum lifetime for subscriptions in milliseconds.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.DisableMonitoredItemCache">
            <summary>
            Gets or sets a value indicating whether the notifications are cached within the monitored items.
            </summary>
            <value>
            	<c>true</c> if monitored item cache is disabled; otherwise, <c>false</c>.
            </value>
            <remarks>
            Applications must process the Session.Notication event if this is set to true.
            This flag improves performance by eliminating the processing involved in updating the cache.
            </remarks>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.SequentialPublishing">
            <summary>
            Gets or sets the behavior of waiting for sequential order in handling incoming messages.
            </summary>
            <value>
            <c>true</c> if incoming messages are handled sequentially; <c>false</c> otherwise.
            </value>
            <remarks>
            Setting <see cref="P:Opc.Ua.Client.Subscription.SequentialPublishing"/> to <c>true</c> means incoming messages are processed in
            a "single-threaded" manner and callbacks will not be invoked in parallel.
            </remarks>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.RepublishAfterTransfer">
            <summary>
            If the available sequence numbers of a subscription
            are republished or acknoledged after a transfer. 
            </summary>
            <remarks>
            Default <c>false</c>, set to <c>true</c> if no data loss is important
            and available publish requests (sequence numbers) that were never acknoledged should be
            recovered with a republish. The setting is used after a subscription transfer.
            </remarks>   
        </member>
        <member name="P:Opc.Ua.Client.Subscription.TransferId">
            <summary>
            The unique identifier assigned by the server which can be used to transfer a session.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.FastDataChangeCallback">
            <summary>
            Gets or sets the fast data change callback.
            </summary>
            <value>The fast data change callback.</value>
            <remarks>
            Only one callback is allowed at a time but it is more efficient to call than an event.
            </remarks>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.FastEventCallback">
            <summary>
            Gets or sets the fast event callback.
            </summary>
            <value>The fast event callback.</value>
            <remarks>
            Only one callback is allowed at a time but it is more efficient to call than an event.
            </remarks>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.MonitoredItems">
            <summary>
            The items to monitor.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.SavedMonitoredItems">
            <summary>
            Allows the list of monitored items to be saved/restored when the object is serialized.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.ChangesPending">
            <summary>
            Returns true if the subscription has changes that need to be applied.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.MonitoredItemCount">
            <summary>
            Returns the number of monitored items.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.Session">
            <summary>
            The session that owns the subscription item.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.Handle">
            <summary>
            A local handle assigned to the subscription
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.Id">
            <summary>
            The unique identifier assigned by the server.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.Created">
            <summary>
            Whether the subscription has been created on the server.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.CurrentPublishingInterval">
            <summary>
            The current publishing interval.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.CurrentKeepAliveCount">
            <summary>
            The current keep alive count.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.CurrentLifetimeCount">
            <summary>
            The current lifetime count.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.CurrentPublishingEnabled">
            <summary>
            Whether publishing is currently enabled.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.CurrentPriority">
            <summary>
            The priority assigned to subscription when it was created.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.PublishTime">
            <summary>
            The when that the last notification received was published.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.LastNotificationTime">
            <summary>
            The when that the last notification was received.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.SequenceNumber">
            <summary>
            The sequence number assigned to the last notification message.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.NotificationCount">
            <summary>
            The number of notifications contained in the last notification message.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.LastNotification">
            <summary>
            The last notification received from the server.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.Notifications">
            <summary>
            The cached notifications.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.AvailableSequenceNumbers">
            <summary>
            The sequence numbers that are available for republish requests.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.ChangesCompleted">
            <summary>
            Sends a notification that the state of the subscription has changed.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.PublishingStopped">
            <summary>
            Returns true if the subscription is not receiving publishes.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.Create">
            <summary>
            Creates a subscription on the server and adds all monitored items.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.Transfer(Opc.Ua.Client.ISession,System.UInt32,Opc.Ua.UInt32Collection)">
            <summary>
            Called after the subscription was transferred.
            </summary>
            <param name="session">The session to which the subscription is transferred.</param>
            <param name="id">Id of the transferred subscription.</param>
            <param name="availableSequenceNumbers">The available sequence numbers on the server.</param>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.Delete(System.Boolean)">
            <summary>
            Deletes a subscription on the server.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.Modify">
            <summary>
            Modifies a subscription on the server.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.SetPublishingMode(System.Boolean)">
            <summary>
            Changes the publishing enabled state for the subscription.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.Republish(System.UInt32)">
            <summary>
            Republishes the specified notification message.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.ApplyChanges">
            <summary>
            Applies any changes to the subscription items.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.ResolveItemNodeIds">
            <summary>
            Resolves all relative paths to nodes on the server.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.CreateItems">
            <summary>
            Creates all items that have not already been created.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.ModifyItems">
            <summary>
            Modies all items that have been changed.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.DeleteItems">
            <summary>
            Deletes all items that have been marked for deletion.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.SetMonitoringMode(Opc.Ua.MonitoringMode,System.Collections.Generic.IList{Opc.Ua.Client.MonitoredItem})">
            <summary>
            Set monitoring mode of items.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.SaveMessageInCache(System.Collections.Generic.IList{System.UInt32},Opc.Ua.NotificationMessage,System.Collections.Generic.IList{System.String})">
            <summary>
            Adds the notification message to internal cache.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.Subscription.OutstandingMessageWorkers">
            <summary>
            Get the number of outstanding message workers
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.AddItem(Opc.Ua.Client.MonitoredItem)">
            <summary>
            Adds an item to the subscription.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.AddItems(System.Collections.Generic.IEnumerable{Opc.Ua.Client.MonitoredItem})">
            <summary>
            Adds an item to the subscription.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.RemoveItem(Opc.Ua.Client.MonitoredItem)">
            <summary>
            Removes an item from the subscription.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.RemoveItems(System.Collections.Generic.IEnumerable{Opc.Ua.Client.MonitoredItem})">
            <summary>
            Removes an item from the subscription.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.FindItemByClientHandle(System.UInt32)">
            <summary>
            Returns the monitored item identified by the client handle.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.ConditionRefresh">
            <summary>
            Tells the server to refresh all conditions being monitored by the subscription.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.ProcessTransferredSequenceNumbers(Opc.Ua.UInt32Collection)">
            <summary>
            Updates the available sequence numbers and queues after transfer.
            </summary>
            <remarks>
            If <see cref="P:Opc.Ua.Client.Subscription.RepublishAfterTransfer"/> is set to <c>true</c>, sequence numbers
            are queued for republish, otherwise ack may be sent.
            </remarks>
            <param name="availableSequenceNumbers">The list of available sequence numbers on the server.</param>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.GetMonitoredItems(Opc.Ua.UInt32Collection@,Opc.Ua.UInt32Collection@)">
            <summary>
            Call the GetMonitoredItems method on the server.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.StartKeepAliveTimer">
            <summary>
            Starts a timer to ensure publish requests are sent frequently enough to detect network interruptions.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.OnKeepAlive(System.Object)">
            <summary>
            Checks if a notification has arrived. Sends a publish if it has not.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.TraceState(System.String)">
            <summary>
            Dumps the current state of the session queue.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.ModifySubscription(System.Double,System.UInt32,System.UInt32)">
            <summary>
            Update the subscription with the given revised settings.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.CreateSubscription(System.UInt32,System.Double,System.UInt32,System.UInt32)">
            <summary>
            Update the subscription with the given revised settings.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.CreateOrModifySubscription(System.Boolean,System.UInt32,System.Double,System.UInt32,System.UInt32)">
            <summary>
            Update the subscription with the given revised settings.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.DeleteSubscription">
            <summary>
            Delete the subscription.
            Ignore errors, always reset all parameter.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.AdjustCounts(System.UInt32@,System.UInt32@)">
            <summary>
            Ensures sensible values for the counts.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.OnMessageReceived">
            <summary>
            Processes the incoming messages.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.VerifySubscriptionState(System.Boolean)">
            <summary>
            Throws an exception if the subscription is not in the correct state.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.UpdateMonitoringMode(System.Collections.Generic.IList{Opc.Ua.Client.MonitoredItem},System.Collections.Generic.IList{Opc.Ua.ServiceResult},Opc.Ua.StatusCodeCollection,Opc.Ua.DiagnosticInfoCollection,Opc.Ua.ResponseHeader,Opc.Ua.MonitoringMode)">
            <summary>
            Update the results to monitored items
            after updating the monitoring mode.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.PrepareItemsToCreate(System.Collections.Generic.List{Opc.Ua.Client.MonitoredItem}@)">
            <summary>
            Prepare the creation requests for all monitored items that have not yet been created.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.PrepareItemsToModify(Opc.Ua.MonitoredItemModifyRequestCollection,System.Collections.Generic.IList{Opc.Ua.Client.MonitoredItem})">
            <summary>
            Prepare the modify requests for all monitored items
            that need modification.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.TransferItems(Opc.Ua.UInt32Collection,Opc.Ua.UInt32Collection,System.Collections.Generic.IList{Opc.Ua.Client.MonitoredItem}@)">
            <summary>
            Transfer all monitored items and prepares the modify
            requests if transfer of client handles is not possible.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.PrepareResolveItemNodeIds(Opc.Ua.BrowsePathCollection,System.Collections.Generic.IList{Opc.Ua.Client.MonitoredItem})">
            <summary>
            Prepare the ResolveItem to NodeId service call.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.SaveDataChange(Opc.Ua.NotificationMessage,Opc.Ua.DataChangeNotification,System.Collections.Generic.IList{System.String})">
            <summary>
            Saves a data change in the monitored item cache.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.SaveEvents(Opc.Ua.NotificationMessage,Opc.Ua.EventNotificationList,System.Collections.Generic.IList{System.String})">
            <summary>
            Saves events in the monitored item cache.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.ManageMessageWorkerSemaphore">
            <summary>
            Manages the semaphore used to limit message workers for handling incoming messages
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.FindOrCreateEntry(System.DateTime,System.UInt32)">
            <summary>
            Find or create an entry for the incoming sequence number.
            </summary>
            <param name="utcNow">The current Utc time.</param>
            <param name="sequenceNumber">The sequence number for the new entry.</param>
        </member>
        <member name="T:Opc.Ua.Client.Subscription.IncomingMessage">
            <summary>
            A message received from the server cached until is processed or discarded.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.CreateAsync(System.Threading.CancellationToken)">
            <summary>
            Creates a subscription on the server and adds all monitored items.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.DeleteAsync(System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Deletes a subscription on the server.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.ModifyAsync(System.Threading.CancellationToken)">
            <summary>
            Modifies a subscription on the server.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.SetPublishingModeAsync(System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Changes the publishing enabled state for the subscription.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.RepublishAsync(System.UInt32,System.Threading.CancellationToken)">
            <summary>
            Republishes the specified notification message.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.ApplyChangesAsync(System.Threading.CancellationToken)">
            <summary>
            Applies any changes to the subscription items.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.ResolveItemNodeIdsAsync(System.Threading.CancellationToken)">
            <summary>
            Resolves all relative paths to nodes on the server.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.CreateItemsAsync(System.Threading.CancellationToken)">
            <summary>
            Creates all items on the server that have not already been created.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.ModifyItemsAsync(System.Threading.CancellationToken)">
            <summary>
            Modies all items that have been changed.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.DeleteItemsAsync(System.Threading.CancellationToken)">
            <summary>
            Deletes all items that have been marked for deletion.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.SetMonitoringModeAsync(Opc.Ua.MonitoringMode,System.Collections.Generic.IList{Opc.Ua.Client.MonitoredItem},System.Threading.CancellationToken)">
            <summary>
            Set monitoring mode of items.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.Subscription.ConditionRefreshAsync(System.Threading.CancellationToken)">
            <summary>
            Tells the server to refresh all conditions being monitored by the subscription.
            </summary>
        </member>
        <member name="T:Opc.Ua.Client.SubscriptionChangeMask">
            <summary>
            Flags indicating what has changed in a subscription.
            </summary>
        </member>
        <member name="F:Opc.Ua.Client.SubscriptionChangeMask.None">
            <summary>
            The subscription has not changed.
            </summary>
        </member>
        <member name="F:Opc.Ua.Client.SubscriptionChangeMask.Created">
            <summary>
            The subscription was created on the server.
            </summary>
        </member>
        <member name="F:Opc.Ua.Client.SubscriptionChangeMask.Deleted">
            <summary>
            The subscription was deleted on the server.
            </summary>
        </member>
        <member name="F:Opc.Ua.Client.SubscriptionChangeMask.Modified">
            <summary>
            The subscription was modified on the server.
            </summary>
        </member>
        <member name="F:Opc.Ua.Client.SubscriptionChangeMask.ItemsAdded">
            <summary>
            Monitored items were added to the subscription (but not created on the server)
            </summary>
        </member>
        <member name="F:Opc.Ua.Client.SubscriptionChangeMask.ItemsRemoved">
            <summary>
            Monitored items were removed to the subscription (but not deleted on the server)
            </summary>
        </member>
        <member name="F:Opc.Ua.Client.SubscriptionChangeMask.ItemsCreated">
            <summary>
            Monitored items were created on the server.
            </summary>
        </member>
        <member name="F:Opc.Ua.Client.SubscriptionChangeMask.ItemsDeleted">
            <summary>
            Monitored items were deleted on the server.
            </summary>
        </member>
        <member name="F:Opc.Ua.Client.SubscriptionChangeMask.ItemsModified">
            <summary>
            Monitored items were modified on the server.
            </summary>
        </member>
        <member name="F:Opc.Ua.Client.SubscriptionChangeMask.Transferred">
            <summary>
            Subscriptions was transferred on the server.
            </summary>
        </member>
        <member name="T:Opc.Ua.Client.FastDataChangeNotificationEventHandler">
            <summary>
            The delegate used to receive data change notifications via a direct function call instead of a .NET Event.
            </summary>
        </member>
        <member name="T:Opc.Ua.Client.FastEventNotificationEventHandler">
            <summary>
            The delegate used to receive event notifications via a direct function call instead of a .NET Event.
            </summary>
        </member>
        <member name="T:Opc.Ua.Client.SubscriptionStateChangedEventArgs">
            <summary>
            The event arguments provided when the state of a subscription changes.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.SubscriptionStateChangedEventArgs.#ctor(Opc.Ua.Client.SubscriptionChangeMask)">
            <summary>
            Creates a new instance.
            </summary>
        </member>
        <member name="P:Opc.Ua.Client.SubscriptionStateChangedEventArgs.Status">
            <summary>
            The changes that have affected the subscription.
            </summary>
        </member>
        <member name="T:Opc.Ua.Client.SubscriptionStateChangedEventHandler">
            <summary>
            The delegate used to receive subscription state change notifications.
            </summary>
        </member>
        <member name="T:Opc.Ua.Client.SubscriptionCollection">
            <summary>
            A collection of subscriptions.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.SubscriptionCollection.#ctor">
            <summary>
            Initializes an empty collection.
            </summary>
        </member>
        <member name="M:Opc.Ua.Client.SubscriptionCollection.#ctor(System.Collections.Generic.IEnumerable{Opc.Ua.Client.Subscription})">
            <summary>
            Initializes the collection from another collection.
            </summary>
            <param name="collection">The existing collection to use as the basis of creating this collection</param>
        </member>
        <member name="M:Opc.Ua.Client.SubscriptionCollection.#ctor(System.Int32)">
            <summary>
            Initializes the collection with the specified capacity.
            </summary>
            <param name="capacity">The max. capacity of the collection</param>
        </member>
        <member name="T:Opc.Ua.SessionClientBatched">
            <summary>
            The client side interface with support for batching according to operation limits.
            </summary>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.#ctor(Opc.Ua.ITransportChannel)">
            <summary>
            Intializes the object with a channel and default operation limits.
            </summary>
        </member>
        <member name="P:Opc.Ua.SessionClientBatched.OperationLimits">
            <summary>
            The operation limits are used to batch the service requests.
            </summary>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.AddNodes(Opc.Ua.RequestHeader,Opc.Ua.AddNodesItemCollection,Opc.Ua.AddNodesResultCollection@,Opc.Ua.DiagnosticInfoCollection@)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.AddNodesAsync(Opc.Ua.RequestHeader,Opc.Ua.AddNodesItemCollection,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.AddReferences(Opc.Ua.RequestHeader,Opc.Ua.AddReferencesItemCollection,Opc.Ua.StatusCodeCollection@,Opc.Ua.DiagnosticInfoCollection@)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.AddReferencesAsync(Opc.Ua.RequestHeader,Opc.Ua.AddReferencesItemCollection,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.DeleteNodes(Opc.Ua.RequestHeader,Opc.Ua.DeleteNodesItemCollection,Opc.Ua.StatusCodeCollection@,Opc.Ua.DiagnosticInfoCollection@)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.DeleteNodesAsync(Opc.Ua.RequestHeader,Opc.Ua.DeleteNodesItemCollection,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.DeleteReferences(Opc.Ua.RequestHeader,Opc.Ua.DeleteReferencesItemCollection,Opc.Ua.StatusCodeCollection@,Opc.Ua.DiagnosticInfoCollection@)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.DeleteReferencesAsync(Opc.Ua.RequestHeader,Opc.Ua.DeleteReferencesItemCollection,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.Browse(Opc.Ua.RequestHeader,Opc.Ua.ViewDescription,System.UInt32,Opc.Ua.BrowseDescriptionCollection,Opc.Ua.BrowseResultCollection@,Opc.Ua.DiagnosticInfoCollection@)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.BrowseAsync(Opc.Ua.RequestHeader,Opc.Ua.ViewDescription,System.UInt32,Opc.Ua.BrowseDescriptionCollection,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.TranslateBrowsePathsToNodeIds(Opc.Ua.RequestHeader,Opc.Ua.BrowsePathCollection,Opc.Ua.BrowsePathResultCollection@,Opc.Ua.DiagnosticInfoCollection@)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.TranslateBrowsePathsToNodeIdsAsync(Opc.Ua.RequestHeader,Opc.Ua.BrowsePathCollection,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.RegisterNodes(Opc.Ua.RequestHeader,Opc.Ua.NodeIdCollection,Opc.Ua.NodeIdCollection@)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.RegisterNodesAsync(Opc.Ua.RequestHeader,Opc.Ua.NodeIdCollection,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.UnregisterNodes(Opc.Ua.RequestHeader,Opc.Ua.NodeIdCollection)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.UnregisterNodesAsync(Opc.Ua.RequestHeader,Opc.Ua.NodeIdCollection,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.Read(Opc.Ua.RequestHeader,System.Double,Opc.Ua.TimestampsToReturn,Opc.Ua.ReadValueIdCollection,Opc.Ua.DataValueCollection@,Opc.Ua.DiagnosticInfoCollection@)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.ReadAsync(Opc.Ua.RequestHeader,System.Double,Opc.Ua.TimestampsToReturn,Opc.Ua.ReadValueIdCollection,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.HistoryRead(Opc.Ua.RequestHeader,Opc.Ua.ExtensionObject,Opc.Ua.TimestampsToReturn,System.Boolean,Opc.Ua.HistoryReadValueIdCollection,Opc.Ua.HistoryReadResultCollection@,Opc.Ua.DiagnosticInfoCollection@)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.HistoryReadAsync(Opc.Ua.RequestHeader,Opc.Ua.ExtensionObject,Opc.Ua.TimestampsToReturn,System.Boolean,Opc.Ua.HistoryReadValueIdCollection,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.Write(Opc.Ua.RequestHeader,Opc.Ua.WriteValueCollection,Opc.Ua.StatusCodeCollection@,Opc.Ua.DiagnosticInfoCollection@)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.WriteAsync(Opc.Ua.RequestHeader,Opc.Ua.WriteValueCollection,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.HistoryUpdate(Opc.Ua.RequestHeader,Opc.Ua.ExtensionObjectCollection,Opc.Ua.HistoryUpdateResultCollection@,Opc.Ua.DiagnosticInfoCollection@)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.HistoryUpdateAsync(Opc.Ua.RequestHeader,Opc.Ua.ExtensionObjectCollection,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.Call(Opc.Ua.RequestHeader,Opc.Ua.CallMethodRequestCollection,Opc.Ua.CallMethodResultCollection@,Opc.Ua.DiagnosticInfoCollection@)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.CallAsync(Opc.Ua.RequestHeader,Opc.Ua.CallMethodRequestCollection,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.CreateMonitoredItems(Opc.Ua.RequestHeader,System.UInt32,Opc.Ua.TimestampsToReturn,Opc.Ua.MonitoredItemCreateRequestCollection,Opc.Ua.MonitoredItemCreateResultCollection@,Opc.Ua.DiagnosticInfoCollection@)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.CreateMonitoredItemsAsync(Opc.Ua.RequestHeader,System.UInt32,Opc.Ua.TimestampsToReturn,Opc.Ua.MonitoredItemCreateRequestCollection,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.ModifyMonitoredItems(Opc.Ua.RequestHeader,System.UInt32,Opc.Ua.TimestampsToReturn,Opc.Ua.MonitoredItemModifyRequestCollection,Opc.Ua.MonitoredItemModifyResultCollection@,Opc.Ua.DiagnosticInfoCollection@)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.ModifyMonitoredItemsAsync(Opc.Ua.RequestHeader,System.UInt32,Opc.Ua.TimestampsToReturn,Opc.Ua.MonitoredItemModifyRequestCollection,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.SetMonitoringMode(Opc.Ua.RequestHeader,System.UInt32,Opc.Ua.MonitoringMode,Opc.Ua.UInt32Collection,Opc.Ua.StatusCodeCollection@,Opc.Ua.DiagnosticInfoCollection@)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.SetMonitoringModeAsync(Opc.Ua.RequestHeader,System.UInt32,Opc.Ua.MonitoringMode,Opc.Ua.UInt32Collection,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.SetTriggering(Opc.Ua.RequestHeader,System.UInt32,System.UInt32,Opc.Ua.UInt32Collection,Opc.Ua.UInt32Collection,Opc.Ua.StatusCodeCollection@,Opc.Ua.DiagnosticInfoCollection@,Opc.Ua.StatusCodeCollection@,Opc.Ua.DiagnosticInfoCollection@)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.SetTriggeringAsync(Opc.Ua.RequestHeader,System.UInt32,System.UInt32,Opc.Ua.UInt32Collection,Opc.Ua.UInt32Collection,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.DeleteMonitoredItems(Opc.Ua.RequestHeader,System.UInt32,Opc.Ua.UInt32Collection,Opc.Ua.StatusCodeCollection@,Opc.Ua.DiagnosticInfoCollection@)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.DeleteMonitoredItemsAsync(Opc.Ua.RequestHeader,System.UInt32,Opc.Ua.UInt32Collection,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.InitResponseCollections``2(``1@,Opc.Ua.DiagnosticInfoCollection@,System.Int32,System.UInt32)">
            <summary>
            Initialize the collections for a service call.
            </summary>
            <remarks>
            Preset the result collections with null if the operation limit
            is sufficient or with the final size if batching is necessary.
            </remarks>
        </member>
        <member name="M:Opc.Ua.SessionClientBatched.AddResponses``2(``1@,Opc.Ua.DiagnosticInfoCollection@,``1,Opc.Ua.DiagnosticInfoCollection)">
            <summary>
            Add the result of a batched service call to the results.
            </summary>
            <remarks>
            Assigns the batched collection result to the result if the result
            collection is not initialized, adds the range to the result
            collections otherwise.
            </remarks>
        </member>
        <member name="T:Opc.Ua.SessionClientExtensions">
            <summary>
            Extension helpers for client service calls.
            </summary>
        </member>
        <member name="M:Opc.Ua.SessionClientExtensions.Batch``2(``1,System.UInt32)">
            <summary>
            Returns batches of a collection for processing.
            </summary>
            <remarks>
            Returns the original collection if batchsize is 0 or the collection count is smaller than the batch size.
            </remarks>
            <typeparam name="T">The type of the items in the collection.</typeparam>
            <typeparam name="C">The type of the items in the collection.</typeparam>
            <param name="collection">The collection from which items are batched.</param>
            <param name="batchSize">The size of a batch.</param>
            <returns>The collection.</returns>
        </member>
    </members>
</doc>
