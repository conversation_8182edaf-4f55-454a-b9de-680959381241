﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace WindowsFormsCargill.DB
{
    public partial class Gateway_Info
    {
        public int ID { get; set; }
        public Constant.Gateway_Type Type { get; set; }
        public string Name { get; set; }
        public string IP { get; set; }
        public string Comment { get; set; }
        public string Firmware { get; set; }
        public DateTime TimeActive { get; set; }
    }

    public partial class GatewayInfoTableDb
    {
        private string connectionString;
        private string TableString;

        public GatewayInfoTableDb(string connection)
        {
            connectionString = connection;
            TableString = "GatewayInfoTable";
            CreateTable();
        }

        public void CreateTable()
        {
            try
            {
                using (SqlConnection sqlConnection = new SqlConnection(connectionString))
                {
                    sqlConnection.Open();
                    if (sqlConnection.State == ConnectionState.Open)
                    {
                        string checkTableExistsQuery = $"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='{TableString}' AND xtype='U') " +
                            $"CREATE TABLE {TableString} " +
                            "(ID INT PRIMARY KEY IDENTITY, Type INT, Name NVARCHAR(255), IP NVARCHAR(255), Comment NVARCHAR(255), Firmware NVARCHAR(255), TimeActive DATETIME)";

                        using (SqlCommand sqlCmd = new SqlCommand(checkTableExistsQuery, sqlConnection))
                        {
                            sqlCmd.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show($"Error: {err.Message}");
            }
        }

        public List<Gateway_Info> ReadAll()
        {
            List<Gateway_Info> responseBuf = new List<Gateway_Info>();
            try
            {
                using (SqlConnection sqlConnection = new SqlConnection(connectionString))
                {
                    sqlConnection.Open();
                    if (sqlConnection.State == ConnectionState.Open)
                    {
                        string commandString = $"SELECT * FROM {TableString}";
                        using (SqlCommand command = new SqlCommand(commandString, sqlConnection))
                        {
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    Gateway_Info gatewayInfo = new Gateway_Info
                                    {
                                        ID = reader.GetInt32(0),
                                        Type = (Constant.Gateway_Type)reader.GetInt32(1),
                                        Name = reader.GetString(2),
                                        IP = reader.GetString(3),
                                        Comment = reader.GetString(4),
                                        Firmware = reader.GetString(5),
                                        TimeActive = reader.GetDateTime(6)
                                    };

                                    responseBuf.Add(gatewayInfo);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show($"Error: {err.Message}");
            }
            return responseBuf;
        }

        public Gateway_Info Add(Gateway_Info newGwInfo)
        {
            Gateway_Info responseBuf = null;
            try
            {
                using (SqlConnection sqlConnection = new SqlConnection(connectionString))
                {
                    sqlConnection.Open();
                    if (sqlConnection.State == ConnectionState.Open)
                    {
                        string commandString = $"INSERT INTO {TableString} (Type, Name, IP, Comment, Firmware, TimeActive) " +
                            "VALUES (@Type, @Name, @IP, @Comment, @Firmware, @TimeActive); SELECT SCOPE_IDENTITY()";

                        using (SqlCommand command = new SqlCommand(commandString, sqlConnection))
                        {
                            command.Parameters.AddWithValue("@Type", (int)newGwInfo.Type);
                            command.Parameters.AddWithValue("@Name", newGwInfo.Name);
                            command.Parameters.AddWithValue("@IP", newGwInfo.IP);
                            command.Parameters.AddWithValue("@Comment", "");
                            command.Parameters.AddWithValue("@Firmware", "");
                            command.Parameters.AddWithValue("@TimeActive", newGwInfo.TimeActive);

                            newGwInfo.ID = Convert.ToInt32(command.ExecuteScalar());
                            responseBuf = newGwInfo;
                        }
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show($"Error: {err.Message}");
            }
            return responseBuf;
        }

        public void UpdateGateway(Gateway_Info gateway)
        {
            try
            {
                using (SqlConnection sqlConnection = new SqlConnection(connectionString))
                {
                    sqlConnection.Open();
                    if (sqlConnection.State == ConnectionState.Open)
                    {
                        string commandString = $"UPDATE {TableString} SET Type = @Type, Name = @Name, IP = @IP, Comment = @Comment, Firmware = @Firmware, TimeActive = @TimeActive " +
                            "WHERE ID = @ID";

                        using (SqlCommand command = new SqlCommand(commandString, sqlConnection))
                        {
                            command.Parameters.AddWithValue("@ID", gateway.ID);
                            command.Parameters.AddWithValue("@Type", (int)gateway.Type);
                            command.Parameters.AddWithValue("@Name", gateway.Name);
                            command.Parameters.AddWithValue("@IP", gateway.IP);
                            command.Parameters.AddWithValue("@Comment", gateway.Comment);
                            command.Parameters.AddWithValue("@Firmware", gateway.Firmware);
                            command.Parameters.AddWithValue("@TimeActive", gateway.TimeActive);

                            command.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show($"Error: {err.Message}");
            }
        }

        public List<int> ReadAllID()
        {
            List<int> responseBuf = new List<int>();
            try
            {
                using (SqlConnection sqlConnection = new SqlConnection(connectionString))
                {
                    sqlConnection.Open();
                    if (sqlConnection.State == ConnectionState.Open)
                    {
                        string commandString = $"SELECT ID FROM {TableString}";
                        using (SqlCommand command = new SqlCommand(commandString, sqlConnection))
                        {
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    responseBuf.Add(reader.GetInt32(0));
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show($"Error: {err.Message}");
            }
            return responseBuf;
        }

        public int DeleteById(int gwID)
        {
            int responseBuf = 0;
            try
            {
                using (SqlConnection sqlConnection = new SqlConnection(connectionString))
                {
                    sqlConnection.Open();
                    if (sqlConnection.State == ConnectionState.Open)
                    {
                        string commandString = $"DELETE FROM {TableString} WHERE ID = @ID";
                        using (SqlCommand command = new SqlCommand(commandString, sqlConnection))
                        {
                            command.Parameters.AddWithValue("@ID", gwID);
                            command.ExecuteNonQuery();
                            responseBuf = gwID;
                        }
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show($"Error: {err.Message}");
            }
            return responseBuf;
        }
    }
}
