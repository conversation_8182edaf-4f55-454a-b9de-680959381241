{"format": 1, "restore": {"D:\\Downloads\\IoT_Cargill_Winform\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Client\\Opc.Ua.Client.csproj": {}}, "projects": {"D:\\Downloads\\IoT_Cargill_Winform\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Client\\Opc.Ua.Client.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Downloads\\IoT_Cargill_Winform\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Client\\Opc.Ua.Client.csproj", "projectName": "OPCFoundation.NetStandard.Opc.Ua.Client.Debug", "projectPath": "D:\\Downloads\\IoT_Cargill_Winform\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Client\\Opc.Ua.Client.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Downloads\\IoT_Cargill_Winform\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Client\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "net48", "projectReferences": {"D:\\Downloads\\IoT_Cargill_Winform\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Configuration\\Opc.Ua.Configuration.csproj": {"projectPath": "D:\\Downloads\\IoT_Cargill_Winform\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Configuration\\Opc.Ua.Configuration.csproj"}, "D:\\Downloads\\IoT_Cargill_Winform\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Core\\Opc.Ua.Core.csproj": {"projectPath": "D:\\Downloads\\IoT_Cargill_Winform\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Core\\Opc.Ua.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net48": {"targetAlias": "net48", "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "D:\\Downloads\\IoT_Cargill_Winform\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Configuration\\Opc.Ua.Configuration.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Downloads\\IoT_Cargill_Winform\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Configuration\\Opc.Ua.Configuration.csproj", "projectName": "OPCFoundation.NetStandard.Opc.Ua.Configuration.Debug", "projectPath": "D:\\Downloads\\IoT_Cargill_Winform\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Configuration\\Opc.Ua.Configuration.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Downloads\\IoT_Cargill_Winform\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Configuration\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "net48", "projectReferences": {"D:\\Downloads\\IoT_Cargill_Winform\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Core\\Opc.Ua.Core.csproj": {"projectPath": "D:\\Downloads\\IoT_Cargill_Winform\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Core\\Opc.Ua.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net48": {"targetAlias": "net48", "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "D:\\Downloads\\IoT_Cargill_Winform\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Core\\Opc.Ua.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Downloads\\IoT_Cargill_Winform\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Core\\Opc.Ua.Core.csproj", "projectName": "OPCFoundation.NetStandard.Opc.Ua.Core.Debug", "projectPath": "D:\\Downloads\\IoT_Cargill_Winform\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Core\\Opc.Ua.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Downloads\\IoT_Cargill_Winform\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "net48", "projectReferences": {"D:\\Downloads\\IoT_Cargill_Winform\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Security.Certificates\\Opc.Ua.Security.Certificates.csproj": {"projectPath": "D:\\Downloads\\IoT_Cargill_Winform\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Security.Certificates\\Opc.Ua.Security.Certificates.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net48": {"targetAlias": "net48", "dependencies": {"Microsoft.Bcl.HashCode": {"target": "Package", "version": "[1.1.1, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[3.1.32, )"}, "NModbus": {"target": "Package", "version": "[3.0.81, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "D:\\Downloads\\IoT_Cargill_Winform\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Security.Certificates\\Opc.Ua.Security.Certificates.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Downloads\\IoT_Cargill_Winform\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Security.Certificates\\Opc.Ua.Security.Certificates.csproj", "projectName": "OPCFoundation.NetStandard.Opc.Ua.Security.Certificates.Debug", "projectPath": "D:\\Downloads\\IoT_Cargill_Winform\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Security.Certificates\\Opc.Ua.Security.Certificates.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Downloads\\IoT_Cargill_Winform\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Security.Certificates\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "net48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net48": {"targetAlias": "net48", "dependencies": {"Portable.BouncyCastle": {"target": "Package", "version": "[1.9.0, )"}, "System.Formats.Asn1": {"target": "Package", "version": "[6.0.0, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}}}