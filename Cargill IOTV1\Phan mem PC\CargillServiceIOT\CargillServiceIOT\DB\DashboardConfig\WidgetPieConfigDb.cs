﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Windows.Forms;

namespace WindowsFormsCargill.DB
{
    public class WidgetPieConfig_InfoDb
    {
        public int ID { get; set; }
        public int dashboardId { get; set; }
        public int deviceSumId { get; set; }
        public string ParaSumName { get; set; }
        public int device1Id { get; set; }
        public string Para1Name { get; set; }
        public int device2Id { get; set; }
        public string Para2Name { get; set; }
        public int device3Id { get; set; }
        public string Para3Name { get; set; }
        public int device4Id { get; set; }
        public string Para4Name { get; set; }
        //public int device5Id { get; set; }
        //public string Para5Name { get; set; }
        //public int device6Id { get; set; }
        //public string Para6Name { get; set; }
        //public int device7Id { get; set; }
        //public string Para7Name { get; set; }
        public int refreshTime { get; set; }
    }

    public class WidgetPieConfigDb
    {
        private string connectionString;
        private string TableString;

        public WidgetPieConfigDb(string connection)
        {
            connectionString = connection;
            TableString = "WidgetPieConfigTable";
            //CreateTable();
        }

        public void CreateTable()
        {
            try
            {
                using (SqlConnection sql_conn = new SqlConnection(connectionString))
                {
                    sql_conn.Open();
                    if (sql_conn.State == ConnectionState.Open)
                    {
                        SqlCommand sql_cmd = sql_conn.CreateCommand();
                        sql_cmd.CommandText = $"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='{TableString}' AND xtype='U') " +
                                              $"CREATE TABLE {TableString} (" +
                                              "ID INT PRIMARY KEY IDENTITY, " +
                                              "dashboardId INT, " +
                                              "deviceSumId INT, " +
                                              "ParaSumName VARCHAR(MAX), " +
                                              "device1Id INT, " +
                                              "Para1Name VARCHAR(MAX), " +
                                              "device2Id INT, " +
                                              "Para2Name VARCHAR(MAX), " +
                                              "device3Id INT, " +
                                              "Para3Name VARCHAR(MAX), " +
                                              "device4Id INT, " +
                                              "Para4Name VARCHAR(MAX), " +
                                              "refreshTime INT)";
                        sql_cmd.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
        }

        public List<WidgetPieConfig_InfoDb> ReadByDashboardId(int dashboardId)
        {
            List<WidgetPieConfig_InfoDb> responseBuf = new List<WidgetPieConfig_InfoDb>();
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        string commandStringBuf = $"SELECT * FROM {TableString} WHERE dashboardId = @dashboardId";
                        using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                        {
                            commandBuf.Parameters.AddWithValue("@dashboardId", dashboardId);

                            using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                            {
                                while (readerBuf.Read())
                                {
                                    if (readerBuf.FieldCount == 13)
                                    {
                                        WidgetPieConfig_InfoDb deviceInfoBuf = new WidgetPieConfig_InfoDb
                                        {
                                            ID = readerBuf.GetInt32(0),
                                            dashboardId = readerBuf.GetInt32(1),
                                            deviceSumId = readerBuf.GetInt32(2),
                                            ParaSumName = readerBuf["ParaSumName"].ToString(),
                                            device1Id = readerBuf.GetInt32(4),
                                            Para1Name = readerBuf["Para1Name"].ToString(),
                                            device2Id = readerBuf.GetInt32(6),
                                            Para2Name = readerBuf["Para2Name"].ToString(),
                                            device3Id = readerBuf.GetInt32(8),
                                            Para3Name = readerBuf["Para3Name"].ToString(),
                                            device4Id = readerBuf.GetInt32(10),
                                            Para4Name = readerBuf["Para4Name"].ToString(),
                                            refreshTime = readerBuf.GetInt32(12)
                                        };
                                        responseBuf.Add(deviceInfoBuf);
                                    }
                                }
                                readerBuf.Close();
                            }
                        }
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
            return responseBuf;
        }

        public WidgetPieConfig_InfoDb ReadById(int Id)
        {
            WidgetPieConfig_InfoDb deviceInfoBuf = new WidgetPieConfig_InfoDb();
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        string commandStringBuf = $"SELECT * FROM {TableString} WHERE ID = @ID";
                        using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                        {
                            commandBuf.Parameters.AddWithValue("@ID", Id);

                            using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                            {
                                while (readerBuf.Read())
                                {
                                    if (readerBuf.FieldCount == 13)
                                    {
                                        deviceInfoBuf.ID = readerBuf.GetInt32(0);
                                        deviceInfoBuf.dashboardId = readerBuf.GetInt32(1);
                                        deviceInfoBuf.deviceSumId = readerBuf.GetInt32(2);
                                        deviceInfoBuf.ParaSumName = readerBuf["ParaSumName"].ToString();
                                        deviceInfoBuf.device1Id = readerBuf.GetInt32(4);
                                        deviceInfoBuf.Para1Name = readerBuf["Para1Name"].ToString();
                                        deviceInfoBuf.device2Id = readerBuf.GetInt32(6);
                                        deviceInfoBuf.Para2Name = readerBuf["Para2Name"].ToString();
                                        deviceInfoBuf.device3Id = readerBuf.GetInt32(8);
                                        deviceInfoBuf.Para3Name = readerBuf["Para3Name"].ToString();
                                        deviceInfoBuf.device4Id = readerBuf.GetInt32(10);
                                        deviceInfoBuf.Para4Name = readerBuf["Para4Name"].ToString();
                                        deviceInfoBuf.refreshTime = readerBuf.GetInt32(12);
                                    }
                                }
                                readerBuf.Close();
                            }
                        }
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
            return deviceInfoBuf;
        }

        public void Update(WidgetPieConfig_InfoDb serialPlot)
        {
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        string commandStringBuf = $"UPDATE {TableString} SET dashboardId=@dashboardId,deviceSumId=@deviceSumId,ParaSumName=@ParaSumName, " +
                                                  $"device1Id=@device1Id,Para1Name=@Para1Name,device2Id=@device2Id,Para2Name=@Para2Name, " +
                                                  $"device3Id=@device3Id,Para3Name=@Para3Name,device4Id=@device4Id,Para4Name=@Para4Name, " +
                                                  $"refreshTime=@refreshTime WHERE ID=@ID";

                        using (SqlCommand command = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                        {
                            command.Parameters.AddWithValue("@dashboardId", serialPlot.dashboardId);
                            command.Parameters.AddWithValue("@deviceSumId", serialPlot.deviceSumId);
                            command.Parameters.AddWithValue("@ParaSumName", serialPlot.ParaSumName);
                            command.Parameters.AddWithValue("@device1Id", serialPlot.device1Id);
                            command.Parameters.AddWithValue("@Para1Name", serialPlot.Para1Name);
                            command.Parameters.AddWithValue("@device2Id", serialPlot.device2Id);
                            command.Parameters.AddWithValue("@Para2Name", serialPlot.Para2Name);
                            command.Parameters.AddWithValue("@device3Id", serialPlot.device3Id);
                            command.Parameters.AddWithValue("@Para3Name", serialPlot.Para3Name);
                            command.Parameters.AddWithValue("@device4Id", serialPlot.device4Id);
                            command.Parameters.AddWithValue("@Para4Name", serialPlot.Para4Name);
                            command.Parameters.AddWithValue("@refreshTime", serialPlot.refreshTime);
                            command.Parameters.AddWithValue("@ID", serialPlot.ID);

                            command.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
        }
    }
}
