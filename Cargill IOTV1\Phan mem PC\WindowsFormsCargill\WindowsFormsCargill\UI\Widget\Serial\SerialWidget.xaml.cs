using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using static WindowsFormsCargill.MainWindow;
using WindowsFormsCargill.DB;
using WindowsFormsCargill.UI.Widget.Serial2;

namespace WindowsFormsCargill.UI.Widget.Serial
{
    /// <summary>
    /// Interaction logic for SerialWidget.xaml
    /// </summary>
    public partial class SerialWidget : UserControl
    {
        public enum CONFIG_TYPE
        {
            SHOW,
            EDIT
        }
        private CONFIG_TYPE configType;

        private SerialUserControl serialPlot = new SerialUserControl();
        private SerialConfig serialConfig = new SerialConfig();

        private SystemInfo systemInfoPoint;

        public class DataViewModel
        {
            public Constant.Dashboard dashboardId { get; set; }
            public int PlotId { get; set; }
            public string DevName { get; set; }
            public string ParaName { get; set; }
            public int deviceId { get; set; }
            public int NumberValue { get; set; }
            public int refreshTime { get; set; }
            public string HistoryTime { get; set; }
            public float MinDashboardValue { get; set; }
            public float MaxDashboardValue { get; set; }
            
        }

        public DataViewModel dataView = new DataViewModel();

        private int CountTime = 0;

        public SerialWidget()
        {
            InitializeComponent();

            serialConfig.DataContext = dataView;

            serialConfig.SaveOnEvent += SaveBtOnclick_EventHandle;
            serialConfig.DeviceSelectEvent += DeviceSelectChanger;
        }

        // ================API for update data ====================================
        #region Api
        public void SetConfig(Constant.Dashboard dashboardId, int serialId)
        {
            dataView.dashboardId = dashboardId;
            dataView.PlotId = serialId;

            DB.WidgetSerialPlot_InfoDb serialWidget = MainWindow.apiDatabase.ReadWidgetSerialPlot_ById(serialId);

            Device_Info dev = MainWindow.apiDatabase.ReadDeviceInfo_ById(serialWidget.deviceId);

            dataView.DevName = dev.Name;
            dataView.ParaName = serialWidget.ParaName;
            dataView.deviceId = serialWidget.deviceId;
            dataView.NumberValue = serialWidget.NumberValue;
            dataView.refreshTime = serialWidget.refreshTime;
            dataView.HistoryTime = serialWidget.HistoryTime;
            dataView.MinDashboardValue = serialWidget.MinDashboardValue;
            dataView.MaxDashboardValue = serialWidget.MaxDashboardValue;

            serialPlot.XamlHeader.Text = dataView.DevName + ":" + dataView.ParaName;
            serialConfig.serialControl.XamlHeader.Text = dataView.DevName + ":" + dataView.ParaName;

            serialConfig.XamlHistoryTime.SelectedValue = dataView.HistoryTime;

            systemInfoPoint = MainWindow.systemInfo;
            switch (dataView.dashboardId)
            {
                case Constant.Dashboard.ENEGRY:
                case Constant.Dashboard.ENEGRY_REPORT:
                    serialConfig.XamlDeviceName.ItemsSource = systemInfoPoint.listDeviceEnegry;
                    //serialConfig.XamlDeviceName.SelectedItem = "{Binding Path=SPerson}";
                    serialConfig.XamlDeviceName.DisplayMemberPath = "deviceName";
                    serialConfig.XamlDeviceName.SelectedValuePath = "deviceId";

                    serialConfig.XamlDeviceName.SelectedValue = dataView.deviceId;

                    if (serialConfig.XamlDeviceName.SelectedIndex != -1)
                    {
                        serialConfig.XamlParaName.ItemsSource = systemInfoPoint.listDeviceEnegry[serialConfig.XamlDeviceName.SelectedIndex].listParaName;
                        serialConfig.XamlParaName.SelectedValue = dataView.ParaName;
                    }
                    break;
                case Constant.Dashboard.STEAM:
                case Constant.Dashboard.STEAM_REPORT:
                    serialConfig.XamlDeviceName.ItemsSource = systemInfoPoint.listDeviceSteam;
                    //serialConfig.XamlDeviceName.SelectedItem = "{Binding Path=SPerson}";
                    serialConfig.XamlDeviceName.DisplayMemberPath = "deviceName";
                    serialConfig.XamlDeviceName.SelectedValuePath = "deviceId";

                    serialConfig.XamlDeviceName.SelectedValue = dataView.deviceId;

                    if (serialConfig.XamlDeviceName.SelectedIndex != -1)
                    {
                        serialConfig.XamlParaName.ItemsSource = systemInfoPoint.listDeviceSteam[serialConfig.XamlDeviceName.SelectedIndex].listParaName;
                        serialConfig.XamlParaName.SelectedValue = dataView.ParaName;
                    }
                    break;
            }


            //serialConfig.XamlDeviceName.Text = dataView.DevName;
            //serialConfig.XamlParaName.Text = dataView.ParaName;
            serialConfig.XamlRefreshTime.Text = dataView.refreshTime.ToString();
            serialConfig.XamlMinDashboardValue.Text = dataView.MinDashboardValue.ToString();
            serialConfig.XamlMaxDashboardValue.Text = dataView.MaxDashboardValue.ToString();
        }

        public void setShowType(CONFIG_TYPE type)
        {
            this.Height = Height;
            configType = type;
            switch (configType)
            {
                case CONFIG_TYPE.SHOW:
                    XamlMainFrame.Content = serialPlot;
                    break;
                case CONFIG_TYPE.EDIT:
                    XamlMainFrame.Content = serialConfig;
                    break;
            }

        }

        public void RefreshPlotTime()
        {
            CountTime++;
            if (CountTime > dataView.refreshTime)
            {
                CountTime = 0;

                RefreshPlot();
            }

        }

        /// <summary>
        /// Calculate total value of all devices with the same parameter name at the latest time
        /// </summary>
        /// <returns>Total value of all devices</returns>
        private double CalculateTotalValue()
        {
            try
            {
                // Get all devices of the same type (Energy)
                List<DB.Device_Info> allDevices = MainWindow.apiDatabase.ReadDeviceInfo_ByType(Constant.Device_Application.ENEGRY);
                double totalValue = 0;

                foreach (var device in allDevices)
                {
                    // Get latest telemetry data for each device with the same parameter name
                    List<DB.TelemetryData> deviceData = MainWindow.apiDatabase.ReadListTelemetry_ByNumbeAndDeviceID(
                        dataView.ParaName, device.ID, 1);

                    if (deviceData.Count > 0)
                    {
                        totalValue += deviceData[0].Value;
                    }
                }

                return totalValue;
            }
            catch
            {
                return 0;
            }
        }

        public void RefreshPlot()
        {
            // Variable
            List<TelemetryData> data;

            // 7 days ago, 30 days ago, 12 months ago
            if (dataView.HistoryTime == "7 days ago" || dataView.HistoryTime == "30 days ago" || dataView.HistoryTime == "7 weeks ago" || dataView.HistoryTime == "12 months ago")
            {
                data = PlotForHistoryTime();
            }
            else if (dataView.HistoryTime == "range 2 days" || dataView.HistoryTime == "range 10 days") // near 2 days, time 5 minute/1 data
            {
                if (dataView.HistoryTime == "range 2 days") data = MainWindow.apiDatabase.ReadListTelemetry_ByNumbeAndDeviceID_ForDay(dataView.ParaName, dataView.deviceId, 2); // range 2 days, 5 minute
                else if (dataView.HistoryTime == "range 10 days") data = MainWindow.apiDatabase.ReadListTelemetry_ByNumbeAndDeviceID_ForDay(dataView.ParaName, dataView.deviceId, 10); // range 10 day, 15 minute
                else data = null;
            }
            else
            {
                // Defaut
                data = MainWindow.apiDatabase.ReadListTelemetry_ByNumbeAndDeviceID(dataView.ParaName,
                 dataView.deviceId, dataView.NumberValue);
            }

            // Calculate total value for percentage calculation
            double totalValue = CalculateTotalValue();

            // Update plot
            try
            {
                if (data.Count > 0)
                {
                    switch (configType)
                    {
                        case CONFIG_TYPE.SHOW:
                            serialPlot.UpdatePlot(data, dataView.HistoryTime, dataView.MinDashboardValue, dataView.MaxDashboardValue, totalValue);
                            break;
                        case CONFIG_TYPE.EDIT:
                            serialConfig.serialControl.UpdatePlot(data, dataView.HistoryTime, dataView.MinDashboardValue, dataView.MaxDashboardValue, totalValue);
                            break;
                    }
                }
                else
                {
                    //serialConfig.serialControl.ClearPlot();
                }
            }
            catch { }
        }

        public void SetNamConfig(string Name)
        {
            serialConfig.XamlTextname.Text = Name;
        }

        #endregion

        private void SaveBtOnclick_EventHandle(object sender, object[] value)
        {
            try
            {
                DB.WidgetSerialPlot_InfoDb serialWidget = new WidgetSerialPlot_InfoDb();
                serialWidget.ID = dataView.PlotId;
                serialWidget.dashboardId = (int)dataView.dashboardId;

                if (serialConfig.XamlDeviceName.SelectedIndex >= 0)
                {
                    serialWidget.deviceId = (int)serialConfig.XamlDeviceName.SelectedValue;
                    serialWidget.ParaName = value[1].ToString();
                    serialWidget.NumberValue = dataView.NumberValue;
                    serialWidget.refreshTime = int.Parse(value[2].ToString());
                    serialWidget.HistoryTime = value[3].ToString();
                    serialWidget.MinDashboardValue = float.Parse(value[4].ToString());
                    serialWidget.MaxDashboardValue = float.Parse(value[5].ToString());

                    MainWindow.apiDatabase.UpdateWidgetSerialPlot(serialWidget);
                }
                SetConfig(dataView.dashboardId, dataView.PlotId);
                RefreshPlot();
            }
            catch
            {

            }

        }

        private void DeviceSelectChanger(object sender, SelectionChangedEventArgs e)
        {
            if (serialConfig.XamlDeviceName.SelectedIndex != -1)
            {
                switch (dataView.dashboardId)
                {
                    case Constant.Dashboard.ENEGRY:
                    case Constant.Dashboard.ENEGRY_REPORT:

                        serialConfig.XamlParaName.ItemsSource = systemInfoPoint.listDeviceEnegry[serialConfig.XamlDeviceName.SelectedIndex].listParaName;
                        serialConfig.XamlParaName.SelectedIndex = 0;
                        break;
                    case Constant.Dashboard.STEAM:
                    case Constant.Dashboard.STEAM_REPORT:

                        serialConfig.XamlParaName.ItemsSource = systemInfoPoint.listDeviceSteam[serialConfig.XamlDeviceName.SelectedIndex].listParaName;
                        serialConfig.XamlParaName.SelectedIndex = 0;
                        break;
                }

            }
        }

        public List<TelemetryData> PlotForHistoryTime()
        {
            // Process 
            List<TelemetryData> ListData = new List<TelemetryData>();
            List<TelemetryData> ListDataTime = new List<TelemetryData>();
            DateTime StartTime = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 0, 0, 0);

            // check page show
            try
            {
                // Check TimeChoose is day, month
                if (dataView.HistoryTime == "7 days ago")
                {
                    int StartHour = 0;
                    int StartMin = 0;

                    DateTime tempDay = StartTime.AddHours(StartHour).AddMinutes(StartMin);
                    DateTime caculatorDay;
                    for (int i = 0; i <= 7; i++) // 0 to 7 day
                    {
                        caculatorDay = tempDay.AddDays(i - 6); //0H0M
                        TelemetryData data = MainWindow.apiDatabase.ReadTelemetry_ByTimeDay_V2(dataView.ParaName, dataView.deviceId, caculatorDay);
                        TelemetryData para = new TelemetryData();
                        if (data != null)
                        {
                            para.Time = caculatorDay;
                            para.Value = data.Value;

                            ListData.Add(para);
                        }
                        else
                        {
                            para.Time = caculatorDay;
                            para.Value = 0; // none value

                            ListData.Add(para);
                        }
                    }
                }              
                else if (dataView.HistoryTime == "30 days ago")
                {
                    int StartHour = 0;
                    int StartMin = 0;

                    DateTime tempDay = StartTime.AddHours(StartHour).AddMinutes(StartMin);
                    DateTime caculatorDay;
                    for (int i = 0; i <= 30; i++) // 0 to 30 day
                    {
                        caculatorDay = tempDay.AddDays(i - 29); //0H0M
                        TelemetryData data = MainWindow.apiDatabase.ReadTelemetry_ByTimeDay_V2(dataView.ParaName, dataView.deviceId, caculatorDay);
                        TelemetryData para = new TelemetryData();
                        if (data != null)
                        {
                            para.Time = caculatorDay;
                            para.Value = data.Value;

                            ListData.Add(para);
                        }
                        else
                        {
                            para.Time = caculatorDay;
                            para.Value = 0; // none value

                            ListData.Add(para);
                        }
                    }
                }
                else if (dataView.HistoryTime == "7 weeks ago")
                {
                    int StartHour = 0;
                    int StartMin = 0;

                    // Find the Monday of that week (StartTime)
                    DateTime StartOfWeek = StartTime;
                    while (StartOfWeek.DayOfWeek != DayOfWeek.Monday)
                    {
                        StartOfWeek = StartOfWeek.AddDays(-1);
                    }

                    DateTime tempDay = StartOfWeek.AddHours(StartHour).AddMinutes(StartMin);
                    DateTime caculatorDay;
                    for (int i = 0; i <= 7; i++) // 0 to 7 week
                    {
                        caculatorDay = tempDay.AddDays(-7 * (6 - i)); //0H0M
                        TelemetryData data = MainWindow.apiDatabase.ReadTelemetry_ByTimeDay_V2(dataView.ParaName, dataView.deviceId, caculatorDay);
                        TelemetryData para = new TelemetryData();
                        if (data != null)
                        {
                            para.Time = caculatorDay;
                            para.Value = data.Value;

                            ListData.Add(para);
                        }
                        else
                        {
                            para.Time = caculatorDay;
                            para.Value = 0; // none value

                            ListData.Add(para);
                        }
                    }
                }
                else if (dataView.HistoryTime == "12 months ago")
                {
                    int StartHour = 0;
                    int StartMin = 0;
                    int AddDay = 1 - StartTime.Day; // first day in month

                    DateTime tempDay = StartTime.AddDays(AddDay).AddHours(StartHour).AddMinutes(StartMin);
                    DateTime caculatorDay;
                    for (int i = 0; i <= 12; i++) // 0 to 12 month
                    {
                        caculatorDay = tempDay.AddMonths(i - 11); //0H0MDAY1
                        TelemetryData data = MainWindow.apiDatabase.ReadTelemetry_ByTimeDay_V2(dataView.ParaName, dataView.deviceId, caculatorDay);
                        TelemetryData para = new TelemetryData();
                        if (data != null)
                        {
                            para.Time = caculatorDay;
                            para.Value = data.Value;

                            ListData.Add(para);
                        }
                        else
                        {
                            para.Time = caculatorDay;
                            para.Value = 0; // none value

                            ListData.Add(para);
                        }
                    }
                }              

                // Clear before add data 
                for (int i = 0; i < ListData.Count - 1; i++)
                {
                    TelemetryData para = new TelemetryData();
                    para.Time = ListData[i].Time;
                    para.Value = ListData[i + 1].Value - ListData[i].Value;
                    ListDataTime.Add(para);
                }
            }
            catch (Exception err)
            {
                // MessageBox.Show( err.ToString());
                Console.WriteLine(err.ToString());
            }

            return ListDataTime;
        }
    }
}
