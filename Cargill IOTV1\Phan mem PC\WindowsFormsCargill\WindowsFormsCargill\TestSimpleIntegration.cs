using System;
using WindowsFormsCargill.UI.DashboardEnergy;

namespace WindowsFormsCargill
{
    /// <summary>
    /// Test class to verify SavePosition → Update_PosDevice integration
    /// </summary>
    public class TestSimpleIntegration
    {
        /// <summary>
        /// Test static reference functionality
        /// </summary>
        public static void TestStaticReference()
        {
            try
            {
                Console.WriteLine("=== Testing Static Reference ===");
                
                // Test when no instance exists
                if (SubPageEnegryDashboard.CurrentInstance == null)
                {
                    Console.WriteLine("✓ CurrentInstance is null when no dashboard exists");
                }
                else
                {
                    Console.WriteLine("✗ CurrentInstance should be null initially");
                }
                
                // Create dashboard instance
                SubPageEnegryDashboard dashboard = new SubPageEnegryDashboard();
                
                // Test static reference is set
                if (SubPageEnegryDashboard.CurrentInstance == dashboard)
                {
                    Console.WriteLine("✓ CurrentInstance is set correctly when dashboard is created");
                }
                else
                {
                    Console.WriteLine("✗ CurrentInstance not set correctly");
                }
                
                // Test Update_PosDevice can be called
                try
                {
                    SubPageEnegryDashboard.CurrentInstance.Update_PosDevice();
                    Console.WriteLine("✓ Update_PosDevice can be called via static reference");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"✗ Update_PosDevice failed: {ex.Message}");
                }
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Test failed with exception: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Test the integration flow
        /// </summary>
        public static void TestIntegrationFlow()
        {
            try
            {
                Console.WriteLine("=== Testing Integration Flow ===");
                
                // Simulate the flow that happens when Save button is clicked
                if (SubPageEnegryDashboard.CurrentInstance != null)
                {
                    Console.WriteLine("Dashboard instance available");
                    
                    // This simulates what happens in SavePOS_BtOnClick
                    SubPageEnegryDashboard.CurrentInstance.Update_PosDevice();
                    Console.WriteLine("✓ Successfully called Update_PosDevice from static reference");
                    
                    // Simulate console log from SavePOS_BtOnClick
                    int testDeviceId = 123;
                    string testXPos = "10.5";
                    string testYPos = "20.3";
                    Console.WriteLine($"✓ Simulated: Called Update_PosDevice for device {testDeviceId} with position ({testXPos}, {testYPos})");
                }
                else
                {
                    Console.WriteLine("✗ No dashboard instance available for testing");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Integration test failed: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Run all tests
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("=== Testing SavePosition → Update_PosDevice Integration ===");
            Console.WriteLine();
            
            TestStaticReference();
            Console.WriteLine();
            
            TestIntegrationFlow();
            Console.WriteLine();
            
            Console.WriteLine("=== Tests Completed ===");
            Console.WriteLine();
            Console.WriteLine("To test manually:");
            Console.WriteLine("1. Open Energy Dashboard");
            Console.WriteLine("2. Click on a device widget");
            Console.WriteLine("3. Change X and Y position values");
            Console.WriteLine("4. Click Save button");
            Console.WriteLine("5. Check if device position updates on dashboard");
            Console.WriteLine("6. Check console for confirmation message");
        }
    }
}
