﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using WindowsFormsCargill.DB;

namespace AppServiceCargill.Core
{
    public class RulerFunction
    {
        // config
        public int Id;
        public int DevId;
        public string InputNam;
        public float LimitValue;
        public float Delta;
        public string EventName;
        public EVENT_TYPE EventType;
        public FUNCTION_NAME FunctionName;
        // state
        private bool EventStateOld;
        private bool EventState;
        public RulerFunction()
        {
            EventStateOld = false;
            EventState = false;
        }
        public void SetConfig(RuleConfig_InfoDb ruleData)
        {
            Id = ruleData.ID;
            DevId = ruleData.DevId;
            InputNam = ruleData.InputNam;
            LimitValue = ruleData.LimitValue;
            Delta = ruleData.Delta;
            EventName = ruleData.EventName;
            EventType = ruleData.EventType;
            FunctionName = ruleData.FunctionName;
        }
        public bool LimitLowFuntion(double InputNew)
        {
            bool result = false;
            if (InputNew <= LimitValue)
            {
                EventState = true;
            }
            if (EventStateOld == false && EventState == true)
            {
                // event Out
                result = true;
            }
            if (InputNew > (LimitValue + Delta))
            {
                EventState = false;
            }
            if (EventStateOld == true && EventState == false)
            {
                // event Releas

            }
            EventStateOld = EventState;
            return result;
        }
        public bool LimitHighFuntion(double InputNew)
        {
            bool result = false;
            if (InputNew <= LimitValue)
            {
                EventState = true;
            }
            if (EventStateOld == false && EventState == true)
            {
                // event Out
                result = true;
            }
            if (InputNew > (LimitValue + Delta))
            {
                EventState = false;
            }
            if (EventStateOld == true && EventState == false)
            {
                // event Releas

            }
            EventStateOld = EventState;
            return result;
        }
        public bool ProcessFunction(double newIntput)
        {

            switch (FunctionName)
            {
                case FUNCTION_NAME.LimitLowFuntion:
                    {
                        Object[] obj = { newIntput };
                        Type thisType = this.GetType();
                        MethodInfo theMethod = thisType.GetMethod(FUNCTION_NAME.LimitLowFuntion.ToString());
                        var result = theMethod.Invoke(this, obj);
                        return (bool)result;
                    }
                case FUNCTION_NAME.LimitHighFuntion:
                    {
                        Object[] obj = { newIntput };
                        Type thisType = this.GetType();
                        MethodInfo theMethod = thisType.GetMethod(FUNCTION_NAME.LimitLowFuntion.ToString());
                        var result = theMethod.Invoke(this, obj);
                        return (bool)result;
                    }
            }

            return false;
        }

    }

    public class RuleProcess
    {
        ApiDatabase apiDatabase = new ApiDatabase();

        List<RulerFunction> listRuler = new List<RulerFunction>();
        public RuleProcess()
        {

            List<RuleConfig_InfoDb> listRuleConfig = apiDatabase.ReadAllRuleConfig();

            for(int i = 0; i < listRuleConfig.Count; i++)
            {
                RulerFunction ruleLow = new RulerFunction();
                ruleLow.SetConfig(listRuleConfig[i]);

                listRuler.Add(ruleLow);
            }

            
        }
        public void MessageProcess(int PlcId, List<TelemetryData> listPara)
        {
            for(int i=0; i< listPara.Count; i++)
            {
                for(int k=0;k< listRuler.Count;k++)
                {
                    if (listPara[i].Name == listRuler[k].InputNam && PlcId == listRuler[k].DevId)
                    {
                        bool result = listRuler[k].ProcessFunction(listPara[i].Value);

                        if(result == true)
                        {
                            apiDatabase.AddEvent(new EventData_InfoDb() { DevId = PlcId, EventType = listRuler[k].EventType, Content = $"Device {PlcId}: {listRuler[k].EventName}",
                                                        Para = listRuler[k].InputNam,Value = listPara[i].Value.ToString(),   Time = DateTime.Now });
                        }
                    }
                }
                
            }
        }

    }
}
