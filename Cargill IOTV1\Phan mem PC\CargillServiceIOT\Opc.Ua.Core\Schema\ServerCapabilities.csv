﻿NA,No capability information is available. Cannot be used in combination with any other capability.
DA,Provides current data.
HD,Provides historical data.
AC,Provides alarms and conditions that may require operator interaction.
HE,Provides historical alarms and events.
GDS,Supports the Global Discovery Server information model. 
LDS,Only supports the Discovery Services. Cannot be used in combination with any other capability.
DI,Supports the Device Integration (DI) information model (see DI).
ADI,Supports the Analyser Device Integration (ADI) information model (see ADI).
FDI,Supports the Field Device Integration (FDI) information model (see FDI).
FDIC,Supports the Field Device Integration (FDI) Communication Server information model (see FDI).
PLC,Supports the PLCopen information model (see PLCopen).
S95,Supports the ISA95 information model (see ISA-95).
RCP,Supports the reverse connect capabilities defined in Part 6.
PUB,Supports the Publisher capabilities defined in Part 14.
NTRS,The server is part of a non-transparent redundant server set defined in Part 4.
AUTOID,Supports the AutoID information model.
MDIS,Supports the MDIS information model.
CNC,Supports the information model for Computerized Numerical Control (CNC) systems.
PLK,Supports the POWERLINK information model.
FDT,Supports the FDT information model.
TMC,Supports the Tobacco Machine Communication (TMC) information model.
CSPP,Supports the CSP+ device profile (CSPPlusForMachine) information model.
61850,Supports the IEC61850 information model.
PACKML,Supports the PackML information model.
MTC,Supports the MTConnect information model.
AUTOML,Supports the AutomationML information model.
SERCOS,Supports the Sercos information model.
MIMOSA,Supports the MIMOSA information model.
WITSML,Supports the WITSML information model.
DEXPI,Supports the DEXPI information model.
IOLINK,Supports the IOLINK information model.
VROBOT,Supports the VDMA ROBOTICS information model.
PNO,Supports the ProfiNET information model.
PADIM,Supports the information model for Process Automation Devices.
ALIAS,Supports the AliasNames capabilities.
SKS,Supports the Security Key Servers (SKS) capabilities.
FXAC,Supports the UA FX Automation Component information model.
FXCM,Supports the UA FX Connection Manager information model.
