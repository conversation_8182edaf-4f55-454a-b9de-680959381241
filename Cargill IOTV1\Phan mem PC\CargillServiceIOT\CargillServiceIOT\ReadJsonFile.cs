﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WindowsFormsCargill
{
    public class ReadJsonFile
    {
        public string ReadFromJsonIOTFile(string value)
        {
            // Path to the JSON file
            string filePath = "C:\\IOTCargill\\Database\\IOTCargill.json";

            // Check if the file exists
            if (File.Exists(filePath))
            {
                // Read the content of the JSON file
                string json = File.ReadAllText(filePath);

                // Deserialize the JSON string into a dictionary
                var data = JsonConvert.DeserializeObject<Dictionary<string, object>>(json);

                // Check if the key exists in the dictionary
                if (data.ContainsKey(value))
                {
                    // Return the value corresponding to the key
                    return data[value].ToString();
                }
                else
                {
                    // Return null if the key does not exist
                    return null;
                }
            }
            else
            {
                // Return null if the file does not exist
                return null;
            }
        }
    }
}
