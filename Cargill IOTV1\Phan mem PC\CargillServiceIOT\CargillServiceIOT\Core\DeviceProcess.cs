﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using WindowsFormsCargill.DB;
using static AppServiceCargill.Connection.Connection_PLC_OPC;
using System.Windows.Forms;

namespace AppServiceCargill.Core
{
    public class DeviceProcess
    {
        bool showOne = false;
        public class DeviceInfor
        {
            public int devId;
            public string Name;

            public int TimeAlive;
            public int CountTimeAlive;

            public CONNECTION_STATE connectionState;
            public CONNECTION_STATE connectionStateOld;

            public List<int> listMsgId;
            public DeviceInfor()
            {
                listMsgId = new List<int>();
            }

            public List<TelemetryData> listTelemetry { get; set; }
        }
        public List<DeviceInfor> listDevice = new List<DeviceInfor>();

        public partial class PLCOPC_SendDataWS
        {
            public int deviceId { get; set; }
            public List<TelemetryData> listTelemetry { get; set; }
        }

        // event
        public event EventHandler<PLCOPC_SendDataWS> plcSendWSData_Event;

        // rule
        RuleProcess rulerProcess = new RuleProcess();
        // global variable

        ApiDatabase apiDatabase = new ApiDatabase();

        private Thread deviceProcessThread;

        private bool running;
        public DeviceProcess()
        {
            // get all device
            List<Device_Info>  listDev = apiDatabase.ReadAllDeviceInfo();
            for(int i=0; i< listDev.Count; i++)
            {
                DeviceInfor dev = new DeviceInfor();
                dev.devId = listDev[i].ID;
                dev.Name =  listDev[i].Name;
                dev.TimeAlive = listDev[i].TimeAlive;
                dev.CountTimeAlive = 1;
                dev.connectionState = CONNECTION_STATE.DISCONNECT;// default
                List<MsgOpc_InfoDataDb>  listTemMsg = apiDatabase.ReadOpcMsgInfo_ByDeviceId(dev.devId);
                for(int k=0;k< listTemMsg.Count; k++)
                {
                    dev.listMsgId.Add(listTemMsg[k].ID);
                }
                listDevice.Add(dev);
            }

            deviceProcessThread = new Thread(DeviceProcessHandle);

        }

        private void DeviceProcessHandle()
        {
            running = true;

            long timeNow = DateTime.Now.Ticks / TimeSpan.TicksPerMillisecond;
            long timePre = timeNow;

            PLCOPC_ReadData readBuf = new PLCOPC_ReadData();

            // Main run
            while (running)
            {
                try
                {
                    for(int i = 0; i < listDevice.Count; i++)
                    {


                        if (listDevice[i].CountTimeAlive ==  0 )
                        {

                            listDevice[i].connectionState = CONNECTION_STATE.CONNECTED;
                            if(showOne == false)
                            {
                                showOne = true;
                               // //MessageBox.Show("Connec opc ok");
                            }                               
                        }

                        if (listDevice[i].CountTimeAlive < 86400)// one day
                        {
                            listDevice[i].CountTimeAlive++;
                        }

                        if (listDevice[i].TimeAlive > 0)
                        {
                            if (listDevice[i].CountTimeAlive > listDevice[i].TimeAlive)
                            {

                                listDevice[i].connectionState = CONNECTION_STATE.DISCONNECT;
                            }
                        }
                        if(listDevice[i].connectionStateOld!= listDevice[i].connectionState)
                        {
                            if(listDevice[i].connectionState == CONNECTION_STATE.DISCONNECT)
                            {
                                apiDatabase.AddEvent(new EventData_InfoDb() { DevId = listDevice[i].devId, DeviceType = WindowsFormsCargill.Constant.Device_Type.DEVICE, EventType = EVENT_TYPE.EVENT_ERROR, Content = $"Device {listDevice[i].Name}: DISCONNECTED", Time = DateTime.Now });

                            }
                            else
                            {
                                apiDatabase.AddEvent(new EventData_InfoDb() { DevId = listDevice[i].devId, DeviceType = WindowsFormsCargill.Constant.Device_Type.DEVICE, EventType = EVENT_TYPE.EVENT_INFOR, Content = $"Device {listDevice[i].Name}: CONNECTED", Time = DateTime.Now });

                            }
                        }

                        listDevice[i].connectionStateOld = listDevice[i].connectionState;
                    }
                }
                catch
                {

                }
                Thread.Sleep(1000);
            } 
        }

        public void Thread_Start()
        {
            if (deviceProcessThread.ThreadState == ThreadState.Unstarted)
            {
                deviceProcessThread.Start();
            }
        }
        public void Thread_Stop()
        {
            running = false;
        }
        public void plcIntervalData_EventHandle(PLCOPC_ReadData data)
        {
            for (int i = 0; i < listDevice.Count; i++)
                for (int k = 0; k < listDevice[i].listMsgId.Count; k++)
                {
                    if (data.MsgId == listDevice[i].listMsgId[k] )
                    {
                        listDevice[i].CountTimeAlive = 0;// reset alive Time
                        ProcessData(listDevice[i].devId, data);
                    }
                }

        }

        private void ProcessData(int devId, PLCOPC_ReadData data)
        {

            List<TelemetryData> listTelemetry = new List<TelemetryData>();
            for (int i = 0; i < data.listData.Count; i++)
            {
                TelemetryData telemetry = new TelemetryData();
                if (data.listData[i][2] != "NaN")
                {
                    telemetry.deviceID = devId;
                    telemetry.Name = data.listData[i][1];
                    switch (data.listData[i][3])
                    {
                        case "Float":
                            {
                                float value;
                                bool result = float.TryParse(data.listData[i][2], out value);
                                if (result)
                                {
                                    if(value == float.NaN)
                                    {
                                        telemetry.Value = 0;
                                    }
                                    else
                                    {
                                        telemetry.Value = value;
                                    }
                                   
                                }
                            }
                            break;
                        case "Double":
                            {
                                double value;
                                bool result = double.TryParse(data.listData[i][2], out value);
                                byte[] bytevalue = BitConverter.GetBytes(value);
                                Int64 totalPower = BitConverter.ToInt64(bytevalue, 0);
                                value = ((double)totalPower) / 1000; // kw (edel)                          
                                Console.WriteLine(value.ToString());

                                if (result)
                                {
                                    if (Double.IsNaN(value))
                                    {
                                        telemetry.Value = 0;
                                    }
                                    else telemetry.Value = value;
                                }
                            }
                            break;
                    }
                    telemetry.Time = DateTime.Now;

                    // Round data with: edel, current, voltage, ...
                    if(telemetry.Name == "TotalMassflow" /*|| telemetry.Name == "EDel" || telemetry.Name == "Total net kWh"*/ || telemetry.Name == "Total net kVAh" || telemetry.Name == "Total net kVArh")
                    {
                        telemetry.Value = Math.Round(telemetry.Value, 3); // round 3
                    }
                    else if (telemetry.Name == "Pressure" || telemetry.Name == "Massflow" || telemetry.Name == "FlowData" 
                    || telemetry.Name == "Temperature")
                    {
                        telemetry.Value = Math.Round(telemetry.Value, 2); // round 2
                    }
                    else
                    {
                        telemetry.Value = Math.Round(telemetry.Value, 0); // round 0
                    }

                    listTelemetry.Add(telemetry);

                    rulerProcess.MessageProcess(data.plcID, listTelemetry);
                }

            }

            // Save data to database
            if(data.isSaveDatabase == true) apiDatabase.AddListTelemetry(listTelemetry);

            // Send event -> websocket
            if (data.isSaveDatabase == false)
            { 
                PLCOPC_SendDataWS dataWS = new PLCOPC_SendDataWS();
                dataWS.deviceId = devId;
                dataWS.listTelemetry = listTelemetry;
                plcSendWSData_Event?.Invoke(this, dataWS);
            }
        }
    }
}
