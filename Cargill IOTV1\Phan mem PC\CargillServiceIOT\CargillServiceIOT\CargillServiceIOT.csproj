﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{8BED0D41-D415-4ACB-8826-8D317FD62C4D}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>CargillServiceIOT</RootNamespace>
    <AssemblyName>CargillServiceIOT</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject />
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xaml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="UIAutomationProvider" />
    <Reference Include="WindowsBase" />
    <Reference Include="WindowsFormsIntegration" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AppServiceIoTCargill.xaml.cs">
      <DependentUpon>AppServiceIoTCargill.xaml</DependentUpon>
    </Compile>
    <Compile Include="CargillServiceIOT.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="CargillServiceIOT.Designer.cs">
      <DependentUpon>CargillServiceIOT.cs</DependentUpon>
    </Compile>
    <Compile Include="Connection\ConnectionWebsocket.cs" />
    <Compile Include="Connection\Connection_PLC_OPC.cs" />
    <Compile Include="Core\ConnectionCore.cs" />
    <Compile Include="Core\DeviceProcess.cs" />
    <Compile Include="Core\RuleProcess.cs" />
    <Compile Include="DB\ApiDatabase.cs" />
    <Compile Include="DB\Connection\OpcMsgDB.cs" />
    <Compile Include="DB\Connection\OpcMsgVarDB.cs" />
    <Compile Include="DB\Connection\TempOpcMsgDeviceDb.cs" />
    <Compile Include="DB\Constant_Database.cs" />
    <Compile Include="DB\DashboardConfig\DashboardConfigDb.cs" />
    <Compile Include="DB\DashboardConfig\WidgetBarConfig.cs" />
    <Compile Include="DB\DashboardConfig\WidgetBarValueConfigDb.cs" />
    <Compile Include="DB\DashboardConfig\WidgetPieConfigDb.cs" />
    <Compile Include="DB\DashboardConfig\WidgetSerial2ConfigDb.cs" />
    <Compile Include="DB\DashboardConfig\WidgetSerialBarConfigDb.cs" />
    <Compile Include="DB\DashboardConfig\WidgetSerialConfigDb.cs" />
    <Compile Include="DB\DeviceInfoDb.cs" />
    <Compile Include="DB\EventDb.cs" />
    <Compile Include="DB\GatewayInfoDb.cs" />
    <Compile Include="DB\RuleConfigDb.cs" />
    <Compile Include="DB\TelemetryDb.cs" />
    <Compile Include="OPC_UAClientHelperAPI.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ReadJsonFile.cs" />
    <Service Include="{94E38DFF-614B-4cbd-B67C-F211BB35CE8B}" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Opc.Ua.Client\Opc.Ua.Client.csproj">
      <Project>{604c10e3-6ffd-463e-bf87-0b0eeee392e9}</Project>
      <Name>Opc.Ua.Client</Name>
    </ProjectReference>
    <ProjectReference Include="..\Opc.Ua.Configuration\Opc.Ua.Configuration.csproj">
      <Project>{158dc5f3-d790-4a39-a08a-d5718ed56223}</Project>
      <Name>Opc.Ua.Configuration</Name>
    </ProjectReference>
    <ProjectReference Include="..\Opc.Ua.Core\Opc.Ua.Core.csproj">
      <Project>{193db2e9-adf2-4759-a12c-0f3f64728172}</Project>
      <Name>Opc.Ua.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\Opc.Ua.Security.Certificates\Opc.Ua.Security.Certificates.csproj">
      <Project>{e6d6b3cd-f693-44df-87b7-60946553947a}</Project>
      <Name>Opc.Ua.Security.Certificates</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Page Include="AppServiceIoTCargill.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>