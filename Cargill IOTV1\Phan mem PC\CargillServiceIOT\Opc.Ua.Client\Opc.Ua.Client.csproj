﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFramework>net48</TargetFramework>
	</PropertyGroup>
  <PropertyGroup>
    <DefineConstants>$(DefineConstants);CLIENT_ASYNC</DefineConstants>
    <AssemblyName>Opc.Ua.Client</AssemblyName>
    <TargetFrameworks>$(LibTargetFrameworks)</TargetFrameworks>
    <PackageId>OPCFoundation.NetStandard.Opc.Ua.Client</PackageId>
    <RootNamespace>Opc.Ua.Client</RootNamespace>
    <Description>OPC UA Client Class Library</Description>
    <IsPackable>true</IsPackable>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <PackageLicenseFile></PackageLicenseFile>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>

  <PropertyGroup Condition="'$(SignAssembly)' == 'true'">
    <DefineConstants>$(DefineConstants);SIGNASSEMBLY</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)' == 'Debug'">
    <PackageId>$(PackageId).Debug</PackageId>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Opc.Ua.Core\Opc.Ua.Core.csproj" />
    <ProjectReference Include="..\Opc.Ua.Configuration\Opc.Ua.Configuration.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System.Windows.Forms" />
  </ItemGroup>

  <Target Name="GetPackagingOutputs" />

</Project>
