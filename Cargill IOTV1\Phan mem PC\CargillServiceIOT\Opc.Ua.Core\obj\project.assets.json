{"version": 3, "targets": {".NETFramework,Version=v4.8": {"Microsoft.Bcl.HashCode/1.1.1": {"type": "package", "frameworkAssemblies": ["System", "mscorlib"], "compile": {"ref/net461/Microsoft.Bcl.HashCode.dll": {}}, "runtime": {"lib/net461/Microsoft.Bcl.HashCode.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging.Abstractions/3.1.32": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "NModbus/3.0.81": {"type": "package", "compile": {"lib/net46/NModbus.dll": {"related": ".xml"}}, "runtime": {"lib/net46/NModbus.dll": {"related": ".xml"}}}, "Portable.BouncyCastle/1.9.0": {"type": "package", "compile": {"lib/net40/BouncyCastle.Crypto.dll": {"related": ".xml"}}, "runtime": {"lib/net40/BouncyCastle.Crypto.dll": {"related": ".xml"}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Formats.Asn1/6.0.0": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.ValueTuple": "4.5.0"}, "frameworkAssemblies": ["System.Numerics"], "compile": {"lib/net461/System.Formats.Asn1.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Formats.Asn1.dll": {"related": ".xml"}}}, "System.Memory/4.5.4": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/4.5.3": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.ValueTuple/4.5.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net47/System.ValueTuple.dll": {}}, "runtime": {"lib/net47/System.ValueTuple.dll": {"related": ".xml"}}}, "OPCFoundation.NetStandard.Opc.Ua.Security.Certificates.Debug/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.8", "dependencies": {"Portable.BouncyCastle": "1.9.0", "System.Formats.Asn1": "6.0.0"}, "compile": {"bin/placeholder/OPCFoundation.NetStandard.Opc.Ua.Security.Certificates.Debug.dll": {}}, "runtime": {"bin/placeholder/OPCFoundation.NetStandard.Opc.Ua.Security.Certificates.Debug.dll": {}}}}}, "libraries": {"Microsoft.Bcl.HashCode/1.1.1": {"sha512": "MalY0Y/uM/LjXtHfX/26l2VtN4LDNZ2OE3aumNOHDLsT4fNYy2hiHXI4CXCqKpNUNm7iJ2brrc4J89UdaL56FA==", "type": "package", "path": "microsoft.bcl.hashcode/1.1.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Bcl.HashCode.dll", "lib/net461/Microsoft.Bcl.HashCode.xml", "lib/netcoreapp2.1/Microsoft.Bcl.HashCode.dll", "lib/netcoreapp2.1/Microsoft.Bcl.HashCode.xml", "lib/netstandard2.0/Microsoft.Bcl.HashCode.dll", "lib/netstandard2.0/Microsoft.Bcl.HashCode.xml", "lib/netstandard2.1/Microsoft.Bcl.HashCode.dll", "lib/netstandard2.1/Microsoft.Bcl.HashCode.xml", "microsoft.bcl.hashcode.1.1.1.nupkg.sha512", "microsoft.bcl.hashcode.nuspec", "ref/net461/Microsoft.Bcl.HashCode.dll", "ref/netcoreapp2.1/Microsoft.Bcl.HashCode.dll", "ref/netstandard2.0/Microsoft.Bcl.HashCode.dll", "ref/netstandard2.1/Microsoft.Bcl.HashCode.dll", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Logging.Abstractions/3.1.32": {"sha512": "BShtU4APsEGa72vaj42rHc8CW40xpFp1dousoNeziHwOUFJLNOJP2wxKX2jyfer0nFaLSkIMtgKn+qeJtS3Pcw==", "type": "package", "path": "microsoft.extensions.logging.abstractions/3.1.32", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.3.1.32.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "NModbus/3.0.81": {"sha512": "EzKEp7CHD8ErBL36iMts+6IrZZ9FEqllaD7Y5XzhoRjlxt5yXRughQ1bxPs99QFYFkW5xfkANB0Qs1gAmYGP8Q==", "type": "package", "path": "nmodbus/3.0.81", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/net46/NModbus.dll", "lib/net46/NModbus.xml", "lib/net6.0/NModbus.dll", "lib/net6.0/NModbus.xml", "lib/netstandard1.3/NModbus.dll", "lib/netstandard1.3/NModbus.xml", "lib/netstandard2.0/NModbus.dll", "lib/netstandard2.0/NModbus.xml", "nmodbus.3.0.81.nupkg.sha512", "nmodbus.nuspec"]}, "Portable.BouncyCastle/1.9.0": {"sha512": "eZZBCABzVOek+id9Xy04HhmgykF0wZg9wpByzrWN7q8qEI0Qen9b7tfd7w8VA3dOeesumMG7C5ZPy0jk7PSRHw==", "type": "package", "path": "portable.bouncycastle/1.9.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net40/BouncyCastle.Crypto.dll", "lib/net40/BouncyCastle.Crypto.xml", "lib/netstandard2.0/BouncyCastle.Crypto.dll", "lib/netstandard2.0/BouncyCastle.Crypto.xml", "portable.bouncycastle.1.9.0.nupkg.sha512", "portable.bouncycastle.nuspec"]}, "System.Buffers/4.5.1": {"sha512": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "type": "package", "path": "system.buffers/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Buffers.dll", "lib/net461/System.Buffers.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.1.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Formats.Asn1/6.0.0": {"sha512": "T6fD00dQ3NTbPDy31m4eQUwKW84s03z0N2C8HpOklyeaDgaJPa/TexP4/SkORMSOwc7WhKifnA6Ya33AkzmafA==", "type": "package", "path": "system.formats.asn1/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Formats.Asn1.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Formats.Asn1.dll", "lib/net461/System.Formats.Asn1.xml", "lib/net6.0/System.Formats.Asn1.dll", "lib/net6.0/System.Formats.Asn1.xml", "lib/netstandard2.0/System.Formats.Asn1.dll", "lib/netstandard2.0/System.Formats.Asn1.xml", "system.formats.asn1.6.0.0.nupkg.sha512", "system.formats.asn1.nuspec", "useSharedDesignerContext.txt"]}, "System.Memory/4.5.4": {"sha512": "1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "type": "package", "path": "system.memory/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.4.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.CompilerServices.Unsafe/4.5.3": {"sha512": "3TIsJhD1EiiT0w2CcDMN/iSSwnNnsrnbzeVHSKkaEgV85txMprmuO+Yq2AdSbeVGcg28pdNDTPK87tJhX7VFHw==", "type": "package", "path": "system.runtime.compilerservices.unsafe/4.5.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/net461/System.Runtime.CompilerServices.Unsafe.dll", "ref/net461/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.4.5.3.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ValueTuple/4.5.0": {"sha512": "okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "type": "package", "path": "system.valuetuple/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.ValueTuple.dll", "lib/net461/System.ValueTuple.xml", "lib/net47/System.ValueTuple.dll", "lib/net47/System.ValueTuple.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.ValueTuple.dll", "lib/netstandard1.0/System.ValueTuple.xml", "lib/netstandard2.0/_._", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net461/System.ValueTuple.dll", "ref/net47/System.ValueTuple.dll", "ref/netcoreapp2.0/_._", "ref/netstandard2.0/_._", "ref/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.valuetuple.4.5.0.nupkg.sha512", "system.valuetuple.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "OPCFoundation.NetStandard.Opc.Ua.Security.Certificates.Debug/1.0.0": {"type": "project", "path": "../Opc.Ua.Security.Certificates/Opc.Ua.Security.Certificates.csproj", "msbuildProject": "../Opc.Ua.Security.Certificates/Opc.Ua.Security.Certificates.csproj"}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.8": ["Microsoft.Bcl.HashCode >= 1.1.1", "Microsoft.Extensions.Logging.Abstractions >= 3.1.32", "NModbus >= 3.0.81", "Newtonsoft.Json >= 13.0.3", "OPCFoundation.NetStandard.Opc.Ua.Security.Certificates.Debug >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Downloads\\IoT_Cargill_Winform\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Core\\Opc.Ua.Core.csproj", "projectName": "OPCFoundation.NetStandard.Opc.Ua.Core.Debug", "projectPath": "D:\\Downloads\\IoT_Cargill_Winform\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Core\\Opc.Ua.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Downloads\\IoT_Cargill_Winform\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "net48", "projectReferences": {"D:\\Downloads\\IoT_Cargill_Winform\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Security.Certificates\\Opc.Ua.Security.Certificates.csproj": {"projectPath": "D:\\Downloads\\IoT_Cargill_Winform\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Security.Certificates\\Opc.Ua.Security.Certificates.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net48": {"targetAlias": "net48", "dependencies": {"Microsoft.Bcl.HashCode": {"target": "Package", "version": "[1.1.1, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[3.1.32, )"}, "NModbus": {"target": "Package", "version": "[3.0.81, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}}