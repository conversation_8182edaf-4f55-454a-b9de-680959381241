﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Windows.Forms;
using WindowsFormsCargill.Constant;
using System.IO;
using static WindowsFormsCargill.DB.TempOpcMsgDeviceDb;

namespace WindowsFormsCargill.DB
{
    public enum CONNECTION_STATE
    {
        DISCONNECT = 0,
        CONNECTED = 1
    }
    public enum COMMAND
    {
        GETINFOR=21,
        RELOAD_CONFIG=31
    }
    public class MessageSocket
    {

        public class Header
        {
            public COMMAND cmd { get; set; }
        }
        public class commandGetInfo
        {
            public COMMAND cmd { get; set; }
            public commandGetInfo()
            {
                cmd = COMMAND.GETINFOR;
            }
        }
        public class commandReload
        {
            public COMMAND cmd { get; set; }
            public commandReload()
            {
                cmd = COMMAND.RELOAD_CONFIG;
            }
        }
        // respond getinfo
        public class GatewayState
        {
            public int gatewayId { get; set; }
            public CONNECTION_STATE ConnectionState { get; set; }
        }
        public class DeviceState
        {
            public int deviceId { get; set; }
            public CONNECTION_STATE ConnectionState { get; set; }

            public List<TelemetryData> listTelemetry { get; set; }
        }
        public class respondGetInfo
        {
            public COMMAND command { get; set; }
            public List<GatewayState> listGatewayState;
            public List<DeviceState> listDevState;
            public respondGetInfo()
            {
                command = COMMAND.GETINFOR;
                listGatewayState = new List<GatewayState>();
                listDevState = new List<DeviceState>();
            }

        }
    }
    public class ApiDatabase
    {
        public string connectionString;//= "Data Source=localhost;Initial Catalog=IOTV1;Integrated Security=True";

        DB.TelemetryTableDb telemetryTable;
        DB.DeviceInfoTableDb deviceInfoTable;
        DB.GatewayInfoTableDb gatewayInfoTable;

        DB.TempOpcMsgDeviceDb tempOpcMsgDevicetable;
        DB.OpcMsgDB opcConMsgTable;
        DB.OpcMsgVarDB opcParaTable;

        DB.EventDb eventTable;
        DB.RuleConfigDb ruleConfigTable;

        DB.WidgetSerialConfigDb serialPlotTable;
        DB.WidgetSerial2ConfigDb serial2PlotTable; 
        DB.WidgetSerialBarConfigDb serialBarPlotTable;
        DB.WidgetBarValueConfigDb barValueTable;
        DB.WidgetPieConfigDb pieConfigTable;
        DB.WidgetBarConfigDb barTable;

        DB.DashboardConfigDb dashboardTable;
        public ApiDatabase()
        {
            ReadJsonFile readJsonFile = new ReadJsonFile();
            string IPAddress = readJsonFile.ReadFromJsonIOTFile("IPAddress");
            string UserName= readJsonFile.ReadFromJsonIOTFile("UserName");
            string Password = readJsonFile.ReadFromJsonIOTFile("Password");
            string Port = readJsonFile.ReadFromJsonIOTFile("Port");
            string PathExtend = readJsonFile.ReadFromJsonIOTFile("PathExtend");
            if (IPAddress == "" || IPAddress == "127.0.0.1" || IPAddress == "localhost")
            {
                // Check path extend
                if(PathExtend == "") connectionString = "Data Source=localhost;Initial Catalog=IOTV1;Integrated Security=True;";
                else connectionString = "Data Source=localhost\\" + PathExtend + ";Initial Catalog=IOTV1;Integrated Security=True;";
            }
            else
            {
                if (Password == "")
                {
                    // Check path extend port 1433
                    if (PathExtend == "") connectionString = "Data Source=" + IPAddress + "," + Port + ";Initial Catalog = IOTV1;Integrated Security=true;";// + "User ID =" + UserName;
                    else connectionString = "Data Source=" + IPAddress + "\\" + PathExtend + "," + Port + ";Initial Catalog = IOTV1;Integrated Security=true;";// + "User ID =" + UserName;
                }
                else
                {
                    // Check path extend
                    connectionString = "Data Source=" + IPAddress + "," + Port + ";Initial Catalog = IOTV1;" + "User ID =" + UserName + ",Password =" + Password;
                }
            }

            // Check connect before update connection for all table
            bool checkConnectionDatabase = false;
            try
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    // Try to open the connection
                    connection.Open();
                    checkConnectionDatabase = true;
                }              
            }
            catch (SqlException ex)
            {
                string connectionString;
                ///////////////////////////
                string sqlFilePath = @"C:\IOTCargill\Database\CreateDatabase.sql";
                if(IPAddress == "127.0.0.1")
                {
                    connectionString = "Server=" + "localhost" + @"\" + PathExtend + "; Integrated Security=true;";
                }     
                else
                // Chuỗi kết nối tới SQL Server
                {
                    connectionString = "Server=" + IPAddress + @"\" + PathExtend + "; Integrated Security=true;";
                }    

                try
                {
                    // Đọc nội dung file SQL
                    string sqlScript = File.ReadAllText(sqlFilePath);

                    // Kết nối tới SQL Server
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        connection.Open();

                        // Tách các lệnh trong file SQL (nếu có nhiều lệnh cách nhau bởi GO)
                        string[] commands = sqlScript.Split(new[] { "GO" }, StringSplitOptions.RemoveEmptyEntries);

                        foreach (string commandText in commands)
                        {
                            if (!string.IsNullOrWhiteSpace(commandText))
                            {
                                using (SqlCommand command = new SqlCommand(commandText, connection))
                                {
                                    command.ExecuteNonQuery();
                                    Console.WriteLine("Executed: " + commandText);
                                }
                            }
                        }

                        connection.Close();
                        Console.WriteLine("SQL script executed successfully.");
                    }
                }
                catch (Exception exx)
                {
                    MessageBox.Show(exx.Message);
                }

                try
                {
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        // Try to open the connection
                        connection.Open();
                        checkConnectionDatabase = true;
                    }
                }
                catch
                {
                    // If the database doesn't exist or there is an issue with the connection
                    MessageBox.Show("Connection database failed. The application will be closed!");
                    checkConnectionDatabase = false;
                    Environment.Exit(0); // exit app
                }
                ///////////////////////////


                
            }

            if(checkConnectionDatabase == true)
            {   
                telemetryTable = new TelemetryTableDb(connectionString);
                deviceInfoTable = new DeviceInfoTableDb(connectionString);
                gatewayInfoTable = new GatewayInfoTableDb(connectionString);
                tempOpcMsgDevicetable = new TempOpcMsgDeviceDb(connectionString);
                opcConMsgTable = new OpcMsgDB(connectionString);
                opcParaTable = new OpcMsgVarDB(connectionString);

                eventTable = new EventDb(connectionString);
                ruleConfigTable = new RuleConfigDb(connectionString);

                serialPlotTable = new WidgetSerialConfigDb(connectionString);
                serial2PlotTable = new WidgetSerial2ConfigDb(connectionString);
                serialBarPlotTable = new WidgetSerialBarConfigDb(connectionString);
                barValueTable = new WidgetBarValueConfigDb(connectionString);
                pieConfigTable = new WidgetPieConfigDb(connectionString);
                barTable = new WidgetBarConfigDb(connectionString);

                dashboardTable = new DashboardConfigDb(connectionString);
            }
        }
        // dash board

        public Dashboard_InfoDb Read_DashboardById(Constant.Dashboard dashboardId)
        {
            return dashboardTable.ReadById((int)dashboardId);
        }
        public void Update_DashboardInfo(Dashboard_InfoDb newDashboard)
        {
            dashboardTable.Update(newDashboard);
        }
        // Telemetry table

        public List<TelemetryData> ReadLatestTelemetry_ByDeviceId(int deviceId)
        {
            return telemetryTable.ReadLatestAll_ByDeviceId(deviceId);
        }

        public void AddListTelemetry(List<TelemetryData> listTelemetry)
        {
            telemetryTable.Add(listTelemetry);
        }

        public TelemetryData ReadLatestTelemetry_ByDeviceIdAndName(int deviceId, string Name)
        {
            return telemetryTable.ReadLastestValue_ByDeviceID(deviceId, Name);
        }
        public List<TelemetryData> ReadListTelemetry_ByNumbeAndDeviceID(string Name, int deviceId, int Number)
        {
            return telemetryTable.ReadNumbeByDeviceID(Name, deviceId, Number);
        }
        public List<TelemetryData> ReadListTelemetry_ByNumbeAndDeviceID_ForDay(string Name, int deviceId, int Day)
        {
            return telemetryTable.ReadNumbeByDeviceID_ForDay(Name, deviceId, Day);
        }
        // for power
        public List<TelemetryData> ReadListTelemetry_ByTimeInt(string Name, int deviceId, DateTime startTime, DateTime StopTime, int Limit)
        {
            return telemetryTable.ReadByDeviceIDAndBetweenTime(Name, deviceId, startTime, StopTime, Limit);
        }
        public TelemetryData ReadTelemetry_ByTimeDay(string Name, int deviceId, DateTime time)
        {
            return telemetryTable.ReadByDeviceIDAndMinusTime(Name, deviceId, time);
        }
        public TelemetryData ReadTelemetry_ByTimeDay_V2(string Name, int deviceId, DateTime time) // + 5 minutes
        {
            return telemetryTable.ReadByDeviceIDAndAddTime(Name, deviceId, time);
        }
        public List<TelemetryData> ReadListTelemetry_ByTimeDayInterval(string Name, int deviceId, DateTime startTime, DateTime StopTime,Int64 Interval, int Limit)
        {
            return telemetryTable.ReadByDeviceIDAndBetweenTimeInterval(Name, deviceId, startTime, StopTime, Interval, Limit);
        }
        public List<TelemetryData> ReadListTelemetry_ByTimeDayInterval_V2(string Name, int deviceId, DateTime startTime, DateTime StopTime, Int64 Interval, int Limit)
        {
            return telemetryTable.ReadByDeviceIDAndBetweenTimeInterval_V2(Name, deviceId, startTime, StopTime, Interval, Limit);
        }
        // device table
        public Device_Info ReadDeviceInfo_ById(int deviceId)
        {
            return deviceInfoTable.ReadById(deviceId);
        }
        public List<Device_Info> ReadDeviceInfo_ByName(string Name)
        {
            return deviceInfoTable.ReadDevByName(Name);
        }
        public List<Device_Info> ReadAllDeviceInfo()
        {
            return deviceInfoTable.ReadAll();
        }
        public List<Device_Info> ReadDeviceInfo_ByType(Device_Application deviceType)
        {
            return deviceInfoTable.ReadDevByType(deviceType);
        }
        public Device_Info AddDeviceInfo(Device_Info newDevice)
        {
            return
            deviceInfoTable.Add(newDevice);
        }
        public void DeleteDeviceInfo_ByGatewayID(int gatewayId)
        {
            deviceInfoTable.DeleteByGatewayID(gatewayId);
        }
        public void DeleteDeviceInfo_ById(int deviceId)
        {
            tempOpcMsgDevicetable.DeleteByDeviceId(deviceId);
            deviceInfoTable.DeleteDeviceByID(deviceId);
        }

        public void UpdateDeviceInfo(Device_Info dev)
        {
            deviceInfoTable.UpdateDevice(dev);
        }
        public void UpdateDevicePosition(int id, float xpos, float ypos)
        {
            deviceInfoTable.UpdateDevicePosition( id,  xpos,  ypos);
        }
        // Gateway Table
        public void AddGatewayInfo(Gateway_Info gateway)
        {
            gatewayInfoTable.Add(gateway);
        }
        public void UpdateGatewayInfo(Gateway_Info gateway)
        {
            gatewayInfoTable.UpdateGateway(gateway);
        }
        public List<Gateway_Info> ReadAllGatewayInfo()
        {
            return gatewayInfoTable.ReadAll();
        }
        public void DeleteGatewayInfo_ById(int gatewayId)
        {
            gatewayInfoTable.DeleteById(gatewayId);
        }

        // Opc msg 
        public List<MsgOpc_InfoDataDb> ReadAllMsgOpcInfo()
        {
            return opcConMsgTable.ReadAll();
        }
        public void AddMsgOpcInfo(MsgOpc_InfoDataDb newNode)
        {
            opcConMsgTable.Add(newNode);
        }
        public void UpdateMsgOpcInfo(MsgOpc_InfoDataDb newNode)
        {
            opcConMsgTable.UpdateMsgInfo(newNode);
        }
        public List<MsgOpc_InfoDataDb> ReadOpcMsgInfo_ByGatewayId(int gatewayId)
        {
            return opcConMsgTable.ReadOpcMsgInfo_ByGatewayId(gatewayId);
        }
        public List<MsgOpc_InfoDataDb> ReadOpcMsgInfo_ByDeviceId(int deviceId)
        {
            List<MsgOpc_InfoDataDb> listMsginfo = new List<MsgOpc_InfoDataDb>();

            List<TempOpcMsgDev_InfoDataDb> listTemp = tempOpcMsgDevicetable.ReadByDeviceId(deviceId);
            for( int i=0; i< listTemp.Count; i++)
            {
                MsgOpc_InfoDataDb msg = opcConMsgTable.ReadById(listTemp[i].opcMsgId);
                listMsginfo.Add(msg);
            }
            return listMsginfo;
        }
        
        public void AddMsgOpcInfo_ToDevice(int deviceId, int opcMsgId)
        {
            TempOpcMsgDev_InfoDataDb tempMsg = new TempOpcMsgDev_InfoDataDb();
            tempMsg.deviceId = deviceId;
            tempMsg.opcMsgId = opcMsgId;
            tempOpcMsgDevicetable.Add(tempMsg);
        }

        public void DeleteTempOpcMsgDev_ByOpcMsgId(int opcMsgId)
        {
            tempOpcMsgDevicetable.DeleteByOpcmsgId(opcMsgId);
        }
        public void DeleteOpcMsg_ByOpcMsgId(int opcMsgId)
        {
            opcConMsgTable.DeleteByID(opcMsgId);
            tempOpcMsgDevicetable.DeleteByOpcmsgId(opcMsgId);
            opcParaTable.DeleteByOpcMsgID(opcMsgId);
        }
        // opc para
        public List<MsgOpcVar_InfoDataDb> ReadAllOpcVar_ByDeviceId(int deviceId)
        {
            List<MsgOpcVar_InfoDataDb> listAllVar = new List<MsgOpcVar_InfoDataDb>();

            // first read all msg
            List<MsgOpc_InfoDataDb>  listMsg = ReadOpcMsgInfo_ByDeviceId(deviceId);
            for( int i=0;i< listMsg.Count; i++)
            {
                List<MsgOpcVar_InfoDataDb> listVar = ReadVarInfo_ByMsgId(listMsg[i].ID);
                listAllVar.AddRange(listVar);
            }
            return listAllVar;
        }

        public List<MsgOpcVar_InfoDataDb> ReadVarInfo_ByMsgId(int msgId)
        {
            return opcParaTable.ReadOpcMsgInfo_ByMsgId(msgId);
        }
        public void AddOpcVarInfo(MsgOpcVar_InfoDataDb newVar)
        {
            opcParaTable.Add(newVar);
        }
        public void AddNodeForDevice(string addressNode, int gatewayID, int timeGet, int deviceID)
        {
            opcParaTable.AddNodeForDevice(addressNode, gatewayID, timeGet, deviceID);
        }
        public void DeleteOpcVarInfo_ById(int varId)
        {
            opcParaTable.DeleteByID(varId);
        }
        
        // Event
        public void AddEvent(EventData_InfoDb newEvent)
        {
            eventTable.Add(newEvent);
        }
        public List<EventData_InfoDb> ReaddLastestEventByDeviceIdLimit(int  deviceId, int Limit)
        {
           return  eventTable.ReadLastesByDeviceIdLimit(deviceId, Limit);
        }

        public List<EventData_InfoDb> ReaddLastestEventLimit( int Limit)
        {
            return eventTable.ReadLastesByLimit( Limit);
        }
        // Rule
        public void AddRuleConfig(RuleConfig_InfoDb newRule)
        {
            ruleConfigTable.Add(newRule);
        }
        public void DeleteRuleConfig(int rulerId)
        {
            ruleConfigTable.DeleteByID(rulerId);
        }
        public List<RuleConfig_InfoDb> ReadAllRuleConfig()
        {
            return ruleConfigTable.ReadAll();
        }
        public List<RuleConfig_InfoDb> ReadRuleConfig_ByDeviceId(int DeviceId)
        {
            return ruleConfigTable.ReadByDeviceId(DeviceId);
        }
        // config widget serial
        public List<WidgetSerialPlot_InfoDb> ReadWidgetSerialPlot_ByDashboardId(Constant.Dashboard dashboardId)
        {
            return serialPlotTable.ReadByDashboardId((int)dashboardId);
        }
        public WidgetSerialPlot_InfoDb ReadWidgetSerialPlot_ById(int serialPlotI)
        {
            return serialPlotTable.ReadById(serialPlotI);
        }
        public void UpdateWidgetSerialPlot(WidgetSerialPlot_InfoDb newSerialPlot)
        {
            serialPlotTable.Update(newSerialPlot);

        }

        // config widget serial 2
        public List<WidgetSerial2Plot_InfoDb> ReadWidgetSerial2Plot_ByDashboardId(Constant.Dashboard dashboardId)
        {
            return serial2PlotTable.ReadByDashboardId((int)dashboardId);
        }
        public WidgetSerial2Plot_InfoDb ReadWidgetSerial2Plot_ById(int serial2PlotI)
        {
            return serial2PlotTable.ReadById(serial2PlotI);
        }
        public void UpdateWidgetSerial2Plot(WidgetSerial2Plot_InfoDb newSerial2Plot)
        {
            serial2PlotTable.Update(newSerial2Plot);
        }

        // config widget serial Bar
        public List<WidgetSerialBarPlot_InfoDb> ReadWidgetSerialBarPlot_ByDashboardId(Constant.Dashboard dashboardId)
        {
            return serialBarPlotTable.ReadByDashboardId((int)dashboardId);
        }
        public WidgetSerialBarPlot_InfoDb ReadWidgetSerialBarPlot_ById(int serialPlotI)
        {
            return serialBarPlotTable.ReadById(serialPlotI);
        }
        public void UpdateWidgetSerialBarPlot(WidgetSerialBarPlot_InfoDb newSerialPlot)
        {
            serialBarPlotTable.Update(newSerialPlot);

        }
        // config barvalue
        public List<WidgetBarValue_InfoDb> ReadWidgetBarValue_ByDashboardId(Constant.Dashboard dashboardId)
        {
            return barValueTable.ReadByDashboardId((int)dashboardId);
        }
        public WidgetBarValue_InfoDb ReadWidgetBarValue_ById(int BarValueId)
        {
            return barValueTable.ReadById(BarValueId);
        }
        public void UpdateWidgetBarValue(WidgetBarValue_InfoDb newBarvalue)
        {
            barValueTable.Update(newBarvalue);
        }
        // pie config pieConfigTable
        public List<WidgetPieConfig_InfoDb> ReadWidgetPieConfig_ByDashboardId(Constant.Dashboard dashboardId)
        {
            return pieConfigTable.ReadByDashboardId((int)dashboardId);
        }
        public WidgetPieConfig_InfoDb ReadWidgetPieConfig_ById(int BarValueId)
        {
            return pieConfigTable.ReadById(BarValueId);
        }
        public void UpdatePieconfig(WidgetPieConfig_InfoDb newBarvalue)
        {
            pieConfigTable.Update(newBarvalue);
        }
        // config bar 
        public List<WidgetBarConfig_InfoDb> ReadWidgetBar_ByDashboardId(Constant.Dashboard dashboardId)
        {
            return barTable.ReadByDashboardId((int)dashboardId);
        }
        public WidgetBarConfig_InfoDb ReadWidgetBar_ById(int BarId)
        {
            return barTable.ReadById(BarId);
        }
        public void UpdateWidgetBarconfig(WidgetBarConfig_InfoDb newBar)
        {
            barTable.Update(newBar);
        }
    }
}
