//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: System.Reflection.AssemblyCompanyAttribute("OPC Foundation")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Debug")]
[assembly: System.Reflection.AssemblyCopyrightAttribute("Copyright © 2004-2022 OPC Foundation, Inc")]
[assembly: System.Reflection.AssemblyDescriptionAttribute("OPC UA Client Class Library")]
[assembly: System.Reflection.AssemblyProductAttribute("OPC UA .NET Standard Library")]
[assembly: System.Reflection.AssemblyTitleAttribute("Opc.Ua.Client")]
[assembly: System.Reflection.AssemblyMetadataAttribute("RepositoryUrl", "https://github.com/OPCFoundation/UA-.NETStandard")]
[assembly: System.Resources.NeutralResourcesLanguageAttribute("en-US")]

// Generated by the MSBuild WriteCodeFragment class.

