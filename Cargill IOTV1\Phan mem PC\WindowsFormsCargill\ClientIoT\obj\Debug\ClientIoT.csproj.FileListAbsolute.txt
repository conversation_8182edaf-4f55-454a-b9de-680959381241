D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\obj\Debug\ClientIoT.csproj.AssemblyReference.cache
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\obj\Debug\App.g.cs
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\obj\Debug\ClientIoT_MarkupCompile.cache
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\libSkiaSharp.dylib
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\x86\libSkiaSharp.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\x64\libSkiaSharp.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\arm64\libSkiaSharp.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\x64\libSkiaSharp.so
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\arm\libSkiaSharp.so
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\arm64\libSkiaSharp.so
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\musl-x64\libSkiaSharp.so
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\ClientIoT.exe.config
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\ClientIoT.exe
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\ClientIoT.pdb
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\CargillServiceIOT.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\WindowsFormsCargill.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\System.Text.Json.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\Newtonsoft.Json.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\Opc.Ua.Client.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\Opc.Ua.Core.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\Opc.Ua.Security.Certificates.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\System.Memory.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\System.ValueTuple.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\System.Text.Encodings.Web.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\System.Numerics.Vectors.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\System.Threading.Tasks.Extensions.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\Microsoft.Bcl.AsyncInterfaces.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\System.Buffers.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\System.Runtime.CompilerServices.Unsafe.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\Microsoft.Extensions.Logging.Abstractions.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\Microsoft.Bcl.HashCode.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\System.Formats.Asn1.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\BouncyCastle.Crypto.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\WindowsFormsCargill.pdb
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\WindowsFormsCargill.dll.config
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\CargillServiceIOT.pdb
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\Newtonsoft.Json.xml
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\Opc.Ua.Client.pdb
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\Opc.Ua.Client.xml
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\Opc.Ua.Core.pdb
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\Opc.Ua.Core.xml
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\Opc.Ua.Security.Certificates.pdb
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\Opc.Ua.Security.Certificates.xml
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\System.Memory.xml
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\System.ValueTuple.xml
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\System.Numerics.Vectors.xml
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\bin\Debug\System.Buffers.xml
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\obj\Debug\ClientIoT.csproj.SuggestedBindingRedirects.cache
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\obj\Debug\ClientIoT.exe.config
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\obj\Debug\MainWindow.g.cs
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\obj\Debug\ClientIoT_MarkupCompile.lref
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\obj\Debug\MainWindow.baml
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\obj\Debug\ClientIoT.g.resources
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\obj\Debug\ClientIoT.Properties.Resources.resources
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\obj\Debug\ClientIoT.csproj.GenerateResource.cache
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\obj\Debug\ClientIoT.csproj.CoreCompileInputs.cache
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\obj\Debug\ClientIoT.csproj.Up2Date
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\obj\Debug\ClientIoT.exe
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\WindowsFormsCargill\ClientIoT\obj\Debug\ClientIoT.pdb
