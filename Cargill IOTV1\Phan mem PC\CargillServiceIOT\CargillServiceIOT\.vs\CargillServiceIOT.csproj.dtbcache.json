{"RootPath": "D:\\Downloads\\Cargill IOTV1\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\CargillServiceIOT", "ProjectFileName": "CargillServiceIOT.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "App.cs"}, {"SourceFile": "App.Designer.cs"}, {"SourceFile": "CargillServiceIOT.cs"}, {"SourceFile": "CargillServiceIOT.Designer.cs"}, {"SourceFile": "Connection\\ConnectionWebsocket.cs"}, {"SourceFile": "Connection\\Connection_PLC_OPC.cs"}, {"SourceFile": "Core\\ConnectionCore.cs"}, {"SourceFile": "Core\\DeviceProcess.cs"}, {"SourceFile": "Core\\RuleProcess.cs"}, {"SourceFile": "DB\\ApiDatabase.cs"}, {"SourceFile": "DB\\Connection\\OpcMsgDB.cs"}, {"SourceFile": "DB\\Connection\\OpcMsgVarDB.cs"}, {"SourceFile": "DB\\Connection\\TempOpcMsgDeviceDb.cs"}, {"SourceFile": "DB\\Constant_Database.cs"}, {"SourceFile": "DB\\DashboardConfig\\DashboardConfigDb.cs"}, {"SourceFile": "DB\\DashboardConfig\\WidgetBarConfig.cs"}, {"SourceFile": "DB\\DashboardConfig\\WidgetBarValueConfigDb.cs"}, {"SourceFile": "DB\\DashboardConfig\\WidgetPieConfigDb.cs"}, {"SourceFile": "DB\\DashboardConfig\\WidgetSerial2ConfigDb.cs"}, {"SourceFile": "DB\\DashboardConfig\\WidgetSerialBarConfigDb.cs"}, {"SourceFile": "DB\\DashboardConfig\\WidgetSerialConfigDb.cs"}, {"SourceFile": "DB\\DeviceInfoDb.cs"}, {"SourceFile": "DB\\EventDb.cs"}, {"SourceFile": "DB\\GatewayInfoDb.cs"}, {"SourceFile": "DB\\RuleConfigDb.cs"}, {"SourceFile": "DB\\TelemetryDb.cs"}, {"SourceFile": "OPC_UAClientHelperAPI.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "ReadJsonFile.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.8.AssemblyAttributes.cs"}], "References": [{"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\Downloads\\Cargill IOTV1\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\packages\\Newtonsoft.Json.13.0.3\\lib\\net45\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\Downloads\\Cargill IOTV1\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Client\\bin\\Debug\\net48\\Opc.Ua.Client.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\Downloads\\Cargill IOTV1\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Client\\bin\\Debug\\net48\\Opc.Ua.Client.dll"}, {"Reference": "D:\\Downloads\\Cargill IOTV1\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Configuration\\bin\\Debug\\net48\\Opc.Ua.Configuration.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\Downloads\\Cargill IOTV1\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Configuration\\bin\\Debug\\net48\\Opc.Ua.Configuration.dll"}, {"Reference": "D:\\Downloads\\Cargill IOTV1\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Core\\bin\\Debug\\net48\\Opc.Ua.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\Downloads\\Cargill IOTV1\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Core\\bin\\Debug\\net48\\Opc.Ua.Core.dll"}, {"Reference": "D:\\Downloads\\Cargill IOTV1\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Security.Certificates\\bin\\Debug\\net48\\Opc.Ua.Security.Certificates.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\Downloads\\Cargill IOTV1\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\Opc.Ua.Security.Certificates\\bin\\Debug\\net48\\Opc.Ua.Security.Certificates.dll"}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Net.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "D:\\Downloads\\Cargill IOTV1\\Cargill IOTV1\\Phan mem PC\\CargillServiceIOT\\CargillServiceIOT\\bin\\Debug\\CargillServiceIOT.dll", "OutputItemRelativePath": "CargillServiceIOT.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}