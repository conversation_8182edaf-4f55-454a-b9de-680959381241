﻿#pragma checksum "..\..\..\..\UI\MetterPage\Sub_Metter.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "CD800FB67A7C3B56746FA34D57697808EA15A81EDB2796561BD11B7960D3C44A"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.DataVisualization.Charting;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using WindowsFormsCargill.UI.MetterPage;


namespace WindowsFormsCargill.UI.MetterPage {
    
    
    /// <summary>
    /// Sub_Metter
    /// </summary>
    public partial class Sub_Metter : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 39 "..\..\..\..\UI\MetterPage\Sub_Metter.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox VisibilityChart;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\..\UI\MetterPage\Sub_Metter.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image XamlImagepath;
        
        #line default
        #line hidden
        
        
        #line 46 "..\..\..\..\UI\MetterPage\Sub_Metter.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label XamlRecState;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\..\UI\MetterPage\Sub_Metter.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox XamlListParaPlot_ComboBox;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\..\UI\MetterPage\Sub_Metter.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox XamlMaxValue_TextBox;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\UI\MetterPage\Sub_Metter.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Forms.DataVisualization.Charting.Chart XamlPlot;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\UI\MetterPage\Sub_Metter.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid XamlMeterPara;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/WindowsFormsCargill;component/ui/metterpage/sub_metter.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\UI\MetterPage\Sub_Metter.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.VisibilityChart = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 2:
            this.XamlImagepath = ((System.Windows.Controls.Image)(target));
            return;
            case 3:
            this.XamlRecState = ((System.Windows.Controls.Label)(target));
            return;
            case 4:
            this.XamlListParaPlot_ComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 49 "..\..\..\..\UI\MetterPage\Sub_Metter.xaml"
            this.XamlListParaPlot_ComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ComboBox_listParaPlot_Changer);
            
            #line default
            #line hidden
            return;
            case 5:
            this.XamlMaxValue_TextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            
            #line 54 "..\..\..\..\UI\MetterPage\Sub_Metter.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveSub_BtOnClick_EventHandle);
            
            #line default
            #line hidden
            return;
            case 7:
            this.XamlPlot = ((System.Windows.Forms.DataVisualization.Charting.Chart)(target));
            return;
            case 8:
            this.XamlMeterPara = ((System.Windows.Controls.DataGrid)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

