﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace WindowsFormsCargill.DB
{
    public partial class Device_Info
    {
        public int ID { get; set; }
        public int gatewayID { get; set; }
        public Constant.Device_Application Type { get; set; }
        public Constant.Device_Model Model { get; set; }
        public string Name { get; set; }
        public float MaxValue { get; set; }
        public float XPos { get; set; }
        public float YPos { get; set; }
        public string MainPara { get; set; }
        public int TimeAlive { get; set; }
        public DateTime TimeActive { get; set; }
        public Constant.Device_ConnectionType ConnectionType { get; set; }
        public Device_Info()
        {
            XPos = 0;
            YPos = 0;
        }
    }

    public class DeviceInfoTableDb
    {


        private string connectionString;
        private string TableString;

        public DeviceInfoTableDb( string connection)
        {
                {// Creat variables
                    connectionString = connection;//"Data Source= EnegryDatabase.db; Version = 3; New = True; Compress = True";
                    TableString = "DevicesTable";
                    //CreateTable();
                }
        }

        public void CreateTable()
        {
            try
            {
                // Create a new database connection:
                using (SqlConnection sqlConn = new SqlConnection(connectionString))
                {
                    sqlConn.Open();
                    if (sqlConn.State == ConnectionState.Open)
                    {
                        using (SqlCommand sqlCmd = sqlConn.CreateCommand())
                        {
                            // Check if the table exists
                            sqlCmd.CommandText = $@"
                            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name = '{TableString}' AND xtype = 'U')
                            BEGIN
                                CREATE TABLE {TableString} (
                                    ID INT PRIMARY KEY IDENTITY,
                                    gatewayID INT,
                                    Type INT,
                                    Model INT,
                                    Name NVARCHAR(MAX),
                                    MaxValue FLOAT,
                                    XPos FLOAT,
                                    YPos FLOAT,
                                    MainPara NVARCHAR(MAX),
                                    TimeAlive INT,
                                    TimeActive DATETIME,
                                    ConnectionType INT
                                )
                            END";
                            sqlCmd.ExecuteNonQuery();
                        }
                    }
                }

            }
            catch (Exception err)
            {
                StackFrame stackFrame = new StackTrace(1).GetFrame(1);
                MessageBox.Show(stackFrame.GetFileName() + "\n" + stackFrame.GetMethod().ToString() + "\n" + stackFrame.GetFileLineNumber().ToString() + "\n" + err.ToString());
            }
        }
        public List<Device_Info> ReadAll()
        {
            List<Device_Info> responseBuf = new List<Device_Info>();
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        string commandStringBuf = $"SELECT * FROM {TableString}";
                        using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                        {
                            using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                            {
                                // Call Read before accessing data.
                                while (readerBuf.Read())
                                {
                                    if (readerBuf.FieldCount == 12)
                                    {
                                        Device_Info deviceInfoBuf = new Device_Info();

                                        deviceInfoBuf.ID = readerBuf.GetInt32(0);
                                        deviceInfoBuf.gatewayID = readerBuf.GetInt32(1);
                                        deviceInfoBuf.Type = (Constant.Device_Application)readerBuf.GetInt32(2); // Changed to GetInt32
                                        deviceInfoBuf.Model = (Constant.Device_Model)readerBuf.GetInt32(3); // Changed to GetInt32
                                        deviceInfoBuf.Name = readerBuf["Name"].ToString();
                                        deviceInfoBuf.MaxValue = readerBuf.GetFloat(5);
                                        deviceInfoBuf.XPos = readerBuf.GetFloat(6);
                                        deviceInfoBuf.YPos = readerBuf.GetFloat(7);
                                        deviceInfoBuf.MainPara = readerBuf["MainPara"].ToString();
                                        deviceInfoBuf.TimeAlive = readerBuf.GetInt32(9);
                                        deviceInfoBuf.TimeActive = readerBuf.GetDateTime(10);
                                        deviceInfoBuf.ConnectionType = (Constant.Device_ConnectionType)readerBuf.GetInt32(11);

                                        responseBuf.Add(deviceInfoBuf);
                                    }
                                }
                                // Call Close when done reading.
                                readerBuf.Close();
                            }
                        }
                        sqlConnectionBuf.Close();
                    }
                }

            }
            catch (Exception err)
            {
                MessageBox.Show(" .", err.ToString());
            }
            return responseBuf;
        }
        public List<int> ReadAllID()
        {
            List<int> responseBuf = new List<int>();
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        try
                        {
                            string commandStringBuf = "SELECT TOP 1000 ID FROM "+ TableString;
                            using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                            {
                                using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                                {
                                    // Call Read before accessing data.
                                    while (readerBuf.Read())
                                    {
                                        if (readerBuf.FieldCount == 1)
                                        {
                                            responseBuf.Add(readerBuf.GetInt32(0));
                                        }
                                    }
                                    // Call Close when done reading.
                                    readerBuf.Close();
                                }
                            }

                        }
                        catch (Exception err)
                        {
                            StackFrame stackFrame = new StackTrace(1).GetFrame(1);
                            MessageBox.Show(stackFrame.GetFileName() + "\n" + stackFrame.GetMethod().ToString() + "\n" + stackFrame.GetFileLineNumber().ToString() + "\n" + err.ToString());
                        }
                        sqlConnectionBuf.Close();
                    }
                }
            }
            catch (Exception err)
            {
                StackFrame stackFrame = new StackTrace(1).GetFrame(1);
                MessageBox.Show(stackFrame.GetFileName() + "\n" + stackFrame.GetMethod().ToString() + "\n" + stackFrame.GetFileLineNumber().ToString() + "\n" + err.ToString());
            }
            return responseBuf;
        }

        public Device_Info ReadById(int deviceId)
        {
            Device_Info responseBuf = new Device_Info();
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        string commandStringBuf = $"SELECT * FROM {TableString}  WHERE   ID  = {deviceId}";
                        using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                        {
                            using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                            {
                                // Call Read before accessing data.
                                while (readerBuf.Read())
                                {
                                    if (readerBuf.FieldCount == 12)
                                    {
                                        responseBuf.ID = readerBuf.GetInt32(0);
                                        responseBuf.gatewayID = readerBuf.GetInt32(1);
                                        responseBuf.Type = (Constant.Device_Application)readerBuf.GetInt32(2); // Sử dụng GetInt32 cho các trường số nguyên
                                        responseBuf.Model = (Constant.Device_Model)readerBuf.GetInt32(3);
                                        responseBuf.Name = readerBuf["Name"].ToString();
                                        responseBuf.MaxValue = readerBuf.GetFloat(5);
                                        responseBuf.XPos = readerBuf.GetFloat(6);
                                        responseBuf.YPos = readerBuf.GetFloat(7);
                                        responseBuf.MainPara = readerBuf["MainPara"].ToString();
                                        responseBuf.TimeAlive = readerBuf.GetInt32(9);
                                        responseBuf.TimeActive = readerBuf.GetDateTime(10);
                                        responseBuf.ConnectionType = (Constant.Device_ConnectionType)readerBuf.GetInt32(11);
                                    }
                                }
                                // Call Close when done reading.
                                readerBuf.Close();
                            }
                        }

                        sqlConnectionBuf.Close();
                    }
                }
            }
            catch (Exception err)
            {
                MessageBox.Show(" .", err.ToString());
            }
            return responseBuf;
        }
        public List<Device_Info> ReadDevByName(string name)
        {
            List<Device_Info> responseBuf = new List<Device_Info>();
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        try
                        {
                            string commandStringBuf = $"SELECT * FROM {TableString} WHERE   Name  = '{name}'";
                            using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                            {
                                using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                                {
                                    while (readerBuf.Read())
                                    {
                                        if (readerBuf.FieldCount == 12)
                                        {
                                            Device_Info deviceInfoBuf = new Device_Info();

                                            deviceInfoBuf.ID = readerBuf.GetInt32(0);
                                            deviceInfoBuf.gatewayID = readerBuf.GetInt32(1);
                                            deviceInfoBuf.Type = (Constant.Device_Application)readerBuf.GetInt32(2); // Sử dụng GetInt32 cho các trường số nguyên
                                            deviceInfoBuf.Model = (Constant.Device_Model)readerBuf.GetInt32(3);
                                            deviceInfoBuf.Name = readerBuf["Name"].ToString();
                                            deviceInfoBuf.MaxValue = readerBuf.GetFloat(5);
                                            deviceInfoBuf.XPos = readerBuf.GetFloat(6);
                                            deviceInfoBuf.YPos = readerBuf.GetFloat(7);
                                            deviceInfoBuf.MainPara = readerBuf["MainPara"].ToString();
                                            deviceInfoBuf.TimeAlive = readerBuf.GetInt32(9);
                                            deviceInfoBuf.TimeActive = readerBuf.GetDateTime(10);
                                            deviceInfoBuf.ConnectionType = (Constant.Device_ConnectionType)readerBuf.GetInt32(11);

                                            responseBuf.Add(deviceInfoBuf);
                                        }
                                    }
                                    // Call Close when done reading.
                                    readerBuf.Close();
                                }
                            }

                        }
                        catch (Exception err)
                        {
                            StackFrame stackFrame = new StackTrace(1).GetFrame(1);
                            MessageBox.Show(stackFrame.GetFileName() + "\n" + stackFrame.GetMethod().ToString() + "\n" + stackFrame.GetFileLineNumber().ToString() + "\n" + err.ToString());
                        }
                        sqlConnectionBuf.Close();
                    }
                }
            }
            catch (Exception err)
            {
                StackFrame stackFrame = new StackTrace(1).GetFrame(1);
                MessageBox.Show(stackFrame.GetFileName() + "\n" + stackFrame.GetMethod().ToString() + "\n" + stackFrame.GetFileLineNumber().ToString() + "\n" + err.ToString());
            }
            return responseBuf;
        }
        public List<Device_Info> ReadDevByType(Constant.Device_Application Type)
        {
            List<Device_Info> responseBuf = new List<Device_Info>();
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        try
                        {
                            string commandStringBuf = "SELECT * FROM " + TableString + " WHERE   Type  ="+ (int)(Type) ;
                            using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                            {
                                using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                                {
                                    while (readerBuf.Read())
                                    {
                                        if (readerBuf.FieldCount == 12)
                                        {
                                            Device_Info deviceInfoBuf = new Device_Info();

                                            deviceInfoBuf.ID = readerBuf.GetInt32(0);
                                            deviceInfoBuf.gatewayID = readerBuf.GetInt32(1);
                                            deviceInfoBuf.Type = (Constant.Device_Application)readerBuf.GetInt32(2);
                                            deviceInfoBuf.Model = (Constant.Device_Model)readerBuf.GetInt32(3);
                                            deviceInfoBuf.Name = readerBuf.GetString(4); 
                                            deviceInfoBuf.MaxValue = readerBuf.GetFloat(5); 
                                            deviceInfoBuf.XPos = readerBuf.GetFloat(6); 
                                            deviceInfoBuf.YPos = readerBuf.GetFloat(7); 
                                            deviceInfoBuf.MainPara = readerBuf.GetString(8); 
                                            deviceInfoBuf.TimeAlive = readerBuf.GetInt32(9); 
                                            deviceInfoBuf.TimeActive = readerBuf.GetDateTime(10); 
                                            deviceInfoBuf.ConnectionType = (Constant.Device_ConnectionType)readerBuf.GetInt32(11); 

                                            responseBuf.Add(deviceInfoBuf);
                                        }
                                    }
                                    // Call Close when done reading.
                                    readerBuf.Close();
                                }
                            }
                        }
                        catch (Exception err)
                        {
                            StackFrame stackFrame = new StackTrace(1).GetFrame(1);
                            MessageBox.Show(stackFrame.GetFileName() + "\n" + stackFrame.GetMethod().ToString() + "\n" + stackFrame.GetFileLineNumber().ToString() + "\n" + err.ToString());
                        }
                        sqlConnectionBuf.Close();
                    }
                }
            }
            catch (Exception err)
            {
                StackFrame stackFrame = new StackTrace(1).GetFrame(1);
                MessageBox.Show(stackFrame.GetFileName() + "\n" + stackFrame.GetMethod().ToString() + "\n" + stackFrame.GetFileLineNumber().ToString() + "\n" + err.ToString());
            }
            return responseBuf;
        }
        //public Device_Info Add(Device_Info newDeviceInfo)
        //{
        //    Device_Info responseBuf = null;
        //    try
        //    {
        //        using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
        //        {
        //            sqlConnectionBuf.Open();
        //            if (sqlConnectionBuf.State == ConnectionState.Open)
        //            {
        //                try
        //                {
        //                    string commandStringBuf = "INSERT INTO " + TableString + " (gatewayID, Type, Model, Name, MaxValue, XPos, YPos, MainPara, " +
        //                        "TimeAlive, TimeActive, ConnectionType) VALUES (@gatewayID, @Type, @Model, @Name, @MaxValue, " +
        //                        "@XPos, @YPos, @MainPara, @TimeAlive, @TimeActive, @ConnectionType)";
        //                    using (SqlCommand commandBuf = new SqlCommand())
        //                    {
        //                        commandBuf.Connection = sqlConnectionBuf;
        //                        commandBuf.CommandText = commandStringBuf;
        //                        commandBuf.Parameters.AddWithValue("@gatewayID", newDeviceInfo.gatewayID);
        //                        commandBuf.Parameters.AddWithValue("@Type", (int)newDeviceInfo.Type);
        //                        commandBuf.Parameters.AddWithValue("@Model", (int)newDeviceInfo.Model);
        //                        commandBuf.Parameters.AddWithValue("@Name", newDeviceInfo.Name);
        //                        commandBuf.Parameters.AddWithValue("@MaxValue", newDeviceInfo.MaxValue);
        //                        commandBuf.Parameters.AddWithValue("@XPos", newDeviceInfo.XPos);
        //                        commandBuf.Parameters.AddWithValue("@YPos", newDeviceInfo.YPos);
        //                        commandBuf.Parameters.AddWithValue("@MainPara", "");
        //                        commandBuf.Parameters.AddWithValue("@TimeAlive", newDeviceInfo.TimeAlive);
        //                        commandBuf.Parameters.AddWithValue("@TimeActive", newDeviceInfo.TimeActive);
        //                        commandBuf.Parameters.AddWithValue("@ConnectionType", (int)newDeviceInfo.ConnectionType);

        //                        commandBuf.ExecuteNonQuery();
        //                        responseBuf = newDeviceInfo;
        //                    }
        //                }
        //                catch (Exception err)
        //                {
        //                    StackFrame stackFrame = new StackTrace(1).GetFrame(1);
        //                    MessageBox.Show(stackFrame.GetFileName() + "\n" + stackFrame.GetMethod().ToString() + "\n" + stackFrame.GetFileLineNumber().ToString() + "\n" + err.ToString());
        //                }
        //                sqlConnectionBuf.Close();
        //            }
        //        }

        //    }
        //    catch (Exception err)
        //    {
        //        StackFrame stackFrame = new StackTrace(1).GetFrame(1);
        //        MessageBox.Show(stackFrame.GetFileName() + "\n" + stackFrame.GetMethod().ToString() + "\n" + stackFrame.GetFileLineNumber().ToString() + "\n" + err.ToString());
        //    }
        //    return responseBuf;
        //}
        public Device_Info Add(Device_Info newDeviceInfo)
        {
            Device_Info responseBuf = null;
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        try
                        {
                            string commandStringBuf = "INSERT INTO " + TableString + " (gatewayID, Type, Model, Name, MaxValue, XPos, YPos, MainPara, " +
                                "TimeAlive, TimeActive, ConnectionType) " +
                                "VALUES (@gatewayID, @Type, @Model, @Name, @MaxValue, @XPos, @YPos, @MainPara, @TimeAlive, @TimeActive, @ConnectionType); " +
                                "SELECT SCOPE_IDENTITY();";

                            using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                            {
                                commandBuf.Parameters.AddWithValue("@gatewayID", newDeviceInfo.gatewayID);
                                commandBuf.Parameters.AddWithValue("@Type", (int)newDeviceInfo.Type);
                                commandBuf.Parameters.AddWithValue("@Model", (int)newDeviceInfo.Model);
                                commandBuf.Parameters.AddWithValue("@Name", newDeviceInfo.Name);
                                commandBuf.Parameters.AddWithValue("@MaxValue", newDeviceInfo.MaxValue);
                                commandBuf.Parameters.AddWithValue("@XPos", newDeviceInfo.XPos);
                                commandBuf.Parameters.AddWithValue("@YPos", newDeviceInfo.YPos);
                                commandBuf.Parameters.AddWithValue("@MainPara", "");
                                commandBuf.Parameters.AddWithValue("@TimeAlive", newDeviceInfo.TimeAlive);
                                commandBuf.Parameters.AddWithValue("@TimeActive", newDeviceInfo.TimeActive);
                                commandBuf.Parameters.AddWithValue("@ConnectionType", (int)newDeviceInfo.ConnectionType);

                                object result = commandBuf.ExecuteScalar();
                                if (result != null && int.TryParse(result.ToString(), out int insertedID))
                                {
                                    newDeviceInfo.ID = insertedID;
                                    responseBuf = newDeviceInfo;
                                }
                            }
                        }
                        catch (Exception err)
                        {
                            StackFrame stackFrame = new StackTrace(1).GetFrame(1);
                            MessageBox.Show(stackFrame.GetFileName() + "\n" + stackFrame.GetMethod().ToString() + "\n" + stackFrame.GetFileLineNumber().ToString() + "\n" + err.ToString());
                        }
                        sqlConnectionBuf.Close();
                    }
                }
            }
            catch (Exception err)
            {
                StackFrame stackFrame = new StackTrace(1).GetFrame(1);
                MessageBox.Show(stackFrame.GetFileName() + "\n" + stackFrame.GetMethod().ToString() + "\n" + stackFrame.GetFileLineNumber().ToString() + "\n" + err.ToString());
            }
            return responseBuf;
        }

        public int DeleteByGatewayID(int newGatewayID)
        {
            int responseBuf = 0;
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        try
                        {
                            string commandStringBuf = "DELETE FROM "+ TableString+ " WHERE gatewayID='" + newGatewayID.ToString() + "';";
                            using (SqlCommand commandBuf = new SqlCommand())
                            {
                                commandBuf.Connection = sqlConnectionBuf;
                                commandBuf.CommandText = commandStringBuf;

                                commandBuf.ExecuteNonQuery();
                                responseBuf = newGatewayID;  // Đảm bảo `responseBuf` và `newGatewayID` đã được định nghĩa và khởi tạo trước đó
                            }

                        }
                        catch (Exception err)
                        {
                            StackFrame stackFrame = new StackTrace(1).GetFrame(1);
                            MessageBox.Show(stackFrame.GetFileName() + "\n" + stackFrame.GetMethod().ToString() + "\n" + stackFrame.GetFileLineNumber().ToString() + "\n" + err.ToString());
                        }
                        sqlConnectionBuf.Close();
                    }
                }
            }
            catch (Exception err)
            {
                StackFrame stackFrame = new StackTrace(1).GetFrame(1);
                MessageBox.Show(stackFrame.GetFileName() + "\n" + stackFrame.GetMethod().ToString() + "\n" + stackFrame.GetFileLineNumber().ToString() + "\n" + err.ToString());
            }
            return responseBuf;
        }
        public int DeleteDeviceByID(int newDeviceID)
        {
            int responseBuf = 0;
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        try
                        {
                            string commandStringBuf = "DELETE FROM "+ TableString + " WHERE ID='" + newDeviceID.ToString() + "';";
                            using (SqlCommand commandBuf = new SqlCommand())
                            {
                                commandBuf.Connection = sqlConnectionBuf;
                                commandBuf.CommandText = commandStringBuf;

                                commandBuf.ExecuteNonQuery();
                                responseBuf = newDeviceID;  // Đảm bảo `responseBuf` và `newDeviceID` đã được định nghĩa và khởi tạo trước đó
                            }

                        }
                        catch (Exception err)
                        {
                            StackFrame stackFrame = new StackTrace(1).GetFrame(1);
                            MessageBox.Show(stackFrame.GetFileName() + "\n" + stackFrame.GetMethod().ToString() + "\n" + stackFrame.GetFileLineNumber().ToString() + "\n" + err.ToString());
                        }
                        sqlConnectionBuf.Close();
                    }
                }
            }
            catch (Exception err)
            {
                StackFrame stackFrame = new StackTrace(1).GetFrame(1);
                MessageBox.Show(stackFrame.GetFileName() + "\n" + stackFrame.GetMethod().ToString() + "\n" + stackFrame.GetFileLineNumber().ToString() + "\n" + err.ToString());
            }
            return responseBuf;
        }
        //UPDATE table_name
        //SET column1 = value1, column2 = value2...., columnN = valueN
        //WHERE[condition];
        public void UpdateDevicePosition(int id, double xpos, double ypos)
        {
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                string query = "UPDATE DevicesTable SET XPos = @xpos, YPos = @ypos WHERE ID = @id";

                using (SqlCommand cmd = new SqlCommand(query, conn))
                {
                    cmd.Parameters.AddWithValue("@xpos", xpos);
                    cmd.Parameters.AddWithValue("@ypos", ypos);
                    cmd.Parameters.AddWithValue("@id", id);

                    conn.Open();
                    int rowsAffected = cmd.ExecuteNonQuery();
                    Console.WriteLine($"{rowsAffected} dòng đã được cập nhật.");
                }
            }
        }

        public void UpdateDevice(Device_Info device)
        {
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        try
                        {
                            string commandStringBuf = "UPDATE " + TableString + " SET gatewayID=@gatewayID,Type=@Type,Model=@Model,Name=@Name,MaxValue=@MaxValue," +
                                                                                    "XPos=@XPos,YPos=@YPos,MainPara=@MainPara,TimeAlive=@TimeAlive,TimeActive=@TimeActive,ConnectionType=@ConnectionType" +
                                                       " WHERE ID='" + device.ID.ToString() + "';";
                            using (SqlCommand command = new SqlCommand())
                            {
                                command.Connection = sqlConnectionBuf;
                                command.CommandText = commandStringBuf;

                                command.Parameters.AddWithValue("@gatewayID", device.gatewayID);
                                command.Parameters.AddWithValue("@Type", (int)device.Type);
                                command.Parameters.AddWithValue("@Model", (int)device.Model);
                                command.Parameters.AddWithValue("@Name", device.Name);
                                command.Parameters.AddWithValue("@MaxValue", device.MaxValue);
                                command.Parameters.AddWithValue("@XPos", device.XPos);
                                command.Parameters.AddWithValue("@YPos", device.YPos);
                                command.Parameters.AddWithValue("@MainPara", device.MainPara);
                                command.Parameters.AddWithValue("@TimeAlive", device.TimeAlive);
                                command.Parameters.AddWithValue("@TimeActive", device.TimeActive);
                                command.Parameters.AddWithValue("@ConnectionType", (int)device.ConnectionType);

                                command.ExecuteNonQuery();
                            }
                        }
                        catch (Exception err)
                        {
                            StackFrame stackFrame = new StackTrace(1).GetFrame(1);
                            MessageBox.Show(stackFrame.GetFileName() + "\n" + stackFrame.GetMethod().ToString() + "\n" + stackFrame.GetFileLineNumber().ToString() + "\n" + err.ToString());
                        }
                        sqlConnectionBuf.Close();
                    }
                }
            }
            catch (Exception err)
            {
                StackFrame stackFrame = new StackTrace(1).GetFrame(1);
                MessageBox.Show(stackFrame.GetFileName() + "\n" + stackFrame.GetMethod().ToString() + "\n" + stackFrame.GetFileLineNumber().ToString() + "\n" + err.ToString());
            }
        }
    }
}
