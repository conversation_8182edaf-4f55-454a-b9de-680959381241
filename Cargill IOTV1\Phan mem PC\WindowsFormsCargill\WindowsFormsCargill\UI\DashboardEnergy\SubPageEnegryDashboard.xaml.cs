using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;
using WindowsFormsCargill.Constant;
using WindowsFormsCargill.DB;
using WindowsFormsCargill.UI.Widget.Bar;
//using WindowsFormsCargill.UI.Widget.BarValue;
using WindowsFormsCargill.UI.Widget.Device;
using WindowsFormsCargill.UI.Widget.EventShow;
using WindowsFormsCargill.UI.Widget.Pie2;
using WindowsFormsCargill.UI.Widget.Serial;
using WindowsFormsCargill.UI.Widget.Serial2;

namespace WindowsFormsCargill.UI.DashboardEnergy
{
    /// <summary>
    /// Interaction logic for SubPageEnegryDashboard.xaml
    /// </summary>
    public partial class SubPageEnegryDashboard : UserControl
    {
        // Static reference to current instance for DeviceWindowWidget to call
        public static SubPageEnegryDashboard CurrentInstance { get; private set; }

        public enum Device_State
        {
            NOMAL = 0,
            WAITING = 1,
            WARNING = 2,
            ERROR = 3,
        }

        public class DataGrid_DeviceInfo
        {
            public int deviceId { get; set; }
            public string Name { get; set; }
            public float Xpos { get; set; }
            public float Ypos { get; set; }
            public string deviceName { get; set; }
            public string parName { get; set; }
            public double parValue { get; set; }
            public float MaxValue { get; set; }
            public DateTime Time { get; set; }
            public Constant.Device_Model Model { get; set; }
            public CONNECTION_STATE ConnectionState { get; set; }
        }
        public class DataGrid_AllData_Observer
        {
            public ObservableCollection<DataGrid_DeviceInfo> listDevice;
        }

        // Variables
        private DataGrid_AllData_Observer uiObserver = new DataGrid_AllData_Observer();

        private readonly DispatcherTimer _dispatcherTimer;
        private bool StartEditFlag = false;
        private bool RowEditFlag = false;

        private bool FirtTimeFlag = false;
        //

        // event 

        // plot
        public SubPageEnegryDashboard()
        {
            InitializeComponent();

            // Set static reference to current instance
            CurrentInstance = this;
            {// Creat variables
                {// For UI data grid
                    uiObserver = new DataGrid_AllData_Observer();
                    uiObserver.listDevice = new ObservableCollection<DataGrid_DeviceInfo>();
                }
            }
            {// Others
                {// Set source for datagrid
                    //XamlListDevEDashboard_DataGrid.ItemsSource = uiObserver.listDevice;

                }
                {// Refresh all datagrid
                    //CollectionViewSource.GetDefaultView(XamlListDevEDashboard_DataGrid.ItemsSource).Refresh();
                }
            }


            InitPlot();

            Loaded += (s, e) =>
            {
                Console.WriteLine("Load Enegry Dashboard");
                List<DB.Device_Info> listDeviceType_Buf = MainWindow.apiDatabase.ReadDeviceInfo_ByType(Constant.Device_Application.ENEGRY);
                Update_ListDevices(listDeviceType_Buf);

                _dispatcherTimer.Start();

                FirtTimeFlag = true;
                //RefreshConfigPlot();

                DevicePosition();
                RefreshPlot();
                CountTime = 6;

            };
            Unloaded += (s, e) =>
            {
                Console.WriteLine("UnLoad  Enegry Dashboard");
                _dispatcherTimer.Stop();

                // Clear static reference when unloading
                if (CurrentInstance == this)
                {
                    CurrentInstance = null;
                }

            };


            _dispatcherTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            _dispatcherTimer.Tick += OnTimer;


        }

        private int CountTime = 0;
        private void OnTimer(object source, EventArgs e)
        {
            //if (XamlRecState.Background == Brushes.Red) XamlRecState.Background = Brushes.Green;
            //else XamlRecState.Background = Brushes.Red;
            // check page show
            // check device Id
            // load data
            
            CountTime++;
            if (FirtTimeFlag == true)
            {
                FirtTimeFlag = false;
                RefreshConfigPlot();
            }
            if (CountTime > 5)
            {
                for (int i = 0; i < uiObserver.listDevice.Count; i++)
                {
                    List<TelemetryData> data = MainWindow.apiDatabase.ReadListTelemetry_ByNumbeAndDeviceID(uiObserver.listDevice[i].parName, uiObserver.listDevice[i].deviceId, 1);
                    if (data.Count == 1)
                    {
                        string value = GetValue(data[0].deviceID, data[0].Name);
                        uiObserver.listDevice[i].parValue = float.Parse(value);
                        uiObserver.listDevice[i].Time = data[0].Time;
                    }
                }

                {// Refresh all datagrid
                    if (StartEditFlag == false && RowEditFlag == false)
                    {
                        //CollectionViewSource.GetDefaultView(XamlListDevEDashboard_DataGrid.ItemsSource).Refresh();
                    }

                }

                for (int i = 0; i < uiObserver.listDevice.Count; i++)
                {
                    for (int k = 0; k < MainWindow.systemState.listDevState.Count; k++)
                    {
                        if (uiObserver.listDevice[i].deviceId == MainWindow.systemState.listDevState[k].deviceId)
                        {
                            uiObserver.listDevice[i].ConnectionState = MainWindow.systemState.listDevState[k].ConnectionState;
                            break;
                        }
                    }
                }

                Refresh_ListDevicesMap_Grid();
            }

            RefreshPlot();
        }

        private string GetValue(int deviceId, string para)
        {
            double value = 0;
            List<TelemetryData> listTelemetry = new List<TelemetryData>();
            for (int k = 0; k < MainWindow.systemState.listDevState.Count; k++)
            {
                if (MainWindow.systemState.listDevState[k].deviceId == deviceId)
                {
                    // Get value flow para name
                    if (MainWindow.systemState.listDevState[k].listTelemetry != null)
                    {
                        for (int i = 0; i < MainWindow.systemState.listDevState[k].listTelemetry.Count; i++)
                        {
                            if (para == MainWindow.systemState.listDevState[k].listTelemetry[i].Name) value = MainWindow.systemState.listDevState[k].listTelemetry[i].Value;
                        }
                    }
                }
            }
            return value.ToString();
        }

        public void InitPlot()
        {
            // event 
            //XamlEventView.Children.Clear();
            //Widget.EventShow.EventShowWidget eventShowWidget = new EventShowWidget();
            //eventShowWidget.SetConfig(Device_Application.ENEGRY);
            //eventShowWidget.RefreshPlot();
            //XamlEventView.Children.Add(eventShowWidget);
            // load mape
            //XamlImageMape.Source = new BitmapImage(new Uri(UI_EnegryMainLayout.dashboardConfig.mapeConfig.ImagePath, UriKind.Relative));

            // Serial Chart
            XamlBarValue.Children.Clear();
            XamlPlot_StackPanelSerialPlot.Controls.Clear();

            for (int i = 0; i < UI_EnegryMainLayout.enegryConfig.listSerial.Count; i++)
            {

                Widget.Serial.SerialWidget serialWidget = new SerialWidget();
                serialWidget.setShowType(SerialWidget.CONFIG_TYPE.SHOW);
                serialWidget.SetConfig(Constant.Dashboard.ENEGRY, UI_EnegryMainLayout.enegryConfig.listSerial[i].ID);
                //serialWidget.RefreshPlot();
                ElementHost elementHost = new ElementHost()
                {
                    Height = 200,
                    Dock = System.Windows.Forms.DockStyle.Fill,
                };
                elementHost.Child = serialWidget;
                XamlPlot_StackPanelSerialPlot.Controls.Add(elementHost);
            }

            // Serial 2 chart  
            XamlPlot_StackPanelSerialPlot2.Controls.Clear();

            for (int i = 0; i < UI_EnegryMainLayout.enegryConfig.listSerial2.Count; i++)
            {

                Widget.Serial2.Serial2Widget serial2Widget = new Serial2Widget();
                serial2Widget.setShowType(Serial2Widget.CONFIG_TYPE.SHOW);
                serial2Widget.SetConfig(Constant.Dashboard.ENEGRY, UI_EnegryMainLayout.enegryConfig.listSerial2[i].ID);
                //serial2Widget.RefreshPlot();
                ElementHost elementHost = new ElementHost()
                {
                    Height = 200,
                    Dock = System.Windows.Forms.DockStyle.Fill,
                };
                elementHost.Child = serial2Widget;
                XamlPlot_StackPanelSerialPlot2.Controls.Add(elementHost);
            }

            // Pie chart

            //XamlPiePlot.Children.Clear();
            //for (int i = 0; i < UI_EnegryMainLayout.enegryConfig.listPie.Count; i++)
            //{

            //    Widget.Pie2.Pie2Widget pieWidget = new Pie2Widget();
            //    //pieWidget.Height = 350;
            //    //pieWidget.Width = 1500;
            //    pieWidget.HorizontalAlignment = HorizontalAlignment.Stretch;
            //    pieWidget.VerticalAlignment = VerticalAlignment.Stretch;
            //    pieWidget.setShowType(Pie2Widget.CONFIG_TYPE.SHOW);
            //    pieWidget.SetConfig(Constant.Dashboard.ENEGRY, UI_EnegryMainLayout.enegryConfig.listPie[i].ID);
            //    //pieWidget.RefreshPlot();

            //    XamlPiePlot.Children.Add(pieWidget);
            //}

            // bar value
            //XamlBarValue.Children.Clear();
            //for (int i = 0; i < UI_EnegryMainLayout.enegryConfig.listBarValue.Count; i++)
            //{
            //    BarValueWidget barValue = new BarValueWidget();

            //    barValue.Width = 380;
            //    barValue.Height = 75;
            //    barValue.HorizontalAlignment = HorizontalAlignment.Left;
            //    barValue.VerticalAlignment = VerticalAlignment.Top;
            //    barValue.setShowType(BarValueWidget.CONFIG_TYPE.SHOW);
            //    barValue.SetConfig(Constant.Dashboard.ENEGRY, UI_EnegryMainLayout.enegryConfig.listBarValue[i].ID);
            //    //barValue.RefreshPlot();

            //    XamlBarValue.Children.Add(barValue);
            //}

            // Bar Chart


            // ========
            //StackedUserControl stackPlot = new StackedUserControl();
            //stackPlot.Height = 200;

            //XamlPlot_StackPanel2.Children.Add(stackPlot);
        }
        public void RefreshConfigPlot()
        {
            // serial Plot
            if (XamlPlot_StackPanelSerialPlot.Controls.Count == UI_EnegryMainLayout.enegryConfig.listSerial.Count)
            {
                for (int i = 0; i < XamlPlot_StackPanelSerialPlot.Controls.Count; i++)
                {
                    if (XamlPlot_StackPanelSerialPlot.Controls[i] is ElementHost host && host.Child is SerialWidget serialWidget)
                    {
                        serialWidget.SetConfig(Constant.Dashboard.ENEGRY, UI_EnegryMainLayout.enegryConfig.listSerial[i].ID);
                        serialWidget.RefreshPlot();
                    }
                }
            }

            // serial 2 load
            if (XamlPlot_StackPanelSerialPlot2.Controls.Count == UI_EnegryMainLayout.enegryConfig.listSerial2.Count)
            {
                for (int i = 0; i < XamlPlot_StackPanelSerialPlot2.Controls.Count; i++)
                {
                    if(XamlPlot_StackPanelSerialPlot2.Controls[i] is ElementHost host && host.Child is Serial2Widget serial2Widget)
                    {
                        serial2Widget.SetConfig(Constant.Dashboard.ENEGRY, UI_EnegryMainLayout.enegryConfig.listSerial2[i].ID);
                        serial2Widget.RefreshPlot();
                    }    
                }
            }

            // barvalue Plot
            //if (XamlBarValue.Children.Count == UI_EnegryMainLayout.enegryConfig.listBarValue.Count)
            //{
            //    for (int i = 0; i < XamlBarValue.Children.Count; i++)
            //    {
            //        Widget.BarValue.BarValueWidget barValue = (BarValueWidget)XamlBarValue.Children[i];
            //        barValue.SetConfig(Constant.Dashboard.ENEGRY, UI_EnegryMainLayout.enegryConfig.listBarValue[i].ID);
            //        barValue.RefreshPlot();
            //    }
            //}

            // Pie Plot
            //if (XamlPiePlot.Children.Count == UI_EnegryMainLayout.enegryConfig.listPie.Count)
            //{
            //    for (int i = 0; i < XamlPiePlot.Children.Count; i++)
            //    {
            //        Widget.Pie2.Pie2Widget pieWidget = (Pie2Widget)XamlPiePlot.Children[i];
            //        pieWidget.SetConfig(Constant.Dashboard.ENEGRY, UI_EnegryMainLayout.enegryConfig.listPie[i].ID);
            //        pieWidget.RefreshPlot();
            //    }
            //}
            
            // event load
            //if (XamlEventView.Children.Count == 1)
            //{
            //    for (int i = 0; i < XamlEventView.Children.Count; i++)
            //    {
            //        Widget.EventShow.EventShowWidget eventShowWidget = (EventShowWidget)XamlEventView.Children[i];
            //        eventShowWidget.RefreshPlotTime();
            //    }
            //}

        }

        public void RefreshPlot()
        {
            // serial Plot
            if (XamlPlot_StackPanelSerialPlot.Controls.Count == UI_EnegryMainLayout.enegryConfig.listSerial.Count)
            {
                for (int i = 0; i < XamlPlot_StackPanelSerialPlot.Controls.Count; i++)
                {
                    if (XamlPlot_StackPanelSerialPlot.Controls[i] is ElementHost host && host.Child is SerialWidget serialWidget)
                        serialWidget.RefreshPlotTime();
                }
            }

            // serial 2 Plot
            if (XamlPlot_StackPanelSerialPlot2.Controls.Count == UI_EnegryMainLayout.enegryConfig.listSerial2.Count)
            {
                for (int i = 0; i < XamlPlot_StackPanelSerialPlot2.Controls.Count; i++)
                {
                    if (XamlPlot_StackPanelSerialPlot2.Controls[i] is ElementHost host && host.Child is Serial2Widget serial2Widget)
                        serial2Widget.RefreshPlotTime();
                }
            }

            // barvalue Plot
            //if (XamlBarValue.Children.Count == UI_EnegryMainLayout.enegryConfig.listBarValue.Count)
            //{
            //    for (int i = 0; i < XamlBarValue.Children.Count; i++)
            //    {
            //        Widget.BarValue.BarValueWidget serialBarWidget = (BarValueWidget)XamlBarValue.Children[i];
            //        serialBarWidget.RefreshPlotTime();
            //    }
            //}

            // Pie Plot
            //if (XamlPiePlot.Children.Count == UI_EnegryMainLayout.enegryConfig.listPie.Count)
            //{
            //    for (int i = 0; i < XamlPiePlot.Children.Count; i++)
            //    {
            //        Widget.Pie2.Pie2Widget pieWidget = (Pie2Widget)XamlPiePlot.Children[i];
            //        pieWidget.RefreshPlotTime();
            //    }
            //}

            // event load
            //if (XamlEventView.Children.Count == 1)
            //{
            //    for (int i = 0; i < XamlEventView.Children.Count; i++)
            //    {
            //        Widget.EventShow.EventShowWidget eventShowWidget = (EventShowWidget)XamlEventView.Children[i];
            //        eventShowWidget.RefreshPlotTime();
            //    }
            //}

        }
        #region Update Data

        public void Update_ListDevices(List<DB.Device_Info> newListDevicesInfo)
        {
            uiObserver.listDevice.Clear();
            for (int i = 0; i < newListDevicesInfo.Count; i++)
            {
                DataGrid_DeviceInfo uiDeviceInfo = new DataGrid_DeviceInfo();

                uiDeviceInfo.deviceId = newListDevicesInfo[i].ID;
                uiDeviceInfo.Name = "Pos_" + newListDevicesInfo[i].ID;
                uiDeviceInfo.Xpos = newListDevicesInfo[i].XPos;
                uiDeviceInfo.Ypos = newListDevicesInfo[i].YPos;
                uiDeviceInfo.Model = newListDevicesInfo[i].Model;
                uiDeviceInfo.deviceName = newListDevicesInfo[i].Name;
                uiDeviceInfo.parName = newListDevicesInfo[i].MainPara;
                uiDeviceInfo.MaxValue = newListDevicesInfo[i].MaxValue;
                uiObserver.listDevice.Add(uiDeviceInfo);
            }
            for (int i = 0; i < uiObserver.listDevice.Count; i++)
            {
                for (int k = 0; k < MainWindow.systemState.listDevState.Count; k++)
                {
                    if (uiObserver.listDevice[i].deviceId == MainWindow.systemState.listDevState[k].deviceId)
                    {
                        uiObserver.listDevice[i].ConnectionState = MainWindow.systemState.listDevState[k].ConnectionState;
                        break;
                    }
                }
            }
            //DevicePosition();
            //CollectionViewSource.GetDefaultView(XamlListDevEDashboard_DataGrid.ItemsSource).Refresh();
        }

        private void SizeImageChangedEventHandel(object sender, SizeChangedEventArgs e)
        {
            //System.Windows.Size sizeObj = e.NewSize;
            for (int i = 0; i < XamlPositionPlot.Children.Count; i++)
            {
                DependencyObject child = VisualTreeHelper.GetChild(XamlPositionPlot, i);

                DeviceWidget deviceWidget = child as DeviceWidget;
                if (deviceWidget != null)
                {
                    for (int k = 0; k < uiObserver.listDevice.Count; k++)
                    {
                        if (deviceWidget.Name == uiObserver.listDevice[k].Name)
                        {
                            
                            deviceWidget.Height = XamlPositionPlot.ActualWidth / 10;
                            deviceWidget.Width = 1000;
                            deviceWidget.Margin = new Thickness(uiObserver.listDevice[k].Xpos * XamlPositionPlot.ActualWidth - deviceWidget.Width / 2, uiObserver.listDevice[k].Ypos * XamlPositionPlot.ActualHeight - deviceWidget.Height/2, 0, 0);
                            break;
                        }
                    }

                }
            }
        }
        public void Update_PosDevice()
        {
            // Refresh device data from database to get latest positions
            List<DB.Device_Info> listDeviceType_Buf = MainWindow.apiDatabase.ReadDeviceInfo_ByType(Constant.Device_Application.ENEGRY);
            Update_ListDevices(listDeviceType_Buf);

            // Update visual positions of device widgets
            for (int i = 0; i < XamlPositionPlot.Children.Count; i++)
            {
                DependencyObject child = VisualTreeHelper.GetChild(XamlPositionPlot, i);

                DeviceWidget deviceWidget = child as DeviceWidget;
                if (deviceWidget != null)
                {
                    for (int k = 0; k < uiObserver.listDevice.Count; k++)
                    {
                        if (deviceWidget.Name == uiObserver.listDevice[k].Name)
                        {
                            deviceWidget.Height = XamlPositionPlot.ActualWidth / 10;
                            deviceWidget.Width = 1000;
                            deviceWidget.Margin = new Thickness(uiObserver.listDevice[k].Xpos * XamlPositionPlot.ActualWidth - deviceWidget.Width / 2, uiObserver.listDevice[k].Ypos * XamlPositionPlot.ActualHeight - deviceWidget.Height / 2, 0, 0);
                            break;
                        }
                    }

                }
            }
            DevicePosition();
        }
        private void OnHandelUpdateProperty(object sender, RoutedEventArgs e)
        {
            for (int i = 0; i < XamlPositionPlot.Children.Count; i++)
            {
                DependencyObject child = VisualTreeHelper.GetChild(XamlPositionPlot, i);

                DeviceWidget deviceWidget = child as DeviceWidget;
                if (deviceWidget != null)
                {
                    for (int k = 0; k < uiObserver.listDevice.Count; k++)
                    {
                        if (deviceWidget.Name == uiObserver.listDevice[k].Name)
                        {
                            deviceWidget.Height = XamlPositionPlot.ActualWidth / 10;
                            deviceWidget.Width = 1000;
                            deviceWidget.Margin = new Thickness(uiObserver.listDevice[k].Xpos * XamlPositionPlot.ActualWidth - deviceWidget.Width / 2, uiObserver.listDevice[k].Ypos * XamlPositionPlot.ActualHeight - deviceWidget.Height / 2, 0, 0);
                            break;
                        }
                    }

                }
            }

            List<DB.Device_Info> listDevice = MainWindow.apiDatabase.ReadAllDeviceInfo();
            for (int i = 0; i < uiObserver.listDevice.Count; i++)
            {
                for (int k = 0; k < listDevice.Count; k++)
                {
                    if (uiObserver.listDevice[i].deviceId == listDevice[k].ID)
                    {
                        listDevice[k].XPos = uiObserver.listDevice[i].Xpos;
                        listDevice[k].YPos = uiObserver.listDevice[i].Ypos;
                        listDevice[k].MainPara = uiObserver.listDevice[i].parName;
                        MainWindow.apiDatabase.UpdateDeviceInfo(listDevice[k]);
                        break;
                    }
                }
            }
        }

        public void DevicePosition()
        {
            XamlPositionPlot.Children.Clear();

            Image image = new Image();
            try
            {
                image.Source = new BitmapImage(new Uri(UI_EnegryMainLayout.enegryConfig.mapeConfig.ImagePath, UriKind.Absolute));//(new Uri("/Image/Metter/factory3D.png", UriKind.Relative));
            }
            catch (Exception e)
            {
                //MessageBox.Show("Cannot find Image, show default Image");
                image.Source = new BitmapImage(new Uri("C:/IOTCargill/Image/factory3D.png", UriKind.RelativeOrAbsolute));// default
            }

            image.HorizontalAlignment = HorizontalAlignment.Left;
            image.VerticalAlignment = VerticalAlignment.Top;
            image.Stretch = Stretch.Fill;
            image.Opacity = 0.5;

            XamlPositionPlot.Children.Add(image);
            for (int i = 0; i < uiObserver.listDevice.Count; i++)
            {
                DeviceWidget device = new DeviceWidget();
                if (XamlPositionPlot.ActualWidth == 0)
                {
                    device.Margin = new Thickness(uiObserver.listDevice[i].Xpos * 80, uiObserver.listDevice[i].Ypos * 80, 0, 0);
                    device.Height = 80;
                    device.Width = 80;
                }
                else
                {
                    device.Height = XamlPositionPlot.ActualWidth / 10;
                    device.Width = 1000;
                    device.Margin = new Thickness(uiObserver.listDevice[i].Xpos * XamlPositionPlot.ActualWidth - device.Width / 2, uiObserver.listDevice[i].Ypos * XamlPositionPlot.ActualHeight - device.Height / 2, 0, 0);
                }
                device.Name = uiObserver.listDevice[i].Name;
                device.HorizontalAlignment = System.Windows.HorizontalAlignment.Left;
                device.VerticalAlignment = System.Windows.VerticalAlignment.Top;
                device.Padding = new Thickness(0, 0, 0, 0);

                device.SetId(uiObserver.listDevice[i].deviceId, uiObserver.listDevice[i].deviceName);

                device.SetValue(uiObserver.listDevice[i].deviceId, uiObserver.listDevice[i].parName, uiObserver.listDevice[i].MaxValue);

                device.SetHeader(uiObserver.listDevice[i].deviceName.ToString());

                switch (uiObserver.listDevice[i].ConnectionState)
                {
                    case CONNECTION_STATE.DISCONNECT:
                        device.SetColor(new SolidColorBrush(Colors.Red));
                        break;
                    case CONNECTION_STATE.CONNECTED:
                        device.SetColor(new SolidColorBrush(Colors.Green));
                        break;
                    default:
                        device.SetColor(new SolidColorBrush(Colors.Gray));
                        break;
                }

                XamlPositionPlot.Children.Add(device);

            }

        }
        private void Refresh_ListDevicesMap_Grid()
        {
            for (int i = 0; i < XamlPositionPlot.Children.Count; i++)
            {

                DependencyObject child = VisualTreeHelper.GetChild(XamlPositionPlot, i);

                DeviceWidget deviceWidget = child as DeviceWidget;
                if (deviceWidget != null)
                {
                    for (int k = 0; k < uiObserver.listDevice.Count; k++)
                    {
                        if (deviceWidget.Name == uiObserver.listDevice[k].Name)
                        {

                            deviceWidget.SetValue(uiObserver.listDevice[k].deviceId, uiObserver.listDevice[k].parName, uiObserver.listDevice[k].MaxValue);
                            deviceWidget.SetHeader(uiObserver.listDevice[k].deviceName.ToString());
                            switch (uiObserver.listDevice[k].ConnectionState)
                            {
                                case CONNECTION_STATE.DISCONNECT:
                                    deviceWidget.SetColor(new SolidColorBrush(Colors.Red));
                                    break;
                                case CONNECTION_STATE.CONNECTED:
                                    deviceWidget.SetColor(new SolidColorBrush(Colors.Green));
                                    break;
                                default:
                                    deviceWidget.SetColor(new SolidColorBrush(Colors.Gray));
                                    break;
                            }
                            break;
                        }
                    }

                }
            }
        }

        #endregion

        #region Event Handle
        private void BeginningEdit_EventHandle(object sender, DataGridBeginningEditEventArgs e)
        {
            StartEditFlag = true;
        }

        private void CellEditEnding_EventHandle(object sender, DataGridCellEditEndingEventArgs e)
        {
            StartEditFlag = false;
        }

        private void PreparingCellForEdit_EventHandle(object sender, DataGridPreparingCellForEditEventArgs e)
        {
            RowEditFlag = true;
        }

        private void RowEditEnding_EventHandle(object sender, DataGridRowEditEndingEventArgs e)
        {
            RowEditFlag = false;
        }
        #endregion
        private void MainGrid_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            //if (XamlDeviceProperty.ActualWidth > 400)
            //{
            //    XamlDeviceProperty.Width = new GridLength(400, GridUnitType.Pixel);
            //}
            //else
            //{
            //    XamlDeviceProperty.Width = new GridLength(1.2, GridUnitType.Star);
            //}
        }
    }
}
