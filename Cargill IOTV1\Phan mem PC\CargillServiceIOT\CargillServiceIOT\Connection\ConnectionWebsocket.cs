﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Threading;
using System.IO;
using System.Net.Sockets;
using System.Runtime.InteropServices.ComTypes;
using System.Globalization;
using System.Runtime.Remoting.Messaging;
using AppServiceCargill.Core;

using WindowsFormsCargill.DB;
using System.Windows.Forms;

//ConnectionSocket
namespace AppServiceCargill.Connection
{
    public class ConnectionSocket
    {
        private Socket listener;

        private Thread processData = null;
        private bool running = false;

        private readonly ConnectionCore connectionCore;

        public void creatListener()
        {
            string serverIP = "0.0.0.0";
            int port = 8182;
            IPEndPoint iPEndPoint = new IPEndPoint(IPAddress.Parse(serverIP), port);
            listener = new Socket(iPEndPoint.AddressFamily, SocketType.Stream, ProtocolType.Tcp);
            bool isAvailable = true;

            IPAddress ipAddress = Dns.GetHostEntry("localhost").AddressList[0];
            try
            {
                TcpListener tcpListener = new TcpListener(ipAddress, 8182);
                tcpListener.Start();
                tcpListener.Stop();
                ////MessageBox.Show($"Port {port} is ok"); 
                isAvailable = true;
            }
            catch (SocketException ex)
            {
                ////MessageBox.Show($"Port {port} is using");
                isAvailable = false;
            }

            try
            {          
                if(isAvailable == true) 
                {
                    listener.Bind(iPEndPoint);
                    listener.Listen(100);
                }
            }
            catch (Exception exp)
            {
                // time Out
                ////MessageBox.Show(exp.ToString());
                Console.WriteLine(exp);
            }
        }

        public ConnectionSocket(ConnectionCore connectCore)
        {
            connectionCore = connectCore;// only read state

            processData = new Thread(ProcessDataFunction);
            processData.Start();

            connectionCore.deviceCore.plcSendWSData_Event += plcSendWSData_EventHandle;
        }

        private void plcSendWSData_EventHandle(object sender, DeviceProcess.PLCOPC_SendDataWS data)
        {
            // Check device id then update list telemetry
            for (int i = 0; i < connectionCore.deviceCore.listDevice.Count; i++)
            {
                // Check then update
                if (data.deviceId == connectionCore.deviceCore.listDevice[i].devId)
                {
                    connectionCore.deviceCore.listDevice[i].listTelemetry = data.listTelemetry;
                }
            }
        }

        public void Stop()
        {
            //try
            //{
            //    listener.Shutdown(SocketShutdown.Both);
            //}
            //finally
            //{
            //    listener.Close();
            //}
            //listener.Close();

            listener.Dispose();
            running = false;
        }

        private async void ProcessDataFunction()
        {
            running = true;

            creatListener();

            while (running == true)
            {
                try
                {
                    Socket clientConnect = await listener.AcceptAsync();

                    if (clientConnect != null)
                    {
                        // Receive message.
                        var buffer = new byte[1_024];
                        //

                        //var received = await clientConnect.ReceiveAsync(new ArraySegment<byte>(buffer), SocketFlags.None);

                        clientConnect.ReceiveTimeout = 5000;

                        var received = clientConnect.Receive(buffer, 1024, SocketFlags.None);
                        if (received == 0)
                        {
                            // disconnect socket
                        }
                        else
                        {
                            string jsonData = Encoding.UTF8.GetString(buffer, 0, received);

                            MessageSocket.Header headerCommand = JsonConvert.DeserializeObject<MessageSocket.Header>(jsonData);

                            switch (headerCommand.cmd)
                            {
                                case COMMAND.GETINFOR:
                                    //MessageSocket.commandGetInfo getinfoCommand = JsonConvert.DeserializeObject<MessageSocket.commandGetInfo>(jsonData);

                                    MessageSocket.respondGetInfo respond = new MessageSocket.respondGetInfo();

                                    for (int i = 0; i < connectionCore.listConnectionOpc.Count; i++)
                                    {
                                        MessageSocket.GatewayState gatewayState = new MessageSocket.GatewayState();
                                        gatewayState.ConnectionState = connectionCore.listConnectionOpc[i].Get_ConnectionState();
                                        gatewayState.gatewayId = connectionCore.listConnectionOpc[i].Get_PLCID();
                                        respond.listGatewayState.Add(gatewayState);
                                    }
                                    for (int i = 0; i < connectionCore.deviceCore.listDevice.Count; i++)
                                    {
                                        MessageSocket.DeviceState deviceState = new MessageSocket.DeviceState();
                                        deviceState.ConnectionState = connectionCore.deviceCore.listDevice[i].connectionState;
                                        deviceState.deviceId = connectionCore.deviceCore.listDevice[i].devId;

                                        if (connectionCore.deviceCore.listDevice[i].listTelemetry == null)
                                        {
                                            /*if (connectionCore.deviceCore.listDevice[i].devId == 9)
                                            {
                                                deviceState.listTelemetry = new List<TelemetryData>();
                                                TelemetryData fakedata = new TelemetryData();
                                                fakedata.ID = 0;
                                                fakedata.deviceID = 9;
                                                fakedata.Name = "Massflow";
                                                fakedata.Value = 27;
                                                fakedata.Time = DateTime.Now;
                                                fakedata.TimeInt = fakedata.Time.Ticks;
                                                fakedata.dayOfWeek = DateTime.Now.DayOfWeek;
                                                deviceState.listTelemetry.Add(fakedata);

                                                TelemetryData fakedata1 = new TelemetryData();
                                                fakedata1.ID = 0;
                                                fakedata1.deviceID = 9;
                                                fakedata1.Name = "TotalMassflow";
                                                fakedata1.Value = 3000;
                                                fakedata1.Time = DateTime.Now;
                                                fakedata1.TimeInt = fakedata1.Time.Ticks;
                                                fakedata1.dayOfWeek = DateTime.Now.DayOfWeek;
                                                deviceState.listTelemetry.Add(fakedata1);

                                                TelemetryData fakedata2 = new TelemetryData();
                                                fakedata2.ID = 0;
                                                fakedata2.deviceID = 9;
                                                fakedata2.Name = "Temperature";
                                                fakedata2.Value = 106;
                                                fakedata2.Time = DateTime.Now;
                                                fakedata2.TimeInt = fakedata2.Time.Ticks;
                                                fakedata2.dayOfWeek = DateTime.Now.DayOfWeek;
                                                deviceState.listTelemetry.Add(fakedata2);

                                                TelemetryData fakedata3 = new TelemetryData();
                                                fakedata3.ID = 0;
                                                fakedata3.deviceID = 9;
                                                fakedata3.Name = "Pressure";
                                                fakedata3.Value = 10;
                                                fakedata3.Time = DateTime.Now;
                                                fakedata3.TimeInt = fakedata3.Time.Ticks;
                                                fakedata3.dayOfWeek = DateTime.Now.DayOfWeek;
                                                deviceState.listTelemetry.Add(fakedata3);
                                            }   */
                                        }
                                        else
                                        {

                                            if(connectionCore.deviceCore.listDevice[i].devId == 7)
                                            {
                                                /*deviceState.listTelemetry = new List<TelemetryData>();
                                                TelemetryData fakedata = new TelemetryData();
                                                fakedata.ID = 0;
                                                fakedata.deviceID = 7;
                                                fakedata.Name = "FlowData";
                                                fakedata.Value = 10;
                                                fakedata.Time = DateTime.Now;
                                                fakedata.TimeInt = fakedata.Time.Ticks;
                                                fakedata.dayOfWeek = DateTime.Now.DayOfWeek;
                                                deviceState.listTelemetry.Add(fakedata);*/
                                            }
                                            /*else if (connectionCore.deviceCore.listDevice[i].devId == 8)
                                            {
                                                deviceState.listTelemetry = new List<TelemetryData>();
                                                TelemetryData fakedata = new TelemetryData();
                                                fakedata.ID = 0;
                                                fakedata.deviceID = 8;
                                                fakedata.Name = "FlowData";
                                                fakedata.Value = 15;
                                                fakedata.Time = DateTime.Now;
                                                fakedata.TimeInt = fakedata.Time.Ticks;
                                                fakedata.dayOfWeek = DateTime.Now.DayOfWeek;
                                                deviceState.listTelemetry.Add(fakedata);
                                            }
                                            else if (connectionCore.deviceCore.listDevice[i].devId == 9)
                                            {
                                                deviceState.listTelemetry = new List<TelemetryData>();
                                                TelemetryData fakedata = new TelemetryData();
                                                fakedata.ID = 0;
                                                fakedata.deviceID = 9;
                                                fakedata.Name = "Massflow";
                                                fakedata.Value = 27;
                                                fakedata.Time = DateTime.Now;
                                                fakedata.TimeInt = fakedata.Time.Ticks;
                                                fakedata.dayOfWeek = DateTime.Now.DayOfWeek;
                                                deviceState.listTelemetry.Add(fakedata);

                                                TelemetryData fakedata1 = new TelemetryData();
                                                fakedata1.ID = 0;
                                                fakedata1.deviceID = 9;
                                                fakedata1.Name = "TotalMassflow";
                                                fakedata1.Value = 3000;
                                                fakedata1.Time = DateTime.Now;
                                                fakedata1.TimeInt = fakedata1.Time.Ticks;
                                                fakedata1.dayOfWeek = DateTime.Now.DayOfWeek;
                                                deviceState.listTelemetry.Add(fakedata1);

                                                TelemetryData fakedata2 = new TelemetryData();
                                                fakedata2.ID = 0;
                                                fakedata2.deviceID = 9;
                                                fakedata2.Name = "Temperature";
                                                fakedata2.Value = 106;
                                                fakedata2.Time = DateTime.Now;
                                                fakedata2.TimeInt = fakedata2.Time.Ticks;
                                                fakedata2.dayOfWeek = DateTime.Now.DayOfWeek;
                                                deviceState.listTelemetry.Add(fakedata2);

                                                TelemetryData fakedata3 = new TelemetryData();
                                                fakedata3.ID = 0;
                                                fakedata3.deviceID = 9;
                                                fakedata3.Name = "Pressure";
                                                fakedata3.Value = 10;
                                                fakedata3.Time = DateTime.Now;
                                                fakedata3.TimeInt = fakedata3.Time.Ticks;
                                                fakedata3.dayOfWeek = DateTime.Now.DayOfWeek;
                                                deviceState.listTelemetry.Add(fakedata3);
                                            }*/
                                            else
                                            {
                                                deviceState.listTelemetry = connectionCore.deviceCore.listDevice[i].listTelemetry;
                                            } 
                                                

                                        }

                                        respond.listDevState.Add(deviceState);
                                    }
                                    string temp = JsonConvert.SerializeObject(respond);
                                    var respondBytes = Encoding.UTF8.GetBytes(temp);

                                    Console.WriteLine(temp);

                                    await clientConnect.SendAsync(new ArraySegment<byte>(respondBytes), 0);
                                    break;
                                case COMMAND.RELOAD_CONFIG:

                                    break;
                            }
                        }

                    }
                    // we just close.
                    clientConnect.Close();

                }
                catch (Exception exp)
                {
                    // time Out
                    Console.WriteLine(exp);
                }

            }

            listener.Close();

        }
    }
}
