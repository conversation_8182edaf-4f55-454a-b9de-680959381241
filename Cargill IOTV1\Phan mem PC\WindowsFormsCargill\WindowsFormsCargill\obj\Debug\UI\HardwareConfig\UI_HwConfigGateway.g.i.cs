﻿#pragma checksum "..\..\..\..\UI\HardwareConfig\UI_HwConfigGateway.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "36546E8263C73A5A9B69B1DF4908BBAE2D2802AC4E903587E546F9897913F2A8"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using WindowsFormsCargill.UI.HardwareConfig;


namespace WindowsFormsCargill.UI.HardwareConfig {
    
    
    /// <summary>
    /// UI_HwConfigGateway
    /// </summary>
    public partial class UI_HwConfigGateway : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 36 "..\..\..\..\UI\HardwareConfig\UI_HwConfigGateway.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox XamlType_ComboBox;
        
        #line default
        #line hidden
        
        
        #line 38 "..\..\..\..\UI\HardwareConfig\UI_HwConfigGateway.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox XamlName_TextBox;
        
        #line default
        #line hidden
        
        
        #line 40 "..\..\..\..\UI\HardwareConfig\UI_HwConfigGateway.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox XamlIP_TextBox;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\UI\HardwareConfig\UI_HwConfigGateway.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid XamlListPLCs_DataGrid;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\..\UI\HardwareConfig\UI_HwConfigGateway.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox XamlName_TextBoxDevice;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\UI\HardwareConfig\UI_HwConfigGateway.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox XamlTimeAlive_TextBox;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\UI\HardwareConfig\UI_HwConfigGateway.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox XamlName_TimeGetTextBox;
        
        #line default
        #line hidden
        
        
        #line 105 "..\..\..\..\UI\HardwareConfig\UI_HwConfigGateway.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox XamlName_TextBoxNodeAddress;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\..\UI\HardwareConfig\UI_HwConfigGateway.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid XamllistDevices_DataGrid;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\UI\HardwareConfig\UI_HwConfigGateway.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGridComboBoxColumn XamlComboboxDeviceModel;
        
        #line default
        #line hidden
        
        
        #line 133 "..\..\..\..\UI\HardwareConfig\UI_HwConfigGateway.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGridComboBoxColumn XamlComboboxDeviceType;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\..\UI\HardwareConfig\UI_HwConfigGateway.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox XamlVarname_Textbox;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\UI\HardwareConfig\UI_HwConfigGateway.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox XamlListType_Combobox;
        
        #line default
        #line hidden
        
        
        #line 170 "..\..\..\..\UI\HardwareConfig\UI_HwConfigGateway.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid XamllistPara_DataGrid;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\..\UI\HardwareConfig\UI_HwConfigGateway.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGridComboBoxColumn XamlType_DatagirdCombobox;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/WindowsFormsCargill;component/ui/hardwareconfig/ui_hwconfiggateway.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\UI\HardwareConfig\UI_HwConfigGateway.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.XamlType_ComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 2:
            this.XamlName_TextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.XamlIP_TextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            
            #line 41 "..\..\..\..\UI\HardwareConfig\UI_HwConfigGateway.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddGw_BtOnClick_EventHandle);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 49 "..\..\..\..\UI\HardwareConfig\UI_HwConfigGateway.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveGw_BtOnClick_EventHandle);
            
            #line default
            #line hidden
            return;
            case 6:
            this.XamlListPLCs_DataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 61 "..\..\..\..\UI\HardwareConfig\UI_HwConfigGateway.xaml"
            this.XamlListPLCs_DataGrid.SelectedCellsChanged += new System.Windows.Controls.SelectedCellsChangedEventHandler(this.SelectedCellsChanged_XamlListPLCs_DataGrid);
            
            #line default
            #line hidden
            return;
            case 8:
            this.XamlName_TextBoxDevice = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.XamlTimeAlive_TextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.XamlName_TimeGetTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.XamlName_TextBoxNodeAddress = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            
            #line 106 "..\..\..\..\UI\HardwareConfig\UI_HwConfigGateway.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddDevice_BtOnClick_EventHandle);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 114 "..\..\..\..\UI\HardwareConfig\UI_HwConfigGateway.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveDevice_BtOnClick_EventHandle);
            
            #line default
            #line hidden
            return;
            case 14:
            this.XamllistDevices_DataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 128 "..\..\..\..\UI\HardwareConfig\UI_HwConfigGateway.xaml"
            this.XamllistDevices_DataGrid.SelectedCellsChanged += new System.Windows.Controls.SelectedCellsChangedEventHandler(this.DeviceSelectionChanger_OnHandle);
            
            #line default
            #line hidden
            return;
            case 15:
            this.XamlComboboxDeviceModel = ((System.Windows.Controls.DataGridComboBoxColumn)(target));
            return;
            case 16:
            this.XamlComboboxDeviceType = ((System.Windows.Controls.DataGridComboBoxColumn)(target));
            return;
            case 18:
            this.XamlVarname_Textbox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 19:
            this.XamlListType_Combobox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 20:
            
            #line 161 "..\..\..\..\UI\HardwareConfig\UI_HwConfigGateway.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddOpcVar_BtOnClick);
            
            #line default
            #line hidden
            return;
            case 21:
            this.XamllistPara_DataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 22:
            this.XamlType_DatagirdCombobox = ((System.Windows.Controls.DataGridComboBoxColumn)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 7:
            
            #line 72 "..\..\..\..\UI\HardwareConfig\UI_HwConfigGateway.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteGw_BtOnClick_EventHandle);
            
            #line default
            #line hidden
            break;
            case 17:
            
            #line 143 "..\..\..\..\UI\HardwareConfig\UI_HwConfigGateway.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteDevice_BtOnClick_EventHandle);
            
            #line default
            #line hidden
            break;
            case 23:
            
            #line 178 "..\..\..\..\UI\HardwareConfig\UI_HwConfigGateway.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteOpcPara_EventHandle);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

