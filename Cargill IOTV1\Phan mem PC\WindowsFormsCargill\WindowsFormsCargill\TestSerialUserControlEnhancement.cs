using System;
using System.Collections.Generic;
using WindowsFormsCargill.DB;
using WindowsFormsCargill.UI.Widget.Serial;

namespace WindowsFormsCargill
{
    /// <summary>
    /// Test class to verify SerialUserControl enhancement with dual chart display
    /// </summary>
    public class TestSerialUserControlEnhancement
    {
        /// <summary>
        /// Test basic dual chart functionality
        /// </summary>
        public static void TestDualChartDisplay()
        {
            try
            {
                Console.WriteLine("=== Testing Dual Chart Display ===");
                
                SerialUserControl control = new SerialUserControl();
                
                // Create sample data
                List<TelemetryData> sampleData = new List<TelemetryData>
                {
                    new TelemetryData { Time = DateTime.Now.AddHours(-3), Value = 100, Name = "EDel", deviceID = 1 },
                    new TelemetryData { Time = DateTime.Now.AddHours(-2), Value = 150, Name = "EDel", deviceID = 1 },
                    new TelemetryData { Time = DateTime.Now.AddHours(-1), Value = 200, Name = "EDel", deviceID = 1 },
                    new TelemetryData { Time = DateTime.Now, Value = 250, Name = "EDel", deviceID = 1 }
                };
                
                double totalValue = 1000; // Assume total of all devices is 1000
                
                // Test with new overloaded method
                control.UpdatePlot(sampleData, "Default", 0, 300, totalValue);
                
                Console.WriteLine("✓ Dual chart display test completed");
                Console.WriteLine($"  - Sample data points: {sampleData.Count}");
                Console.WriteLine($"  - Total value for percentage: {totalValue}");
                Console.WriteLine($"  - Expected percentages: 10%, 15%, 20%, 25%");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Dual chart test failed: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Test percentage calculation
        /// </summary>
        public static void TestPercentageCalculation()
        {
            try
            {
                Console.WriteLine("=== Testing Percentage Calculation ===");
                
                // Test cases
                var testCases = new[]
                {
                    new { Value = 100.0, Total = 1000.0, Expected = 10.0 },
                    new { Value = 250.0, Total = 1000.0, Expected = 25.0 },
                    new { Value = 500.0, Total = 2000.0, Expected = 25.0 },
                    new { Value = 100.0, Total = 0.0, Expected = 0.0 }, // Edge case: division by zero
                };
                
                foreach (var testCase in testCases)
                {
                    double percentage = testCase.Total > 0 ? (testCase.Value / testCase.Total) * 100 : 0;
                    
                    if (Math.Abs(percentage - testCase.Expected) < 0.01)
                    {
                        Console.WriteLine($"✓ {testCase.Value}/{testCase.Total} = {percentage:F2}% (Expected: {testCase.Expected}%)");
                    }
                    else
                    {
                        Console.WriteLine($"✗ {testCase.Value}/{testCase.Total} = {percentage:F2}% (Expected: {testCase.Expected}%)");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Percentage calculation test failed: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Test chart type switching
        /// </summary>
        public static void TestChartTypeSwitching()
        {
            try
            {
                Console.WriteLine("=== Testing Chart Type Switching ===");
                
                SerialUserControl control = new SerialUserControl();
                
                List<TelemetryData> sampleData = new List<TelemetryData>
                {
                    new TelemetryData { Time = DateTime.Now, Value = 100, Name = "EDel", deviceID = 1 }
                };
                
                // Test different time choices
                string[] timeChoices = { "Default", "range 2 days", "range 10 days", "7 days ago", "30 days ago", "12 months ago" };
                
                foreach (string timeChoice in timeChoices)
                {
                    control.UpdatePlot(sampleData, timeChoice, 0, 200, 1000);
                    
                    string expectedType = (timeChoice == "Default" || timeChoice == "range 2 days" || timeChoice == "range 10 days") 
                        ? "Line" : "Column";
                    
                    Console.WriteLine($"✓ Time choice '{timeChoice}' -> Expected chart type: {expectedType}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Chart type switching test failed: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Test backward compatibility
        /// </summary>
        public static void TestBackwardCompatibility()
        {
            try
            {
                Console.WriteLine("=== Testing Backward Compatibility ===");
                
                SerialUserControl control = new SerialUserControl();
                
                List<TelemetryData> sampleData = new List<TelemetryData>
                {
                    new TelemetryData { Time = DateTime.Now, Value = 100, Name = "EDel", deviceID = 1 }
                };
                
                // Test old method signature (should still work)
                control.UpdatePlot(sampleData, "Default", 0, 200);
                
                Console.WriteLine("✓ Old UpdatePlot method signature works");
                Console.WriteLine("  - Percentage will be 0% when totalValue is not provided");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Backward compatibility test failed: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Run all tests
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("=== Testing SerialUserControl Enhancement ===");
            Console.WriteLine();
            
            TestDualChartDisplay();
            Console.WriteLine();
            
            TestPercentageCalculation();
            Console.WriteLine();
            
            TestChartTypeSwitching();
            Console.WriteLine();
            
            TestBackwardCompatibility();
            Console.WriteLine();
            
            Console.WriteLine("=== Tests Completed ===");
            Console.WriteLine();
            Console.WriteLine("Manual Testing Instructions:");
            Console.WriteLine("1. Open Energy Dashboard");
            Console.WriteLine("2. Look for SerialWidget charts");
            Console.WriteLine("3. Verify you see both:");
            Console.WriteLine("   - Blue columns/lines for device values");
            Console.WriteLine("   - Orange lines for percentages");
            Console.WriteLine("4. Click on data points to see tooltips");
            Console.WriteLine("5. Change time ranges to see chart type changes");
            Console.WriteLine("6. Check that percentages add up correctly across devices");
        }
    }
}
