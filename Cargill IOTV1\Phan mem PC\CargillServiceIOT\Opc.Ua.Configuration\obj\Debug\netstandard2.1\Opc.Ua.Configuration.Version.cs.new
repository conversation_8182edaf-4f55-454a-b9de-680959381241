//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

[assembly: System.Reflection.AssemblyVersionAttribute("0.0.0.0")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("0.0")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("0.0.0")]
#if NETSTANDARD || NETFRAMEWORK || NETCOREAPP
[System.CodeDom.Compiler.GeneratedCode("Nerdbank.GitVersioning.Tasks","3.2.31.56335")]
#endif
#if NETFRAMEWORK || NETCOREAPP || NETSTANDARD2_0 || NETSTANDARD2_1
[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
#endif
internal static partial class ThisAssembly {
    internal const string AssemblyVersion = "0.0.0.0";
    internal const string AssemblyFileVersion = "0.0";
    internal const string AssemblyInformationalVersion = "0.0.0";
    internal const string AssemblyName = "Opc.Ua.Configuration";
    internal const string AssemblyTitle = "Opc.Ua.Configuration";
    internal const string AssemblyConfiguration = "Debug";
    internal const bool IsPublicRelease = false;
    internal const bool IsPrerelease = false;
    internal const string RootNamespace = "Opc.Ua.Configuration";
}
