# Hướng dẫn sử dụng SerialUserControl với Biểu đồ Kép

## 🚀 Bước 1: Khởi động ứng dụng

1. **Build project:**
   ```
   - Right-click vào Solution → Clean Solution
   - Right-click vào Solution → Rebuild Solution
   ```

2. **Chạy ứng dụng:**
   - Nhấn F5 hoặc click Start
   - Đợi ứng dụng khởi động hoàn toàn

## 📊 Bước 2: Truy cập Energy Dashboard

1. **Mở Energy Dashboard:**
   - Click vào tab "Energy" trên menu chính
   - Hoặc navigate đến Energy Dashboard section

2. **Tìm SerialWidget:**
   - Trong dashboard, bạn sẽ thấy các biểu đồ SerialWidget
   - Mỗi widget hiển thị dữ liệu của một thiết bị cụ thể

## 🎯 Bước 3: Hiểu giao diện mới

### **Legend (<PERSON><PERSON> thích):**
```
■ Value (màu xanh)     ■ Percentage (màu cam)
```

### **Dual Y-Axis:**
- **Trục Y bên trái**: <PERSON><PERSON><PERSON> trị thực của thiết bị (kW, kWh, etc.)
- **Trục Y bên phải**: Phần trăm so với tổng (0-100%)

### **Biểu đồ kép:**
- **Cột xanh**: Giá trị thực của thiết bị theo thời gian
- **Đường cam**: Phần trăm thiết bị này chiếm trong tổng

## 🔍 Bước 4: Tương tác với biểu đồ

### **Xem chi tiết:**
1. **Hover chuột** lên các điểm dữ liệu
2. **Click** vào điểm dữ liệu để xem tooltip:
   - **Cột xanh**: "Date: [time], Value: [giá trị]"
   - **Đường cam**: "Date: [time], Percentage: [phần trăm]%"

### **Zoom và Pan:**
- **Zoom**: Scroll chuột lên/xuống
- **Pan**: Kéo thả để di chuyển
- **Reset**: Double-click để reset zoom

## 📈 Bước 5: Đọc hiểu dữ liệu

### **Ví dụ thực tế:**
```
Thiết bị A: 250 kW (25% tổng)
Thiết bị B: 150 kW (15% tổng)
Thiết bị C: 600 kW (60% tổng)
Tổng: 1000 kW
```

### **Ý nghĩa:**
- **Giá trị tuyệt đối**: Biết thiết bị tiêu thụ bao nhiều năng lượng
- **Phần trăm**: Biết thiết bị chiếm bao nhiều % trong tổng tiêu thụ
- **Xu hướng**: Theo dõi thay đổi theo thời gian

## ⚙️ Bước 6: Cấu hình (nếu cần)

### **Thay đổi time range:**
1. Click vào dropdown time selection
2. Chọn khoảng thời gian mong muốn:
   - Default, 7 days ago, 30 days ago, 12 months ago

### **Chart type tự động:**
- **Line chart**: Cho "Default", "range 2 days", "range 10 days"
- **Column chart**: Cho các khoảng thời gian dài hơn

## 🛠️ Troubleshooting

### **Nếu không thấy biểu đồ:**
1. Kiểm tra có dữ liệu thiết bị không
2. Refresh dashboard
3. Kiểm tra kết nối database

### **Nếu phần trăm hiển thị 0%:**
1. Kiểm tra có thiết bị nào khác cùng parameter không
2. Kiểm tra dữ liệu trong database
3. Restart ứng dụng

### **Nếu có lỗi build:**
1. Clean Solution
2. Rebuild Solution
3. Kiểm tra tất cả references

## 💡 Tips sử dụng hiệu quả

### **So sánh thiết bị:**
- Mở nhiều SerialWidget cùng lúc
- So sánh phần trăm để biết thiết bị nào tiêu thụ nhiều nhất
- Theo dõi xu hướng thay đổi

### **Phân tích hiệu suất:**
- Thiết bị có % cao → Cần tối ưu hóa
- Thiết bị có % thấp → Có thể tăng tải
- Xu hướng tăng/giảm → Lập kế hoạch bảo trì

### **Báo cáo:**
- Screenshot biểu đồ cho báo cáo
- Export dữ liệu nếu cần
- Theo dõi KPI theo thời gian

## 📋 Checklist sử dụng

- [ ] Build project thành công
- [ ] Mở Energy Dashboard
- [ ] Thấy legend ■ Value ■ Percentage
- [ ] Thấy cột xanh (giá trị) và đường cam (phần trăm)
- [ ] Tooltip hiển thị đúng khi click
- [ ] Có thể zoom/pan biểu đồ
- [ ] Phần trăm tính toán chính xác
- [ ] Time range thay đổi được

## 🎯 Kết quả mong đợi

Sau khi sử dụng, bạn sẽ có:
- **Cái nhìn toàn diện** về hiệu suất từng thiết bị
- **So sánh dễ dàng** giữa các thiết bị
- **Phân tích xu hướng** theo thời gian
- **Dữ liệu chính xác** cho việc ra quyết định
