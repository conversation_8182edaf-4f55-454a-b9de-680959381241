﻿#pragma checksum "..\..\..\..\UI\DashboardEnergy\SubPageEnegryDashboard.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "A035E41278D10747AE5A8B41804FBBFA09BB554308D858DA62CE6771A7B9A43A"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using WindowsFormsCargill.UI.DashboardEnergy;


namespace WindowsFormsCargill.UI.DashboardEnergy {
    
    
    /// <summary>
    /// SubPageEnegryDashboard
    /// </summary>
    public partial class SubPageEnegryDashboard : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 23 "..\..\..\..\UI\DashboardEnergy\SubPageEnegryDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid XamlGrid;
        
        #line default
        #line hidden
        
        
        #line 45 "..\..\..\..\UI\DashboardEnergy\SubPageEnegryDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Forms.TableLayoutPanel XamlPlot_StackPanelSerialPlot;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\..\UI\DashboardEnergy\SubPageEnegryDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Forms.TableLayoutPanel XamlPlot_StackPanelSerialPlot2;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\UI\DashboardEnergy\SubPageEnegryDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid XamlPositionPlot;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\..\UI\DashboardEnergy\SubPageEnegryDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image XamlImageMape;
        
        #line default
        #line hidden
        
        
        #line 94 "..\..\..\..\UI\DashboardEnergy\SubPageEnegryDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid XamlBarValue;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/WindowsFormsCargill;component/ui/dashboardenergy/subpageenegrydashboard.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\UI\DashboardEnergy\SubPageEnegryDashboard.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.XamlGrid = ((System.Windows.Controls.Grid)(target));
            
            #line 23 "..\..\..\..\UI\DashboardEnergy\SubPageEnegryDashboard.xaml"
            this.XamlGrid.SizeChanged += new System.Windows.SizeChangedEventHandler(this.MainGrid_SizeChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.XamlPlot_StackPanelSerialPlot = ((System.Windows.Forms.TableLayoutPanel)(target));
            return;
            case 3:
            this.XamlPlot_StackPanelSerialPlot2 = ((System.Windows.Forms.TableLayoutPanel)(target));
            return;
            case 4:
            
            #line 74 "..\..\..\..\UI\DashboardEnergy\SubPageEnegryDashboard.xaml"
            ((System.Windows.Controls.Grid)(target)).SizeChanged += new System.Windows.SizeChangedEventHandler(this.SizeImageChangedEventHandel);
            
            #line default
            #line hidden
            return;
            case 5:
            this.XamlPositionPlot = ((System.Windows.Controls.Grid)(target));
            return;
            case 6:
            this.XamlImageMape = ((System.Windows.Controls.Image)(target));
            return;
            case 7:
            this.XamlBarValue = ((System.Windows.Controls.Grid)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

