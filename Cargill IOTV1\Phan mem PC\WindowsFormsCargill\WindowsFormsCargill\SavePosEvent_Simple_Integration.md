# SavePOS_BtOnClick → Update_PosDevice Integration

## Mô tả
Khi nhấn nút `SavePOS_BtOnClick` ở `DeviceWindowWidget`, sẽ trực tiếp gọi hàm `Update_PosDevice` ở `SubPageEnegryDashboard`.

## Cách tiếp cận
Sử dụng static reference để `DeviceWindowWidget` có thể trực tiếp gọi method `Update_PosDevice` của `SubPageEnegryDashboard`.

## Các thay đổi đã thực hiện

### 1. SubPageEnegryDashboard.xaml.cs
- **Thêm static property**: `public static SubPageEnegryDashboard CurrentInstance { get; private set; }`
- **Cập nhật constructor**: Set `CurrentInstance = this;`
- **Cập nhật Unloaded event**: Clear `CurrentInstance = null;` khi unload
- **Cập nhật Update_PosDevice()**: Thêm refresh data từ database trước khi update UI

### 2. DeviceWindowWidget.xaml.cs
- **Thêm using**: `using WindowsFormsCargill.UI.DashboardEnergy;`
- **Cập nhật SavePOS_BtOnClick()**: Gọi trực tiếp `SubPageEnegryDashboard.CurrentInstance.Update_PosDevice()`

## Luồng hoạt động

1. **Khởi tạo**: Khi `SubPageEnegryDashboard` được tạo, nó set `CurrentInstance = this`

2. **Save Position**: Khi user nhấn Save Position trong `DeviceWindowWidget`:
   - Cập nhật database: `MainWindow.apiDatabase.UpdateDevicePosition()`
   - Gọi trực tiếp: `SubPageEnegryDashboard.CurrentInstance.Update_PosDevice()`
   - `Update_PosDevice()` sẽ:
     - Refresh data từ database
     - Cập nhật vị trí visual của tất cả device widgets

3. **Cleanup**: Khi dashboard bị unload, `CurrentInstance` được set về `null`

## Code Implementation

### SubPageEnegryDashboard.xaml.cs:
```csharp
public partial class SubPageEnegryDashboard : UserControl
{
    // Static reference to current instance for DeviceWindowWidget to call
    public static SubPageEnegryDashboard CurrentInstance { get; private set; }
    
    public SubPageEnegryDashboard()
    {
        InitializeComponent();
        
        // Set static reference to current instance
        CurrentInstance = this;
        
        // ... rest of constructor
        
        Unloaded += (s, e) =>
        {
            // Clear static reference when unloading
            if (CurrentInstance == this)
            {
                CurrentInstance = null;
            }
        };
    }
    
    public void Update_PosDevice()
    {
        // Refresh device data from database to get latest positions
        List<DB.Device_Info> listDeviceType_Buf = MainWindow.apiDatabase.ReadDeviceInfo_ByType(Constant.Device_Application.ENEGRY);
        Update_ListDevices(listDeviceType_Buf);
        
        // Update visual positions of device widgets
        for (int i = 0; i < XamlPositionPlot.Children.Count; i++)
        {
            // ... update UI logic
        }
    }
}
```

### DeviceWindowWidget.xaml.cs:
```csharp
private void SavePOS_BtOnClick(object sender, RoutedEventArgs e)
{
    // Update database with new position
    MainWindow.apiDatabase.UpdateDevicePosition(deviceID, float.Parse(Xpos_Textbox.Text), float.Parse(Ypos_Textbox.Text));
    
    // Call Update_PosDevice on SubPageEnegryDashboard if instance exists
    if (SubPageEnegryDashboard.CurrentInstance != null)
    {
        SubPageEnegryDashboard.CurrentInstance.Update_PosDevice();
        Console.WriteLine($"Called Update_PosDevice for device {deviceID} with position ({Xpos_Textbox.Text}, {Ypos_Textbox.Text})");
    }
    
    // Keep the original event for backward compatibility
    if (SavePosButtonEvent != null)
    {
        object[] array = new object[2] { float.Parse(Xpos_Textbox.Text), float.Parse(Ypos_Textbox.Text)};
        SavePosButtonEvent.Invoke(this, array);
    }
}
```

## Lợi ích

1. **Đơn giản**: Chỉ cần 1 static reference, không cần event handling phức tạp
2. **Trực tiếp**: Gọi trực tiếp method mong muốn
3. **Realtime**: Vị trí được cập nhật ngay lập tức
4. **An toàn**: Static reference được cleanup khi cần
5. **Backward compatible**: Vẫn giữ event cũ cho các component khác

## Cách test

1. Chạy ứng dụng và mở Energy Dashboard
2. Click vào device widget để mở DeviceWindowWidget
3. Thay đổi X Position và Y Position
4. Nhấn nút Save
5. Kiểm tra:
   - Vị trí device được cập nhật trên dashboard ngay lập tức
   - Console log hiển thị message xác nhận
   - Database được cập nhật với vị trí mới
