# 🎬 Demo Script: SerialUserControl với Biểu đồ K<PERSON>p

## 📋 Chuẩn bị Demo

### **Tr<PERSON><PERSON><PERSON> khi bắt đầu:**
1. ✅ Clean và Rebuild project
2. ✅ Đ<PERSON>m bảo có dữ liệu thiết bị trong database
3. ✅ Ứng dụng chạy không lỗi
4. ✅ Chuẩn bị màn hình để demo

## 🎯 Demo Flow (5-10 phút)

### **1. <PERSON><PERSON><PERSON><PERSON> thi<PERSON> (30 giây)**
```
"Hôm nay tôi sẽ demo tính năng mới của SerialUserControl - 
hiển thị đồng thời giá trị thiết bị và phần trăm so với tổng"
```

### **2. Khởi động ứng dụng (30 giây)**
- Mở ứng dụng
- Navigate đến Energy Dashboard
- Chỉ ra các SerialWidget trên màn hình

### **3. <PERSON><PERSON><PERSON><PERSON> thích giao di<PERSON> mới (1 phút)**
```
"<PERSON><PERSON><PERSON> c<PERSON><PERSON> b<PERSON> thấy, giờ đây mỗi biểu đồ có:
- Legend phía trên: ■ Value (xanh) và ■ Percentage (cam)
- Hai trục Y: trái cho giá trị, phải cho phần trăm
- Biểu đồ cột xanh: giá trị thực của thiết bị
- Biểu đồ đường cam: phần trăm so với tổng"
```

### **4. Demo tương tác (2 phút)**

#### **Click vào cột xanh:**
```
"Khi click vào cột xanh, tooltip hiển thị:
'Date: 23/07/2025 14:30, Value: 250.5 kW'"
```

#### **Click vào đường cam:**
```
"Khi click vào đường cam, tooltip hiển thị:
'Date: 23/07/2025 14:30, Percentage: 25.05%'"
```

#### **Demo zoom/pan:**
```
"Có thể zoom bằng scroll chuột và pan bằng cách kéo thả"
```

### **5. Giải thích ý nghĩa (2 phút)**

#### **Ví dụ thực tế:**
```
"Ví dụ: Thiết bị này tiêu thụ 250kW và chiếm 25% tổng tiêu thụ.
Điều này có nghĩa là:
- Tổng tiêu thụ của tất cả thiết bị là 1000kW
- Thiết bị này là một trong những thiết bị tiêu thụ lớn
- Cần theo dõi để tối ưu hóa năng lượng"
```

#### **So sánh nhiều thiết bị:**
```
"Khi xem nhiều biểu đồ cùng lúc, ta có thể:
- So sánh phần trăm để biết thiết bị nào tiêu thụ nhiều nhất
- Theo dõi xu hướng thay đổi theo thời gian
- Phát hiện bất thường trong tiêu thụ"
```

### **6. Demo time range (1 phút)**
```
"Có thể thay đổi khoảng thời gian xem:
- Default: Hiển thị dạng line chart
- 7 days, 30 days: Hiển thị dạng column chart
- Chart type tự động thay đổi phù hợp với dữ liệu"
```

### **7. Lợi ích và ứng dụng (1 phút)**
```
"Tính năng này giúp:
- Quản lý năng lượng hiệu quả hơn
- Phát hiện thiết bị tiêu thụ bất thường
- Lập kế hoạch bảo trì dựa trên dữ liệu
- Tối ưu hóa chi phí vận hành"
```

### **8. Q&A và kết thúc (1 phút)**
```
"Có câu hỏi nào về tính năng mới này không?"
```

## 🎥 Camera và Screen Setup

### **Góc quay đề xuất:**
1. **Wide shot**: Toàn bộ màn hình ứng dụng
2. **Close-up**: Focus vào một SerialWidget cụ thể
3. **Mouse tracking**: Highlight cursor khi click

### **Screen recording settings:**
- **Resolution**: 1920x1080 minimum
- **Frame rate**: 30fps
- **Audio**: Clear microphone, no background noise
- **Cursor**: Visible và highlighted

## 📝 Script chi tiết

### **Opening (30s):**
```
"Xin chào, tôi là [Tên]. Hôm nay tôi sẽ demo tính năng mới 
của SerialUserControl trong hệ thống IoT Cargill. 

Tính năng này cho phép hiển thị đồng thời giá trị thực 
của thiết bị và phần trăm mà thiết bị đó chiếm trong 
tổng tiêu thụ của hệ thống."
```

### **Demo chính (3-4 phút):**
```
"Như các bạn thấy trên màn hình, giờ đây mỗi biểu đồ 
có hai loại dữ liệu:

1. Cột màu xanh thể hiện giá trị thực - ví dụ 250kW
2. Đường màu cam thể hiện phần trăm - ví dụ 25%

Điều này có nghĩa là thiết bị này tiêu thụ 250kW và 
chiếm 25% trong tổng tiêu thụ của hệ thống.

[Click demo vào các điểm dữ liệu]

Khi tôi click vào cột xanh, tooltip hiển thị giá trị chính xác.
Khi click vào đường cam, tooltip hiển thị phần trăm.

[Demo zoom/pan]

Tôi có thể zoom và pan để xem chi tiết hơn.

[Demo time range]

Có thể thay đổi khoảng thời gian để phân tích xu hướng."
```

### **Closing (30s):**
```
"Tính năng này giúp các kỹ sư và quản lý có cái nhìn 
toàn diện về hiệu suất hệ thống, từ đó đưa ra quyết định 
tối ưu hóa năng lượng hiệu quả hơn.

Cảm ơn các bạn đã theo dõi!"
```

## ✅ Checklist trước khi demo

- [ ] Ứng dụng chạy ổn định
- [ ] Có dữ liệu thiết bị đầy đủ
- [ ] SerialWidget hiển thị đúng biểu đồ kép
- [ ] Tooltip hoạt động
- [ ] Zoom/pan hoạt động
- [ ] Time range thay đổi được
- [ ] Audio/video setup sẵn sàng
- [ ] Script đã thuộc lòng
- [ ] Backup plan nếu có lỗi

## 🚨 Troubleshooting trong Demo

### **Nếu biểu đồ không hiển thị:**
```
"Có vẻ như dữ liệu đang được load, 
hãy refresh lại dashboard..."
```

### **Nếu tooltip không hoạt động:**
```
"Đây là tính năng interactive, 
có thể cần click chính xác vào điểm dữ liệu..."
```

### **Nếu có lỗi bất ngờ:**
```
"Đây là phiên bản demo, trong thực tế 
tính năng này đã được test kỹ lưỡng..."
```
