﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Threading;
using System.Security.Cryptography.X509Certificates;
using WindowsFormsCargill.DB;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using Siemens.UAClientHelper;
using Opc.Ua;
using Opc.Ua.Client;
using WindowsFormsCargill;

namespace AppServiceCargill.Connection
{

    public class Connection_PLC_OPC
    {
        public class OpcSeverConfig
        {
            public int plcID;
            public string serverURL;

            public bool userAuth;
            public string User;
            public string PassWord;
        }
        public class Opc_Data_Config
        {
            public string Data;
            public string DataType;
            public Opc_Data_Config(string _data, string _type)
            {
                Data = _data;
                DataType = _type;
            }
            public Opc_Data_Config()
            {
                ;
            }
        }
        public class Opc_Msg_Config
        {
            public int MsgId;
            public int CountTime;
            public int CountSendWS;
            public int TimeCycle;
            public string NodeAddress;
            public List<Opc_Data_Config> ListDataConf;

            public Opc_Msg_Config()
            {
                ListDataConf = new List<Opc_Data_Config>();
            }
        }
        public OpcSeverConfig severConfig = new OpcSeverConfig();
        public List<Opc_Msg_Config> listMsgConfig = new List<Opc_Msg_Config>();


        public partial class PLCOPC_ReadData
        {
            public int MsgId { get; set; }
            public int plcID { get; set; }
            public bool isSaveDatabase { get; set; }
            public List<string[]> listData { get; set; }
        }

        // Variables
        private class OpcClientRun
        {
            public CONNECTION_STATE ConnectionStateOld;
            public CONNECTION_STATE ConnectionState;

            public EndpointDescriptionCollection opcUAEndpoint;
            public Session opcUASession;

            public Thread autoReadDataThread;
            public bool running;
        }
        OPC_UAClientHelperAPI opcUAClient;
        private OpcClientRun opcClientRun = new OpcClientRun();
        // Events
        public event EventHandler<int> plcConnected_Event;
        public event EventHandler<int> plcDisconnected_Event;

        public event EventHandler<PLCOPC_ReadData> plcIntervalData_Event;

        public Connection_PLC_OPC(int newID, string newServerURL, bool userAuth, string User, string Pass)
        {
            {// Creat variables
                severConfig.plcID = newID;
                severConfig.serverURL = newServerURL;
                severConfig.userAuth = userAuth;
                severConfig.User = User;
                severConfig.PassWord = Pass;

                opcClientRun.ConnectionStateOld = CONNECTION_STATE.DISCONNECT;
                opcClientRun.ConnectionState = CONNECTION_STATE.DISCONNECT;

                opcUAClient = new OPC_UAClientHelperAPI();
                opcClientRun.opcUAEndpoint = null;
                opcClientRun.opcUASession = null;
            }
            {// Creat thread
                opcClientRun.running = false;
                opcClientRun.autoReadDataThread = new Thread(AutoReadData_Thread);
            }
        }

        public void SetConfig(List<Opc_Msg_Config> listConfg)
        {
            listMsgConfig = listConfg;
        }
        // Private methods
        private void Notification_KeepAlive(ISession sender, KeepAliveEventArgs e)
        {
            try
            {
                // check for events from discarded sessions.
                if (!ReferenceEquals(sender, opcClientRun.opcUASession))
                {
                    return;
                }
                // check for disconnected session.
                if (!ServiceResult.IsGood(e.Status))
                {
                    opcClientRun.ConnectionState = CONNECTION_STATE.DISCONNECT;

                    if (opcClientRun.ConnectionStateOld != opcClientRun.ConnectionState)
                    {
                        plcDisconnected_Event.Invoke(this, severConfig.plcID);
                        Console.WriteLine("===> DISCONNECT OPC");
                    }
                    opcClientRun.ConnectionStateOld = opcClientRun.ConnectionState;
                    // try reconnecting using the existing session state
                    opcClientRun.opcUASession.Reconnect();

                }
                else
                {
                    opcClientRun.ConnectionState = CONNECTION_STATE.CONNECTED;

                    if (opcClientRun.ConnectionStateOld != opcClientRun.ConnectionState)
                    {
                        plcConnected_Event.Invoke(this, severConfig.plcID);
                        Console.WriteLine("===> CONNECTED OPC");
                    }
                    opcClientRun.ConnectionStateOld = opcClientRun.ConnectionState;

                }
            }
            catch (Exception)
            {
                opcClientRun.ConnectionState = CONNECTION_STATE.DISCONNECT;

                if (opcClientRun.ConnectionStateOld != opcClientRun.ConnectionState)
                {
                    plcDisconnected_Event.Invoke(this, severConfig.plcID);
                    Console.WriteLine("===> DISCONNECT OPC exeption");
                }
                opcClientRun.ConnectionStateOld = opcClientRun.ConnectionState;


                ////MessageBox.Show("Mất kết nối tới server OPC UA .\nCó thể do server OPC UA đã khởi động lại .\nVui lòng khởi động lại phần mềm .");
            }
        }
        private void Notification_ServerCertificate(CertificateValidator cert, CertificateValidationEventArgs e)
        {
            try
            {
                //Search for the server's certificate in store;if found -> accept
                X509Store store = new X509Store(StoreName.Root, StoreLocation.CurrentUser);
                store.Open(OpenFlags.ReadOnly);
                X509CertificateCollection certCol = store.Certificates.Find(X509FindType.FindByThumbprint, e.Certificate.Thumbprint, true);
                store.Close();
                if (certCol.Capacity > 0)
                {
                    e.Accept = true;
                }

                //Show cert dialog if cert hasn't been accepted yet
                else
                {
                    ////MessageBox.Show("Notification_ServerCertificate: Cert hasn't been accepted yet");
                }
            }
            catch
            {
                ;
            }
        }

        private void AutoReadData_Thread()
        {
            opcClientRun.running = true;

            long timeNow = DateTime.Now.Ticks / TimeSpan.TicksPerMillisecond;
            long timePre = timeNow;

            PLCOPC_ReadData readBuf = new PLCOPC_ReadData();
            PLCOPC_ReadData readBufWs = new PLCOPC_ReadData();

            // Main run
            while (opcClientRun.running)
            {
                try
                {
                    if (opcClientRun.ConnectionState == CONNECTION_STATE.CONNECTED && listMsgConfig != null)
                    {
                        for (int i = 0; i < listMsgConfig.Count; i++)
                        {
                            // For save to database
                            listMsgConfig[i].CountTime++;
                            if (listMsgConfig[i].CountTime > listMsgConfig[i].TimeCycle)
                            {
                                listMsgConfig[i].CountTime = 0;

                                readBuf.plcID = severConfig.plcID;
                                readBuf.MsgId = listMsgConfig[i].MsgId;

                                readBuf.listData = ReadStructUdt(listMsgConfig[i].NodeAddress);
                                if (readBuf.listData != null)
                                {

                                    PLCOPC_ReadData dataReturn = new PLCOPC_ReadData();

                                    dataReturn.plcID = severConfig.plcID;
                                    dataReturn.MsgId = listMsgConfig[i].MsgId;
                                    dataReturn.listData = new List<string[]>();
                                    dataReturn.isSaveDatabase = true; // must save data to database

                                    for (int k = 0; k < readBuf.listData.Count; k++)
                                    {
                                        if (readBuf.listData[k].Count() == 4)
                                        {
                                            if (readBuf.listData[k][3] == "Boolean" && readBuf.listData[k][1] == "Offline")
                                            {
                                                if (!bool.Parse(readBuf.listData[k][2].ToString()))
                                                    plcIntervalData_Event.Invoke(this, dataReturn);
                                                else break;
                                            }

                                            for (int n = 0; n < listMsgConfig[i].ListDataConf.Count; n++)
                                            {

                                                if (listMsgConfig[i].ListDataConf[n].DataType == readBuf.listData[k][3] && listMsgConfig[i].ListDataConf[n].Data == readBuf.listData[k][1])
                                                {
                                                    dataReturn.listData.Add(readBuf.listData[k]);
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                    //if (dataReturn != null) plcIntervalData_Event.Invoke(this, dataReturn);
                                }
                            }

                            // For send data websocket
                            listMsgConfig[i].CountSendWS++;
                            if (listMsgConfig[i].CountSendWS > 5) // 5 second to update
                            {
                                listMsgConfig[i].CountSendWS = 0;

                                readBufWs.plcID = severConfig.plcID;
                                readBufWs.MsgId = listMsgConfig[i].MsgId;

                                readBufWs.listData = ReadStructUdt(listMsgConfig[i].NodeAddress);
                                if (readBufWs.listData != null)
                                {

                                    PLCOPC_ReadData dataReturnWs = new PLCOPC_ReadData();

                                    dataReturnWs.plcID = severConfig.plcID;
                                    dataReturnWs.MsgId = listMsgConfig[i].MsgId;
                                    dataReturnWs.listData = new List<string[]>();
                                    dataReturnWs.isSaveDatabase = false; // send to websocket

                                    for (int k = 0; k < readBufWs.listData.Count; k++)
                                    {
                                        if (readBufWs.listData[k].Count() == 4)
                                        {
                                            if (readBufWs.listData[k][3] == "Boolean" && readBufWs.listData[k][1] == "Offline")
                                            {
                                                if (!bool.Parse(readBufWs.listData[k][2].ToString())) 
                                                    plcIntervalData_Event.Invoke(this, dataReturnWs);
                                                else break;
                                            }

                                            for (int n = 0; n < listMsgConfig[i].ListDataConf.Count; n++)
                                            {

                                                if (listMsgConfig[i].ListDataConf[n].DataType == readBufWs.listData[k][3] && listMsgConfig[i].ListDataConf[n].Data == readBufWs.listData[k][1])
                                                {
                                                    dataReturnWs.listData.Add(readBufWs.listData[k]);
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        // Try to reconnect to OPC UA server
                        Connect();
                        Thread.Sleep(2000);
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine(e.Message, "Error");
                    Disconnect();
                    opcClientRun.ConnectionState = CONNECTION_STATE.DISCONNECT;

                    if (opcClientRun.ConnectionStateOld != opcClientRun.ConnectionState)
                    {
                        plcDisconnected_Event.Invoke(this, severConfig.plcID);
                    }
                    opcClientRun.ConnectionStateOld = opcClientRun.ConnectionState;
                }
                Thread.Sleep(1000);
            }
            // Disconnect from OPC UA server
            Disconnect();
            Thread.Sleep(100);
        }

        private void Connect()
        {
            try
            {
                ReadJsonFile readJsonFile = new ReadJsonFile();
                ApiDatabase apiDatabase = new ApiDatabase();
                List<Gateway_Info> gateway_Info = apiDatabase.ReadAllGatewayInfo();
                string IPAddress = gateway_Info[0].IP; //readJsonFile.ReadFromJsonIOTFile("IPPLC");
                ApplicationDescriptionCollection servers = opcUAClient.FindServers("opc.tcp://"+ IPAddress + ":4840");

                foreach (ApplicationDescription ad in servers)
                {
                    foreach (string url in ad.DiscoveryUrls)
                    {
                        try
                        {
                            EndpointDescriptionCollection endpoints = opcUAClient.GetEndpoints(url);

                            if (endpoints.Count <= 0)
                            {

                                plcDisconnected_Event.Invoke(this, severConfig.plcID);
                                opcClientRun.ConnectionState = CONNECTION_STATE.DISCONNECT;
                                ////MessageBox.Show("Không thể kết nối đến server OPC UA theo địa chỉ: " + serverURL + " .");
                            }
                            else
                            {
                                opcClientRun.opcUAEndpoint = endpoints;
                                try
                                {
                                    //Register mandatory events (cert and keep alive)
                                    opcUAClient.KeepAliveNotification += new KeepAliveEventHandler(Notification_KeepAlive);
                                    opcUAClient.CertificateValidationNotification += new CertificateValidationEventHandler(Notification_ServerCertificate);
                                    //Check for a selected endpoint
                                    if (opcClientRun.opcUAEndpoint != null)
                                    {
                                        //Call connect
                                        opcUAClient.Connect(opcClientRun.opcUAEndpoint[0], severConfig.userAuth, severConfig.User, severConfig.PassWord);
                                        //opcUAClient.Connect(opcUAEndpoint, true, "Cargillhy001", "Cargillhy88").Wait();
                                        //Extract the session object for further direct session interactions
                                        opcClientRun.opcUASession = opcUAClient.Session;
                                    }
                                    else
                                    {
                                        Disconnect();
                                        opcClientRun.ConnectionState = CONNECTION_STATE.DISCONNECT;

                                        if (opcClientRun.ConnectionStateOld != opcClientRun.ConnectionState)
                                        {
                                            plcDisconnected_Event.Invoke(this, severConfig.plcID);
                                        }
                                        opcClientRun.ConnectionStateOld = opcClientRun.ConnectionState;
                                        ////MessageBox.Show("Endpoint không xác định .");
                                        return;
                                    }
                                }
                                catch (Exception)
                                {
                                    Disconnect();
                                    opcClientRun.ConnectionState = CONNECTION_STATE.DISCONNECT;

                                    if (opcClientRun.ConnectionStateOld != opcClientRun.ConnectionState)
                                    {
                                        plcDisconnected_Event.Invoke(this, severConfig.plcID);
                                    }
                                    opcClientRun.ConnectionStateOld = opcClientRun.ConnectionState;
                                    ////MessageBox.Show("Không thể kết nối đến server OPC UA theo địa chỉ: " + serverURL + " .");
                                    Console.WriteLine("cannot connect to sever");
                                }
                            }
                        }
                        catch (ServiceResultException sre)
                        {
                            Disconnect();
                            opcClientRun.ConnectionState = CONNECTION_STATE.DISCONNECT;

                            if (opcClientRun.ConnectionStateOld != opcClientRun.ConnectionState)
                            {
                                plcDisconnected_Event.Invoke(this, severConfig.plcID);
                            }
                            opcClientRun.ConnectionStateOld = opcClientRun.ConnectionState;
                            //If an url in ad.DiscoveryUrls can not be reached, myClientHelperAPI will throw an Exception
                            Console.WriteLine(sre.Message, "Error");
                        }

                    }
                }
            }
            catch (Exception ex)
            {
                ////MessageBox.Show(ex.Message);
                //Utilities.WriteLogError("Connect OPC:" + severConfig.serverURL.ToString() + "==>Error:" + ex);
                Disconnect();
                opcClientRun.ConnectionState = CONNECTION_STATE.DISCONNECT;

                if (opcClientRun.ConnectionStateOld != opcClientRun.ConnectionState)
                {
                    plcDisconnected_Event.Invoke(this, severConfig.plcID);
                }
                opcClientRun.ConnectionStateOld = opcClientRun.ConnectionState;
                Console.WriteLine(ex.Message, "Error");

                Thread.Sleep(10000);
            }
        }
        private void Disconnect()
        {
            try
            {
                opcUAClient.Disconnect();
            }
            catch (Exception)
            {

            }
        }

        private bool ReadConnectionState(string NodeAddess)
        {
            bool connectState = false;
            try
            {
                List<string> listTemp = opcUAClient.ReadValues(new List<string>() { NodeAddess });
                Console.WriteLine("address= " + NodeAddess + " value " + listTemp[0]);
                if (listTemp.Count == 1)
                {
                    connectState = bool.Parse(listTemp[0].ToString());
                }
            }
            catch (Exception exp)
            {
                //Utilities.WriteLogError(" ===> Get bool node Id :" + NodeAddess + "==>Error:" + exp);
                Console.WriteLine(exp);
            }

            return connectState;
        }

        private List<string[]> ReadStructUdt(string newOPCAddress)
        {
            List<string[]> responseBuf;
            try
            {
                if (opcUAClient.Session != null)
                {
                    responseBuf = opcUAClient.ReadStructUdt(newOPCAddress);
                    if (responseBuf.Count > 0)
                    {
                        responseBuf.RemoveAt(responseBuf.Count - 1);
                    }
                }
                else
                {
                    ////MessageBox.Show("Chưa kết nối được tới server OPC UA .\nServer URL: " + serverURL + " .");
                    responseBuf = null;
                }
            }
            catch (Exception exp)
            {
                //Utilities.WriteLogError(" ===> Get struct node Id :" + newOPCAddress + "==>Error:" + exp);
                Console.WriteLine(exp);
                responseBuf = null;
            }
            return responseBuf;
        }


        // Public methods -------------------------------------------------------------------------------
        public int Get_PLCID()
        {
            return severConfig.plcID;
        }
        public CONNECTION_STATE Get_ConnectionState()
        {
            return opcClientRun.ConnectionState;
        }

        public void Thread_Start()
        {
            if (opcClientRun.autoReadDataThread.ThreadState == ThreadState.Unstarted)
            {
                opcClientRun.autoReadDataThread.Start();
            }
        }
        public void Thread_Stop()
        {
            opcClientRun.running = false;
            for (int i = 0; i < 1000; i++)
            {// Wait autoReadDataThread stop, max timeout is 10 seconds
                if (opcClientRun.autoReadDataThread.ThreadState == ThreadState.Running)
                {
                    Thread.Sleep(10);
                }
                else
                {
                    i = 1000;
                }
            }
        }


    }
}
