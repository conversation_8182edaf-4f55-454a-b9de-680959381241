﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.Windows.Forms;

namespace WindowsFormsCargill.DB
{
    public class WidgetBarValue_InfoDb
    {
        public int ID { get; set; }
        public int dashboardId { get; set; }
        public string header1 { get; set; }
        public int device1Id { get; set; }
        public string Para1Name { get; set; }
        public string header2 { get; set; }
        public int device2Id { get; set; }
        public string Para2Name { get; set; }
        public string header3 { get; set; }
        public int device3Id { get; set; }
        public string Para3Name { get; set; }
        public string header4 { get; set; }
        public int device4Id { get; set; }
        public string Para4Name { get; set; }
        public int refreshTime { get; set; }
    }

    public class WidgetBarValueConfigDb
    {
        private string connectionString;
        private string TableString;

        public WidgetBarValueConfigDb(string connection)
        {
            connectionString = connection;
            TableString = "WidgetBarValueConfigTable";
        }

        public void CreateTable()
        {
            try
            {
                using (SqlConnection sql_conn = new SqlConnection(connectionString))
                {
                    sql_conn.Open();
                    if (sql_conn.State == ConnectionState.Open)
                    {
                        SqlCommand sql_cmd = new SqlCommand();
                        sql_cmd.Connection = sql_conn;

                        sql_cmd.CommandText = $"IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '{TableString}') " +
                                              $"BEGIN CREATE TABLE {TableString} (ID INT PRIMARY KEY IDENTITY, dashboardId INT, header1 NVARCHAR(MAX), device1Id INT, Para1Name NVARCHAR(MAX), " +
                                              $"header2 NVARCHAR(MAX), device2Id INT, Para2Name NVARCHAR(MAX), header3 NVARCHAR(MAX), device3Id INT, Para3Name NVARCHAR(MAX), " +
                                              $"header4 NVARCHAR(MAX), device4Id INT, Para4Name NVARCHAR(MAX), refreshTime INT) END";
                        sql_cmd.ExecuteNonQuery();
                    }
                    sql_conn.Close();
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
        }

        public List<WidgetBarValue_InfoDb> ReadByDashboardId(int dashboardId)
        {
            List<WidgetBarValue_InfoDb> responseBuf = new List<WidgetBarValue_InfoDb>();
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        string commandStringBuf = $"SELECT * FROM {TableString} WHERE dashboardId = @dashboardId";
                        using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                        {
                            commandBuf.Parameters.AddWithValue("@dashboardId", dashboardId);

                            using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                            {
                                while (readerBuf.Read())
                                {
                                    if (readerBuf.FieldCount == 15)
                                    {
                                        WidgetBarValue_InfoDb deviceInfoBuf = new WidgetBarValue_InfoDb
                                        {
                                            ID = readerBuf.GetInt32(0),
                                            dashboardId = readerBuf.GetInt32(1),
                                            header1 = readerBuf["header1"].ToString(),
                                            device1Id = readerBuf.GetInt32(3),
                                            Para1Name = readerBuf["Para1Name"].ToString(),
                                            header2 = readerBuf["header2"].ToString(),
                                            device2Id = readerBuf.GetInt32(6),
                                            Para2Name = readerBuf["Para2Name"].ToString(),
                                            header3 = readerBuf["header3"].ToString(),
                                            device3Id = readerBuf.GetInt32(9),
                                            Para3Name = readerBuf["Para3Name"].ToString(),
                                            header4 = readerBuf["header4"].ToString(),
                                            device4Id = readerBuf.GetInt32(12),
                                            Para4Name = readerBuf["Para4Name"].ToString(),
                                            refreshTime = readerBuf.GetInt32(14)
                                        };

                                        responseBuf.Add(deviceInfoBuf);
                                    }
                                }
                                readerBuf.Close();
                            }
                        }
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
            return responseBuf;
        }

        public WidgetBarValue_InfoDb ReadById(int Id)
        {
            WidgetBarValue_InfoDb deviceInfoBuf = new WidgetBarValue_InfoDb();
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        string commandStringBuf = $"SELECT * FROM {TableString} WHERE ID = @ID";
                        using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                        {
                            commandBuf.Parameters.AddWithValue("@ID", Id);

                            using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                            {
                                while (readerBuf.Read())
                                {
                                    if (readerBuf.FieldCount == 15)
                                    {
                                        deviceInfoBuf.ID = readerBuf.GetInt32(0);
                                        deviceInfoBuf.dashboardId = readerBuf.GetInt32(1);
                                        deviceInfoBuf.header1 = readerBuf["header1"].ToString();
                                        deviceInfoBuf.device1Id = readerBuf.GetInt32(3);
                                        deviceInfoBuf.Para1Name = readerBuf["Para1Name"].ToString();
                                        deviceInfoBuf.header2 = readerBuf["header2"].ToString();
                                        deviceInfoBuf.device2Id = readerBuf.GetInt32(6);
                                        deviceInfoBuf.Para2Name = readerBuf["Para2Name"].ToString();
                                        deviceInfoBuf.header3 = readerBuf["header3"].ToString();
                                        deviceInfoBuf.device3Id = readerBuf.GetInt32(9);
                                        deviceInfoBuf.Para3Name = readerBuf["Para3Name"].ToString();
                                        deviceInfoBuf.header4 = readerBuf["header4"].ToString();
                                        deviceInfoBuf.device4Id = readerBuf.GetInt32(12);
                                        deviceInfoBuf.Para4Name = readerBuf["Para4Name"].ToString();
                                        deviceInfoBuf.refreshTime = readerBuf.GetInt32(14);
                                    }
                                }
                                readerBuf.Close();
                            }
                        }
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
            return deviceInfoBuf;
        }

        public void Update(WidgetBarValue_InfoDb serialPlot)
        {
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        string commandStringBuf = $"UPDATE {TableString} SET dashboardId=@dashboardId, device1Id=@device1Id, Para1Name=@Para1Name, " +
                            $"device2Id=@device2Id, Para2Name=@Para2Name, device3Id=@device3Id, Para3Name=@Para3Name, device4Id=@device4Id, Para4Name=@Para4Name, " +
                            $"header1=@header1, header2=@header2, header3=@header3, header4=@header4, refreshTime=@refreshTime " +
                            "WHERE ID=@ID";

                        using (SqlCommand command = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                        {
                            command.Parameters.AddWithValue("@dashboardId", serialPlot.dashboardId);
                            command.Parameters.AddWithValue("@header1", serialPlot.header1);
                            command.Parameters.AddWithValue("@device1Id", serialPlot.device1Id);
                            command.Parameters.AddWithValue("@Para1Name", serialPlot.Para1Name);
                            command.Parameters.AddWithValue("@header2", serialPlot.header2);
                            command.Parameters.AddWithValue("@device2Id", serialPlot.device2Id);
                            command.Parameters.AddWithValue("@Para2Name", serialPlot.Para2Name);
                            command.Parameters.AddWithValue("@header3", serialPlot.header3);
                            command.Parameters.AddWithValue("@device3Id", serialPlot.device3Id);
                            command.Parameters.AddWithValue("@Para3Name", serialPlot.Para3Name);
                            command.Parameters.AddWithValue("@header4", serialPlot.header4);
                            command.Parameters.AddWithValue("@device4Id", serialPlot.device4Id);
                            command.Parameters.AddWithValue("@Para4Name", serialPlot.Para4Name);
                            command.Parameters.AddWithValue("@refreshTime", serialPlot.refreshTime);
                            command.Parameters.AddWithValue("@ID", serialPlot.ID);
                            command.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
        }
    }
}
