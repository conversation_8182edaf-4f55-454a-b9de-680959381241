# SerialUserControl Enhancement Documentation

## Mô tả
Cải tiến `SerialUserControl` để hiển thị đồng thời:
- **Bi<PERSON><PERSON> đồ cột**: Thể hiện giá trị thực của thiết bị
- **Biểu đồ đường**: <PERSON>h<PERSON> hiện phần trăm của thiết bị so với tổng giá trị của tất cả thiết bị cùng loại

## Các thay đổi đã thực hiện

### 1. SerialUserControl.xaml
- **Tăng chiều cao header**: Từ 15px lên 25px để chứa legend
- **Thêm legend**: Hiển thị ■ Value (màu xanh) và ■ Percentage (màu cam)

### 2. SerialUserControl.xaml.cs

#### Cấu trúc Chart mới:
- **ChartArea**: Thêm AxisY2 (secondary axis) cho phần trăm
- **Series1 (DeviceValue)**: <PERSON><PERSON><PERSON><PERSON> đồ cột cho giá trị thiết bị (màu SteelBlue)
- **Series2 (Percentage)**: Biểu đồ đường cho phần trăm (màu Orange)

#### Methods được cập nhật:
- **Constructor**: 
  - Thêm series2 và legend
  - Cấu hình AxisY2 cho phần trăm
- **UpdatePlot**: 
  - Overload method để nhận thêm parameter `totalValue`
  - Tính phần trăm: `(value / totalValue) * 100`
  - Cập nhật cả hai series đồng thời
- **ClearPlot**: Clear cả hai series
- **XamlPlot_MouseDown**: Tooltip hiển thị thông tin phù hợp cho từng series

### 3. SerialWidget.xaml.cs

#### Method mới:
- **CalculateTotalValue()**: 
  - Lấy tất cả thiết bị cùng loại (ENERGY)
  - Tính tổng giá trị mới nhất của tất cả thiết bị có cùng parameter name
  - Trả về tổng giá trị để tính phần trăm

#### Method được cập nhật:
- **RefreshPlot()**: 
  - Gọi `CalculateTotalValue()` để lấy tổng giá trị
  - Truyền `totalValue` vào `UpdatePlot()`

## Tính năng mới

### 1. Dual Chart Display
- **Primary Y-Axis (trái)**: Hiển thị giá trị thực của thiết bị
- **Secondary Y-Axis (phải)**: Hiển thị phần trăm (0-100%)
- **X-Axis**: Thời gian (chung cho cả hai series)

### 2. Smart Chart Types
- **Giá trị thiết bị**: 
  - Line chart cho "Default", "range 2 days", "range 10 days"
  - Column chart cho các khoảng thời gian khác
- **Phần trăm**: Luôn là line chart

### 3. Enhanced Tooltips
- **DeviceValue series**: "Date: [time], Value: [value]"
- **Percentage series**: "Date: [time], Percentage: [percentage]%"

### 4. Automatic Total Calculation
- Tự động tính tổng giá trị của tất cả thiết bị cùng loại
- Cập nhật realtime khi có dữ liệu mới
- Xử lý trường hợp totalValue = 0 (tránh chia cho 0)

## Cách hoạt động

1. **Khởi tạo**: SerialWidget tạo SerialUserControl với 2 series
2. **Lấy dữ liệu**: SerialWidget lấy dữ liệu thiết bị từ database
3. **Tính tổng**: Gọi `CalculateTotalValue()` để tính tổng tất cả thiết bị
4. **Hiển thị**: 
   - Series1 hiển thị giá trị thực (cột/đường)
   - Series2 hiển thị phần trăm (đường)
5. **Tương tác**: User có thể click để xem tooltip chi tiết

## Lợi ích

1. **Thông tin đầy đủ**: Xem cả giá trị tuyệt đối và tương đối
2. **So sánh dễ dàng**: Biết thiết bị chiếm bao nhiều % trong tổng
3. **Trực quan**: Hai loại biểu đồ khác nhau cho hai loại dữ liệu
4. **Tương thích ngược**: Vẫn hoạt động với code cũ (overload method)

## Ví dụ sử dụng

```csharp
// Cách cũ (vẫn hoạt động)
serialControl.UpdatePlot(data, timeChoose, minY, maxY);

// Cách mới (với phần trăm)
serialControl.UpdatePlot(data, timeChoose, minY, maxY, totalValue);
```

## Test Cases

1. **Hiển thị cơ bản**: Kiểm tra cả hai series hiển thị đúng
2. **Tooltip**: Click vào từng series để xem thông tin
3. **Chart types**: Thay đổi time range để xem chart type thay đổi
4. **Phần trăm**: Kiểm tra tính toán phần trăm chính xác
5. **Edge cases**: TotalValue = 0, không có dữ liệu

## Cấu hình màu sắc

- **DeviceValue**: SteelBlue (#4682B4)
- **Percentage**: Orange (#FFA500)
- **Grid**: Light Gray (#F0F0F0)
- **Background**: White
