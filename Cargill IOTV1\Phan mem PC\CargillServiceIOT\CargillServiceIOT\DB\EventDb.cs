﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Windows.Forms;
using WindowsFormsCargill.Constant;

namespace WindowsFormsCargill.DB
{
    public enum EVENT_TYPE
    {
        EVENT_INFOR,
        EVENT_WARNING,
        EVENT_ERROR
    }

    public class EventData_InfoDb
    {
        public int ID { get; set; }
        public int DevId { get; set; }
        public Device_Type DeviceType { get; set; }
        public EVENT_TYPE EventType { get; set; }
        public string Content { get; set; }
        public string Para { get; set; }
        public string Value { get; set; }
        public DateTime Time { get; set; }
    }

    public class EventDb
    {
        private string connectionString;
        private string TableString;

        public EventDb(string connection)
        {
            connectionString = connection;
            TableString = "EventTable";
            // CreateTable();
        }

        public void CreateTable()
        {
            try
            {
                using (SqlConnection sqlConnection = new SqlConnection(connectionString))
                {
                    sqlConnection.Open();
                    if (sqlConnection.State == ConnectionState.Open)
                    {
                        using (SqlCommand sqlCommand = sqlConnection.CreateCommand())
                        {
                            sqlCommand.CommandText = $"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='{TableString}' AND xtype='U') BEGIN CREATE TABLE {TableString} (ID INT IDENTITY(1,1) PRIMARY KEY, DevId INT, DeviceType INT, EventType INT, Content NVARCHAR(MAX), Para NVARCHAR(MAX), Value NVARCHAR(MAX), Time DATETIME) END";
                            sqlCommand.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
        }

        public bool Add(EventData_InfoDb newEvent)
        {
            bool result = false;
            try
            {
                using (SqlConnection sqlConnection = new SqlConnection(connectionString))
                {
                    sqlConnection.Open();
                    if (sqlConnection.State == ConnectionState.Open)
                    {
                        try
                        {
                            string commandString = $"INSERT INTO {TableString} (DevId, DeviceType, EventType, Content, Para, Value, Time) VALUES (@DevId, @DeviceType, @EventType, @Content, @Para, @Value, @Time)";
                            using (SqlCommand command = new SqlCommand(commandString, sqlConnection))
                            {
                                command.Parameters.AddWithValue("@DevId", newEvent.DevId);
                                command.Parameters.AddWithValue("@DeviceType", newEvent.DeviceType);
                                command.Parameters.AddWithValue("@EventType", newEvent.EventType);
                                command.Parameters.AddWithValue("@Content", newEvent.Content ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Para", newEvent.Para ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Value", newEvent.Value ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Time", newEvent.Time != DateTime.MinValue ? newEvent.Time : DateTime.Now);

                                command.ExecuteNonQuery();
                                result = true;
                            }
                        }
                        catch (Exception err)
                        {
                            //MessageBox.Show(err.ToString());
                        }
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
            return result;
        }

        public List<EventData_InfoDb> ReadLastesByLimit(int limit)
        {
            List<EventData_InfoDb> responseList = new List<EventData_InfoDb>();
            try
            {
                using (SqlConnection sqlConnection = new SqlConnection(connectionString))
                {
                    sqlConnection.Open();
                    if (sqlConnection.State == ConnectionState.Open)
                    {
                        try
                        {
                            string commandString = $"SELECT TOP {limit} * FROM {TableString} ORDER BY ID DESC";
                            using (SqlCommand command = new SqlCommand(commandString, sqlConnection))
                            {
                                using (SqlDataReader reader = command.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        if (reader.FieldCount == 8)
                                        {
                                            EventData_InfoDb eventData = new EventData_InfoDb
                                            {
                                                ID = reader.GetInt32(0),
                                                DevId = reader.GetInt32(1),
                                                DeviceType = (Device_Type)reader.GetInt32(2),
                                                EventType = (EVENT_TYPE)reader.GetInt32(3),
                                                Content = reader.IsDBNull(4) ? null : reader.GetString(4),
                                                Para = reader.IsDBNull(5) ? null : reader.GetString(5),
                                                Value = reader.IsDBNull(6) ? null : reader.GetString(6),
                                                Time = reader.GetDateTime(7)
                                            };

                                            responseList.Add(eventData);
                                        }
                                    }
                                }
                            }
                        }
                        catch (Exception err)
                        {
                            StackFrame stackFrame = new StackTrace(1).GetFrame(1);
                            //MessageBox.Show($"{stackFrame.GetFileName()}\n{stackFrame.GetMethod()}\n{stackFrame.GetFileLineNumber()}\n{err}");
                        }
                    }
                }
            }
            catch (Exception err)
            {
                StackFrame stackFrame = new StackTrace(1).GetFrame(1);
                //MessageBox.Show($"{stackFrame.GetFileName()}\n{stackFrame.GetMethod()}\n{stackFrame.GetFileLineNumber()}\n{err}");
            }
            return responseList;
        }

        public List<EventData_InfoDb> ReadLastesByDeviceIdLimit(int deviceId, int limit)
        {
            List<EventData_InfoDb> responseList = new List<EventData_InfoDb>();
            try
            {
                using (SqlConnection sqlConnection = new SqlConnection(connectionString))
                {
                    sqlConnection.Open();
                    if (sqlConnection.State == ConnectionState.Open)
                    {
                        try
                        {
                            string commandString = $"SELECT TOP {limit} * FROM {TableString} WHERE DevId = @DeviceId ORDER BY ID DESC";
                            using (SqlCommand command = new SqlCommand(commandString, sqlConnection))
                            {
                                command.Parameters.AddWithValue("@DeviceId", deviceId);

                                using (SqlDataReader reader = command.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        if (reader.FieldCount == 8)
                                        {
                                            EventData_InfoDb eventData = new EventData_InfoDb
                                            {
                                                ID = reader.GetInt32(0),
                                                DevId = reader.GetInt32(1),
                                                DeviceType = (Device_Type)reader.GetInt32(2),
                                                EventType = (EVENT_TYPE)reader.GetInt32(3),
                                                Content = reader.IsDBNull(4) ? null : reader.GetString(4),
                                                Para = reader.IsDBNull(5) ? null : reader.GetString(5),
                                                Value = reader.IsDBNull(6) ? null : reader.GetString(6),
                                                Time = reader.GetDateTime(7)
                                            };

                                            responseList.Add(eventData);
                                        }
                                    }
                                }
                            }
                        }
                        catch (Exception err)
                        {
                            StackFrame stackFrame = new StackTrace(1).GetFrame(1);
                            //MessageBox.Show($"{stackFrame.GetFileName()}\n{stackFrame.GetMethod()}\n{stackFrame.GetFileLineNumber()}\n{err}");
                        }
                    }
                }
            }
            catch (Exception err)
            {
                StackFrame stackFrame = new StackTrace(1).GetFrame(1);
                //MessageBox.Show($"{stackFrame.GetFileName()}\n{stackFrame.GetMethod()}\n{stackFrame.GetFileLineNumber()}\n{err}");
            }
            return responseList;
        }
    }
}
