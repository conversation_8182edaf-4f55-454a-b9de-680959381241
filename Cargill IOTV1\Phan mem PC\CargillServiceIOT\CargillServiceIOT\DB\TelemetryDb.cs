﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Windows.Forms;
using System.Data.SqlClient;

namespace WindowsFormsCargill.DB
{
    public partial class TelemetryData
    {
        public int ID { get; set; }
        public int deviceID { get; set; }
        public string Name { get; set; }
        public double Value { get; set; }
        public DateTime Time { get; set; }
        public long TimeInt { get; set; }
        public DayOfWeek dayOfWeek { get; set; }

    }

    public class TelemetryTableDb
    {
        private string connectionString;
        private string TableString;

        public long StartTimeInt = 0;

        public long EndTimeInt =0;
        public TelemetryTableDb(string connection)
        {
            {// Creat variables
                connectionString = connection;//connectionString = "Data Source= EnegryDatabase.db; Version = 3; New = True; Compress = True";
                TableString = "TelemetryTable";
                CreateTable();
                GetParaTable();
            }
        }

        public void CreateTable()
        {
            try
            {
                // Create a new database connection:
                using (SqlConnection sqlConnection = new SqlConnection(connectionString))
                {
                    sqlConnection.Open();
                    if (sqlConnection.State == ConnectionState.Open)
                    {
                        using (SqlCommand sqlCommand = sqlConnection.CreateCommand())
                        {
                            // Check if the table exists
                            sqlCommand.CommandText = $"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='{TableString}' AND xtype='U') BEGIN " +
                                                     $"CREATE TABLE {TableString} (" +
                                                     "ID INT IDENTITY(1,1) PRIMARY KEY, " +
                                                     "deviceID INT, " +
                                                     "Name NVARCHAR(MAX), " +
                                                     "Value FLOAT, " +
                                                     "Time DATETIME, " +
                                                     "TimeInt INT, " +
                                                     "dayOfWeek INT) END";
                            sqlCommand.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch (Exception err)
            {
                // Display the error message
                //MessageBox.Show(err.ToString());
            }
        }

        public void GetParaTable()
        {
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        string commandStringBuf = $"SELECT TOP 1 TimeInt FROM {TableString} ORDER BY TimeInt ASC";

                        using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                        {
                            using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                            {
                                if (readerBuf.Read())
                                {
                                    if (readerBuf.FieldCount == 1)
                                    {
                                        StartTimeInt = readerBuf.GetInt64(0);
                                    }
                                }
                            }
                        }

                        commandStringBuf = $"SELECT TOP 1 TimeInt FROM {TableString} ORDER BY ID DESC";

                        using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                        {
                            using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                            {
                                if (readerBuf.Read())
                                {
                                    if (readerBuf.FieldCount == 1)
                                    {
                                        EndTimeInt = readerBuf.GetInt64(0);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
        }

        public bool Add(List<TelemetryData> newTelemetry)
        {
            bool result = false;
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        string commandStringBuf = "INSERT INTO " + TableString + " (deviceID, Name, Value, Time, TimeInt, dayOfWeek) " +
                                                  "VALUES (@deviceID, @Name, @Value, @Time, @TimeInt, @dayOfWeek)";

                        using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                        {
                            foreach (var telemetry in newTelemetry)
                            {
                                commandBuf.Parameters.Clear();
                                commandBuf.Parameters.AddWithValue("@deviceID", telemetry.deviceID);
                                commandBuf.Parameters.AddWithValue("@Name", telemetry.Name);
                                commandBuf.Parameters.AddWithValue("@Value", telemetry.Value);
                                if (telemetry.Time != null)
                                {
                                    commandBuf.Parameters.AddWithValue("@Time", telemetry.Time);
                                    commandBuf.Parameters.AddWithValue("@TimeInt", telemetry.Time.Ticks);
                                    commandBuf.Parameters.AddWithValue("@dayOfWeek", (int)telemetry.Time.DayOfWeek);
                                }
                                else
                                {
                                    DateTime now = DateTime.Now;
                                    commandBuf.Parameters.AddWithValue("@Time", now);
                                    commandBuf.Parameters.AddWithValue("@TimeInt", now.Ticks);
                                    commandBuf.Parameters.AddWithValue("@dayOfWeek", (int)now.DayOfWeek);
                                }
                                commandBuf.ExecuteNonQuery();
                            }

                            result = true;
                            EndTimeInt = DateTime.Now.Ticks;
                        }
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString()); // Better to log this error rather than showing it to the user.
                GetParaTable(); // Update parameters in case of an error
            }
            return result;
        }

        public int DeleteByDeviceID(int newDeviceID)
        {
            int responseBuf = 0;
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        string commandStringBuf = "DELETE FROM " + TableString + " WHERE deviceID = @deviceID";

                        using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                        {
                            commandBuf.Parameters.AddWithValue("@deviceID", newDeviceID);

                            int rowsAffected = commandBuf.ExecuteNonQuery();
                            if (rowsAffected > 0)
                            {
                                responseBuf = newDeviceID;
                            }
                        }
                    }
                }
            }
            catch (Exception err)
            {
                // Improved error handling: Logging the error
                LogError(err);
            }
            return responseBuf;
        }

        // Optional: Method to log errors (replace with actual logging mechanism)
        private void LogError(Exception err)
        {
            // Example of logging the error to a file or a monitoring system
            // Adjust this method according to your logging strategy
            System.IO.File.AppendAllText("error_log.txt", $"{DateTime.Now}: {err.ToString()}{Environment.NewLine}");
        }

        public int DeleteByAttributeID(int newAttributeID)
        {
            int responseBuf = 0;
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        try
                        {
                            string commandStringBuf = "DELETE FROM "+ TableString+" WHERE ID ='" + newAttributeID.ToString() + "' ;";
                            using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                            {
                                commandBuf.Connection = sqlConnectionBuf;
                                commandBuf.CommandText = commandStringBuf;

                                commandBuf.ExecuteNonQuery();
                                responseBuf = newAttributeID;
                            }
                        }
                        catch (Exception err)
                        {
                            StackFrame stackFrame = new StackTrace(1).GetFrame(1);
                            //MessageBox.Show(stackFrame.GetFileName() + "\n" + stackFrame.GetMethod().ToString() + "\n" + stackFrame.GetFileLineNumber().ToString() + "\n" + err.ToString());
                        }
                        sqlConnectionBuf.Close();
                    }
                }
            }
            catch (Exception err)
            {
                StackFrame stackFrame = new StackTrace(1).GetFrame(1);
                //MessageBox.Show(stackFrame.GetFileName() + "\n" + stackFrame.GetMethod().ToString() + "\n" + stackFrame.GetFileLineNumber().ToString() + "\n" + err.ToString());
            }
            return responseBuf;
        }

        public List<TelemetryData> ReadNumbeByDeviceID(string Name, int deviceId, int Number)
        {
            List<TelemetryData> responseBuf = new List<TelemetryData>();
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        // Update the query for SQL Server
                        string commandStringBuf = $"WITH GroupPara AS (SELECT TOP {Number} * FROM  {TableString}  WHERE deviceID = {deviceId} AND Name = '{Name}' ORDER BY TimeInt DESC) SELECT * FROM GroupPara ORDER BY TimeInt";
                        using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                        {
                            // Use parameters to prevent SQL injection
                            if (Name == null) Name = ""; // fix error

                            commandBuf.Parameters.AddWithValue("@deviceID", deviceId);
                            commandBuf.Parameters.AddWithValue("@Name", Name);
                            commandBuf.Parameters.AddWithValue("@Number", Number);

                            using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                            {
                                while (readerBuf.Read())
                                {
                                    if (readerBuf.FieldCount == 7)
                                    {
                                        TelemetryData temp = new TelemetryData
                                        {
                                            ID = readerBuf.GetInt32(0),
                                            deviceID = readerBuf.GetInt32(1),
                                            Name = readerBuf.GetString(2),
                                            Value = readerBuf.GetFloat(3),
                                            Time = readerBuf.GetDateTime(4),
                                            TimeInt = readerBuf.GetInt64(5),
                                            dayOfWeek = (DayOfWeek)readerBuf.GetInt32(6)
                                        };
                                        responseBuf.Add(temp);
                                    }
                                }
                                readerBuf.Close();
                            }
                        }
                    }
                }
            }
            catch (Exception err)
            {
                // Improved error handling
                //MessageBox.Show($"Error: {err.Message}");
            }
            return responseBuf;
        }


        public List<TelemetryData> ReadNumbeByDeviceID_ForDay(string Name, int deviceId, int Day)
        {
            List<TelemetryData> responseBuf = new List<TelemetryData>();
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        // Handle interval, limitFindDay
                        Int64 Interval = 0;
                        if (Day <= 2) Interval = 3000000000; // range 5 minute
                        else Interval = 9000000000; // range 15 minute

                        // Add time now
                        DateTime caculatorDay;
                        DateTime TimeNow = new DateTime();
                        TimeNow = DateTime.Now;
                        //DateTime TimeNow = new DateTime(2024, 8, 1); // test

                        if (Day <= 2) caculatorDay = TimeNow.AddDays(-2); // < 2 days
                        else caculatorDay = TimeNow.AddDays(-10); // < 10 days

                        string commandStringBuf = $@"
                        WITH MinTimePerGroup AS (
                            SELECT [Name], MIN([Time]) AS MinTime, [deviceID]
                            FROM {TableString}
                            WHERE TimeInt >= {caculatorDay.Ticks}
                            AND TimeInt <= {TimeNow.Ticks}
                            AND TimeInt % {Interval} >= 0
                            AND TimeInt % {Interval} <= {Interval / 2}
                            AND Name = '{Name}'
                            AND deviceId = {deviceId}
                            GROUP BY Floor(TimeInt / {Interval}), [Name], [deviceID])
                        SELECT t.[ID], t.[deviceID], t.[Name], t.[Value], t.[Time], t.[TimeInt], t.[dayOfWeek]
                        FROM {TableString} t
                        JOIN MinTimePerGroup mtp
                        ON t.[Time] = mtp.MinTime AND t.[Name] = mtp.[Name] AND t.[deviceID] = mtp.[deviceID]";
                        using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                        {
                            using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                            {
                                // Call Read before accessing data.
                                //RowCount = Convert.ToInt32(cmd.ExecuteScalar());
                                while (readerBuf.Read())
                                {
                                    if (readerBuf.FieldCount == 7)
                                    {
                                        TelemetryData temp = new TelemetryData();
                                        temp.ID = readerBuf.GetInt32(0);
                                        temp.deviceID = readerBuf.GetInt32(1);
                                        temp.Name = readerBuf.GetString(2);
                                        temp.Value = readerBuf.GetFloat(3);
                                        temp.Time = readerBuf.GetDateTime(4);
                                        temp.TimeInt = readerBuf.GetInt64(5);
                                        temp.dayOfWeek = (DayOfWeek)readerBuf.GetInt32(6);
                                        responseBuf.Add(temp);
                                    }
                                }
                                // Call Close when done reading.
                                readerBuf.Close();
                            }
                        }
                        sqlConnectionBuf.Close();
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());////MessageBox.Show(" .", err.ToString());
            }
            return responseBuf;
        }

        public List<TelemetryData> ReadByDeviceIDAndBetweenTime(string Name, int deviceId, DateTime StartTime, DateTime StopTime, int Limit)
        {
            List<TelemetryData> responseBuf = new List<TelemetryData>();
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        // Update the query for SQL Server
                        string commandStringBuf = $"SELECT TOP (@Limit) * FROM {TableString} WHERE TimeInt BETWEEN @StartTime AND @StopTime AND deviceID = @deviceID AND Name = @Name ORDER BY ID DESC;";

                        using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                        {
                            // Use parameters to prevent SQL injection
                            commandBuf.Parameters.AddWithValue("@StartTime", StartTime.Ticks);
                            commandBuf.Parameters.AddWithValue("@StopTime", StopTime.Ticks);
                            commandBuf.Parameters.AddWithValue("@deviceID", deviceId);
                            commandBuf.Parameters.AddWithValue("@Name", Name);
                            commandBuf.Parameters.AddWithValue("@Limit", Limit);

                            using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                            {
                                while (readerBuf.Read())
                                {
                                    if (readerBuf.FieldCount == 7)
                                    {
                                        TelemetryData temp = new TelemetryData
                                        {
                                            ID = readerBuf.GetInt32(0),
                                            deviceID = readerBuf.GetInt32(1),
                                            Name = readerBuf.GetString(2),
                                            Value = readerBuf.GetFloat(3),
                                            Time = readerBuf.GetDateTime(4),
                                            TimeInt = readerBuf.GetInt64(5),
                                            dayOfWeek = (DayOfWeek)readerBuf.GetInt32(6)
                                        };
                                        responseBuf.Add(temp);
                                    }
                                }
                                readerBuf.Close();
                            }
                        }
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show($"Error: {err.Message}");
            }
            return responseBuf;
        }


        public List<TelemetryData> ReadByDeviceIDAndBetweenTimeInterval_V2(string Name, int deviceId, DateTime StartTime, DateTime StopTime, long Interval, int Limit)
        {
            List<TelemetryData> responseBuf = new List<TelemetryData>();
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        string commandStringBuf = $@"
                        WITH MinTimePerGroup AS (
                            SELECT [Name], MIN([Time]) AS MinTime, [deviceID]
                            FROM {TableString}
                            WHERE TimeInt >= {StartTime.Ticks}
                            AND TimeInt <= {StopTime.Ticks}
                            AND TimeInt % {Interval} >= 0
                            AND TimeInt % {Interval} <= {Interval / 2}
                            AND Name = '{Name}'
                            AND deviceId = {deviceId}
                            GROUP BY Floor(TimeInt / {Interval}), [Name], [deviceID])
                        SELECT TOP 1000 t.[ID], t.[deviceID],t.[Name], t.[Value], t.[Time], t.[TimeInt], t.[dayOfWeek]
                        FROM {TableString} t
                        JOIN MinTimePerGroup mtp
                        ON t.[Time] = mtp.MinTime AND t.[Name] = mtp.[Name] AND t.[deviceID] = mtp.[deviceID]";//ORDER BY ID DESC 

                        using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                        {
                            // Add parameters to prevent SQL injection
                            commandBuf.Parameters.AddWithValue("@StartTime", StartTime.Ticks);
                            commandBuf.Parameters.AddWithValue("@StopTime", StopTime.Ticks);
                            commandBuf.Parameters.AddWithValue("@Interval", Interval);
                            commandBuf.Parameters.AddWithValue("@Name", Name);
                            commandBuf.Parameters.AddWithValue("@deviceID", deviceId);
                            commandBuf.Parameters.AddWithValue("@Limit", Limit);

                            using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                            {
                                while (readerBuf.Read())
                                {
                                    if (readerBuf.FieldCount == 7)
                                    {
                                        TelemetryData temp = new TelemetryData
                                        {
                                            ID = readerBuf.GetInt32(0),
                                            deviceID = readerBuf.GetInt32(1),
                                            Name = readerBuf.GetString(2),
                                            Value = readerBuf.GetFloat(3),
                                            Time = readerBuf.GetDateTime(4),
                                            TimeInt = readerBuf.GetInt64(5),
                                            dayOfWeek = (DayOfWeek)readerBuf.GetInt32(6)
                                        };

                                        if (responseBuf.Count < Limit)
                                        {
                                            responseBuf.Add(temp);
                                        }
                                        else
                                        {
                                            break;
                                        }
                                    }
                                }
                                readerBuf.Close();
                            }
                        }
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show($"Error: {err.Message}");
            }
            return responseBuf;
        }

        public List<TelemetryData> ReadByDeviceIDAndBetweenTimeInterval(string Name, int deviceId, DateTime StartTime, DateTime StopTime,Int64 Interval, int Limit)
        {
            List<TelemetryData> responseBuf = new List<TelemetryData>();
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        int count = 0;
                        for(Int64 timeInt = StartTime.Ticks; timeInt < StopTime.Ticks; timeInt+= Interval)
                        {
                            count++;
                            if (count > 1000) break;

                            string commandStringBuf = $"SELECT TOP 1 * FROM {TableString} WHERE (TimeInt BETWEEN {timeInt} AND {timeInt+ Interval}) AND (deviceID = {deviceId}) " +
                                $"AND (Name = '{Name}')";//ORDER BY ID DESC 
                                                                  //Console.WriteLine(commandStringBuf);
                            using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                            {
                                using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                                {
                                    while (readerBuf.Read())
                                    {
                                        if (readerBuf.FieldCount == 7)
                                        {
                                            TelemetryData temp = new TelemetryData
                                            {
                                                ID = readerBuf.GetInt32(0),
                                                deviceID = readerBuf.GetInt32(1),
                                                Name = readerBuf.GetString(2),
                                                Value = readerBuf.GetFloat(3),
                                                Time = readerBuf.GetDateTime(4),
                                                TimeInt = readerBuf.GetInt64(5),
                                                dayOfWeek = (DayOfWeek)readerBuf.GetInt32(6)
                                            };

                                            if (responseBuf.Count < Limit)
                                            {
                                                responseBuf.Add(temp);
                                            }
                                            else
                                            {
                                                break;
                                            }
                                        }
                                    }
                                    // Ensure to close the reader when done
                                    readerBuf.Close();
                                }
                            }

                            Console.WriteLine(responseBuf.Count);
                        }
                        sqlConnectionBuf.Close();
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(" .", err.ToString());
            }
            return responseBuf;
        }
        // -------------------->>>>>>>>>>>>>>>>>>>>>>>>
        public TelemetryData ReadByDeviceIDAndMinusTime(string Name, int deviceId, DateTime time)
        {
            if(time.Ticks <= EndTimeInt && time.Ticks >= StartTimeInt)
            {
                List<TelemetryData> responseBuf = new List<TelemetryData>();
                try
                {
                    using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                    {
                        sqlConnectionBuf.Open();
                        if (sqlConnectionBuf.State == ConnectionState.Open)
                        {
                            DateTime minus = time;
                            minus = minus.AddMinutes(-5);
                            string commandStringBuf = $"SELECT TOP 1 * FROM (SELECT TOP 50 * FROM {TableString} WHERE (TimeInt <= {EndTimeInt}) AND (TimeInt >= {time.Ticks}) AND (Name = '{Name}') ORDER BY TimeInt) AS SubQuery WHERE (deviceID = {deviceId}) AND (Name = '{Name}')";

                            Console.WriteLine(commandStringBuf);
                            using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                            {
                                using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                                {
                                    while (readerBuf.Read())
                                    {
                                        if (readerBuf.FieldCount == 7)
                                        {
                                            TelemetryData temp = new TelemetryData
                                            {
                                                ID = readerBuf.GetInt32(0),
                                                deviceID = readerBuf.GetInt32(1),
                                                Name = readerBuf.GetString(2),
                                                Value = readerBuf.GetFloat(3),
                                                Time = readerBuf.GetDateTime(4),
                                                TimeInt = readerBuf.GetInt64(5)
                                            };

                                            // Check if TimeInt is greater than the specified threshold
                                            if (temp.TimeInt > minus.Ticks)
                                            {
                                                temp.dayOfWeek = (DayOfWeek)readerBuf.GetInt32(6);
                                                responseBuf.Add(temp);
                                            }
                                        }
                                    }
                                    // Close the reader when done
                                    readerBuf.Close();
                                }
                            }
                            sqlConnectionBuf.Close();
                        }
                    }
                }
                catch (Exception err)
                {
                    //MessageBox.Show(" .", err.ToString());
                }
                if (responseBuf.Count == 1) return responseBuf[0];
            }

            return null;
        }
        public TelemetryData ReadByDeviceIDAndAddTime(string Name, int deviceId, DateTime time)
        {
            if (time.Ticks >= StartTimeInt)
            {
                List<TelemetryData> responseBuf = new List<TelemetryData>();
                try
                {
                    using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                    {
                        sqlConnectionBuf.Open();
                        if (sqlConnectionBuf.State == ConnectionState.Open)
                        {
                            // Add time to 5 minutes
                            DateTime timeAdd = time.AddMinutes(5);

                            string commandStringBuf = $"SELECT TOP 1 * FROM {TableString} WHERE TimeInt < {timeAdd.Ticks} AND deviceID = {deviceId} AND Name = '{Name}' ORDER BY ID DESC";

                            Console.WriteLine(commandStringBuf);
                            using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                            {
                                using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                                {
                                    while (readerBuf.Read())
                                    {
                                        if (readerBuf.FieldCount == 7)
                                        {
                                            TelemetryData temp = new TelemetryData
                                            {
                                                ID = readerBuf.GetInt32(0),
                                                deviceID = readerBuf.GetInt32(1),
                                                Name = readerBuf.GetString(2),
                                                Value = readerBuf.GetFloat(3),
                                                Time = readerBuf.GetDateTime(4),
                                                TimeInt = readerBuf.GetInt64(5),
                                                dayOfWeek = (DayOfWeek)readerBuf.GetInt32(6)
                                            };

                                            responseBuf.Add(temp);
                                        }
                                    }
                                    readerBuf.Close();
                                }
                            }
                            sqlConnectionBuf.Close();
                        }
                    }
                }
                catch (Exception err)
                {
                    //MessageBox.Show(" .", err.ToString());
                }

                if (responseBuf.Count == 1)
                    return responseBuf[0];
            }

            return null;
        }

        public TelemetryData ReadLastestValue_ByDeviceID(int deviceId, string Name)
        {
            TelemetryData responseBuf = new TelemetryData();
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        string commandStringBuf = $"SELECT TOP 1 * FROM {TableString} WHERE deviceID={deviceId} AND Name='{Name}' ORDER BY ID DESC";
                        using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                        {
                            using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                            {
                                // Check if any data is returned
                                if (readerBuf.Read() && readerBuf.FieldCount == 7)
                                {
                                    responseBuf.ID = readerBuf.GetInt32(0);
                                    responseBuf.deviceID = readerBuf.GetInt32(1);
                                    responseBuf.Name = readerBuf.GetString(2);
                                    responseBuf.Value = readerBuf.GetFloat(3);
                                    responseBuf.Time = readerBuf.GetDateTime(4);
                                    responseBuf.TimeInt = readerBuf.GetInt64(5);
                                    responseBuf.dayOfWeek = (DayOfWeek)readerBuf.GetInt32(6);
                                }

                                // Ensure to close the reader
                                readerBuf.Close();
                            }
                        }

                        sqlConnectionBuf.Close();
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(" .", err.ToString());
            }
            return responseBuf;
        }

        // Read Latest Device
        public List<TelemetryData> ReadLatestAll_ByDeviceId(int deviceId)
        {
            List<TelemetryData> responseBuf = new List<TelemetryData>();
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        // Use SQL Server syntax to limit rows
                        string commandStringBuf = $@"WITH Ranked AS(SELECT *, ROW_NUMBER() OVER(PARTITION BY Name ORDER BY ID DESC) AS rn FROM {TableString} WHERE deviceID = {deviceId}) SELECT TOP 100 * FROM Ranked WHERE rn = 1 ORDER BY Name, ID DESC;";
                        
                        using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                        {
                            using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                            {
                                // Loop through each record returned by the query
                                while (readerBuf.Read())
                                {
                                    if (readerBuf.FieldCount == 8)
                                    {
                                        // Create a new TelemetryData object
                                        TelemetryData temp = new TelemetryData();

                                        temp.ID = readerBuf.GetInt32(0);
                                        temp.deviceID = readerBuf.GetInt32(1);
                                        temp.Name = readerBuf.GetString(2);
                                        temp.Value = readerBuf.GetFloat(3);
                                        temp.Time = readerBuf.GetDateTime(4);
                                        temp.TimeInt = readerBuf.GetInt64(5);
                                        temp.dayOfWeek = (DayOfWeek)readerBuf.GetInt32(6);
                                        

                                        // Add the object to the response list
                                        responseBuf.Add(temp);
                                    }
                                }
                            }
                        }
                        sqlConnectionBuf.Close();
                    }
                }

            }
            catch (Exception err)
            {
                //MessageBox.Show(" .", err.ToString());
            }
            return responseBuf;
        }

    }
}
