D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\KanzenServiceIoT.exe.config
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\KanzenServiceIoT.exe
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\KanzenServiceIoT.pdb
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\CargillServiceIOT.dll
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\Opc.Ua.Client.dll
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\Opc.Ua.Core.dll
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\Opc.Ua.Security.Certificates.dll
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\Newtonsoft.Json.dll
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\Microsoft.Extensions.Logging.Abstractions.dll
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\Microsoft.Bcl.HashCode.dll
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\System.Buffers.dll
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\System.Memory.dll
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\System.Formats.Asn1.dll
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\BouncyCastle.Crypto.dll
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\System.Numerics.Vectors.dll
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\System.Runtime.CompilerServices.Unsafe.dll
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\System.ValueTuple.dll
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\CargillServiceIOT.pdb
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\Opc.Ua.Client.pdb
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\Opc.Ua.Client.xml
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\Opc.Ua.Core.pdb
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\Opc.Ua.Core.xml
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\Opc.Ua.Security.Certificates.pdb
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\Opc.Ua.Security.Certificates.xml
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\Newtonsoft.Json.xml
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\obj\Debug\KanzenServiceIoT.csproj.AssemblyReference.cache
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\obj\Debug\MainWindow.g.cs
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\obj\Debug\App.g.cs
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\obj\Debug\KanzenServiceIoT_MarkupCompile.cache
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\obj\Debug\KanzenServiceIoT_MarkupCompile.lref
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\obj\Debug\MainWindow.baml
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\obj\Debug\KanzenServiceIoT.g.resources
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\obj\Debug\KanzenServiceIoT.Properties.Resources.resources
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\obj\Debug\KanzenServiceIoT.csproj.GenerateResource.cache
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\obj\Debug\KanzenServiceIoT.csproj.CoreCompileInputs.cache
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\obj\Debug\KanzenSe.01C84253.Up2Date
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\obj\Debug\KanzenServiceIoT.exe
D:\Downloads\Cargill IOTV1\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\obj\Debug\KanzenServiceIoT.pdb
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\KanzenServiceIoT.exe.config
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\KanzenServiceIoT.exe
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\KanzenServiceIoT.pdb
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\CargillServiceIOT.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\Opc.Ua.Client.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\Opc.Ua.Core.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\Opc.Ua.Security.Certificates.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\Newtonsoft.Json.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\Microsoft.Extensions.Logging.Abstractions.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\Microsoft.Bcl.HashCode.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\System.Buffers.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\System.Memory.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\System.Formats.Asn1.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\BouncyCastle.Crypto.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\System.Numerics.Vectors.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\System.Runtime.CompilerServices.Unsafe.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\System.ValueTuple.dll
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\CargillServiceIOT.pdb
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\Opc.Ua.Client.pdb
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\Opc.Ua.Client.xml
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\Opc.Ua.Core.pdb
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\Opc.Ua.Core.xml
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\Opc.Ua.Security.Certificates.pdb
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\Opc.Ua.Security.Certificates.xml
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\bin\Debug\Newtonsoft.Json.xml
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\obj\Debug\KanzenServiceIoT.csproj.AssemblyReference.cache
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\obj\Debug\MainWindow.g.cs
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\obj\Debug\App.g.cs
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\obj\Debug\KanzenServiceIoT_MarkupCompile.cache
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\obj\Debug\KanzenServiceIoT_MarkupCompile.lref
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\obj\Debug\MainWindow.baml
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\obj\Debug\KanzenServiceIoT.g.resources
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\obj\Debug\KanzenServiceIoT.Properties.Resources.resources
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\obj\Debug\KanzenServiceIoT.csproj.GenerateResource.cache
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\obj\Debug\KanzenServiceIoT.csproj.CoreCompileInputs.cache
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\obj\Debug\KanzenSe.01C84253.Up2Date
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\obj\Debug\KanzenServiceIoT.exe
D:\Downloads\IoT_Cargill_Winform\Cargill IOTV1\Phan mem PC\CargillServiceIOT\KanzenServiceIoT\obj\Debug\KanzenServiceIoT.pdb
