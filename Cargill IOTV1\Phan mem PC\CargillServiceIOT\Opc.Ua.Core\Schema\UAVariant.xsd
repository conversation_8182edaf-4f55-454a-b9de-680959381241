<xs:schema
  xmlns:xs="http://www.w3.org/2001/XMLSchema"
  xmlns:tns="http://opcfoundation.org/UA/2008/02/Types.xsd"
  targetNamespace="http://opcfoundation.org/UA/2008/02/Types.xsd"
  elementFormDefault="qualified"
>
  <xs:complexType name="Variant">
    <xs:sequence>
      <xs:any minOccurs="0" processContents="lax" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Variant" type="tns:Variant" nillable="true" />

  <xs:complexType name="ListOfVariant">
    <xs:sequence>
      <xs:element name="Variant" type="tns:Variant" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfVariant" type="tns:ListOfVariant" nillable="true"></xs:element>

</xs:schema>