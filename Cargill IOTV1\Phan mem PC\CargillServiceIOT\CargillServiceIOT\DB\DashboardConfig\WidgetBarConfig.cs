﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace WindowsFormsCargill.DB
{
    public class WidgetBarConfig_InfoDb
    {
        public int ID { get; set; }
        public int dashboardId { get; set; }
        public int deviceId { get; set; }
        public string ParaName { get; set; }
        public int LTimeHourStart1 { get; set; }
        public int LTimeMinStart1 { get; set; }
        public int LTimeHourStop1 { get; set; }
        public int LTimeMinStop1 { get; set; }
        public int LTimeHourStart2 { get; set; }
        public int LTimeMinStart2 { get; set; }
        public int LTimeHourStop2 { get; set; }
        public int LTimeMinStop2 { get; set; }

        public int HTimeHourStart1 { get; set; }
        public int HTimeMinStart1 { get; set; }
        public int HTimeHourStop1 { get; set; }
        public int HTimeMinStop1 { get; set; }
        public int HTimeHourStart2 { get; set; }
        public int HTimeMinStart2 { get; set; }
        public int HTimeHourStop2 { get; set; }
        public int HTimeMinStop2 { get; set; }

        public int refreshTime { get; set; }
    }

    public class WidgetBarConfigDb
    {
        private string connectionString;
        private string TableString;

        public WidgetBarConfigDb(string connection)
        {
            connectionString = connection;
            TableString = "WidgetBarConfigTable";
        }

        public void CreateTable()
        {
            try
            {
                using (SqlConnection sql_conn = new SqlConnection(connectionString))
                {
                    sql_conn.Open();
                    if (sql_conn.State == ConnectionState.Open)
                    {
                        SqlCommand sql_cmd = new SqlCommand();
                        sql_cmd.Connection = sql_conn;

                        sql_cmd.CommandText = "IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '" + TableString + "') " +
                                              "BEGIN CREATE TABLE " + TableString + "(ID INT PRIMARY KEY IDENTITY, dashboardId INT, deviceId INT, ParaName NVARCHAR(MAX)," +
                                              "LTimeHourStart1 INT, LTimeMinStart1 INT, LTimeHourStop1 INT, LTimeMinStop1 INT," +
                                              "LTimeHourStart2 INT, LTimeMinStart2 INT, LTimeHourStop2 INT, LTimeMinStop2 INT," +
                                              "HTimeHourStart1 INT, HTimeMinStart1 INT, HTimeHourStop1 INT, HTimeMinStop1 INT," +
                                              "HTimeHourStart2 INT, HTimeMinStart2 INT, HTimeHourStop2 INT, HTimeMinStop2 INT, refreshTime INT) END";
                        sql_cmd.ExecuteNonQuery();
                    }
                    sql_conn.Close();
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
        }

        public List<WidgetBarConfig_InfoDb> ReadByDashboardId(int dashboardId)
        {
            List<WidgetBarConfig_InfoDb> responseBuf = new List<WidgetBarConfig_InfoDb>();
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        try
                        {
                            string commandStringBuf = $"SELECT * FROM {TableString} WHERE dashboardId = @dashboardId";
                            using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                            {
                                commandBuf.Parameters.AddWithValue("@dashboardId", dashboardId);

                                using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                                {
                                    while (readerBuf.Read())
                                    {
                                        if (readerBuf.FieldCount == 21)
                                        {
                                            WidgetBarConfig_InfoDb deviceInfoBuf = new WidgetBarConfig_InfoDb();

                                            deviceInfoBuf.ID = readerBuf.GetInt32(0);
                                            deviceInfoBuf.dashboardId = readerBuf.GetInt32(1);
                                            deviceInfoBuf.deviceId = readerBuf.GetInt32(2);
                                            deviceInfoBuf.ParaName = readerBuf["ParaName"].ToString();
                                            deviceInfoBuf.LTimeHourStart1 = readerBuf.GetInt32(4);
                                            deviceInfoBuf.LTimeMinStart1 = readerBuf.GetInt32(5);
                                            deviceInfoBuf.LTimeHourStop1 = readerBuf.GetInt32(6);
                                            deviceInfoBuf.LTimeMinStop1 = readerBuf.GetInt32(7);
                                            deviceInfoBuf.LTimeHourStart2 = readerBuf.GetInt32(8);
                                            deviceInfoBuf.LTimeMinStart2 = readerBuf.GetInt32(9);
                                            deviceInfoBuf.LTimeHourStop2 = readerBuf.GetInt32(10);
                                            deviceInfoBuf.LTimeMinStop2 = readerBuf.GetInt32(11);
                                            deviceInfoBuf.HTimeHourStart1 = readerBuf.GetInt32(12);
                                            deviceInfoBuf.HTimeMinStart1 = readerBuf.GetInt32(13);
                                            deviceInfoBuf.HTimeHourStop1 = readerBuf.GetInt32(14);
                                            deviceInfoBuf.HTimeMinStop1 = readerBuf.GetInt32(15);
                                            deviceInfoBuf.HTimeHourStart2 = readerBuf.GetInt32(16);
                                            deviceInfoBuf.HTimeMinStart2 = readerBuf.GetInt32(17);
                                            deviceInfoBuf.HTimeHourStop2 = readerBuf.GetInt32(18);
                                            deviceInfoBuf.HTimeMinStop2 = readerBuf.GetInt32(19);
                                            deviceInfoBuf.refreshTime = readerBuf.GetInt32(20);
                                            responseBuf.Add(deviceInfoBuf);
                                        }
                                    }
                                    readerBuf.Close();
                                }
                            }
                        }
                        catch (Exception err)
                        {
                            //MessageBox.Show(err.ToString());
                        }
                        sqlConnectionBuf.Close();
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
            return responseBuf;
        }

        public WidgetBarConfig_InfoDb ReadById(int Id)
        {
            WidgetBarConfig_InfoDb deviceInfoBuf = new WidgetBarConfig_InfoDb();
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        string commandStringBuf = $"SELECT * FROM {TableString} WHERE ID = @ID";
                        using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                        {
                            commandBuf.Parameters.AddWithValue("@ID", Id);

                            using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                            {
                                while (readerBuf.Read())
                                {
                                    if (readerBuf.FieldCount == 21)
                                    {
                                        deviceInfoBuf.ID = readerBuf.GetInt32(0);
                                        deviceInfoBuf.dashboardId = readerBuf.GetInt32(1);
                                        deviceInfoBuf.deviceId = readerBuf.GetInt32(2);
                                        deviceInfoBuf.ParaName = readerBuf["ParaName"].ToString();
                                        deviceInfoBuf.LTimeHourStart1 = readerBuf.GetInt32(4);
                                        deviceInfoBuf.LTimeMinStart1 = readerBuf.GetInt32(5);
                                        deviceInfoBuf.LTimeHourStop1 = readerBuf.GetInt32(6);
                                        deviceInfoBuf.LTimeMinStop1 = readerBuf.GetInt32(7);
                                        deviceInfoBuf.LTimeHourStart2 = readerBuf.GetInt32(8);
                                        deviceInfoBuf.LTimeMinStart2 = readerBuf.GetInt32(9);
                                        deviceInfoBuf.LTimeHourStop2 = readerBuf.GetInt32(10);
                                        deviceInfoBuf.LTimeMinStop2 = readerBuf.GetInt32(11);
                                        deviceInfoBuf.HTimeHourStart1 = readerBuf.GetInt32(12);
                                        deviceInfoBuf.HTimeMinStart1 = readerBuf.GetInt32(13);
                                        deviceInfoBuf.HTimeHourStop1 = readerBuf.GetInt32(14);
                                        deviceInfoBuf.HTimeMinStop1 = readerBuf.GetInt32(15);
                                        deviceInfoBuf.HTimeHourStart2 = readerBuf.GetInt32(16);
                                        deviceInfoBuf.HTimeMinStart2 = readerBuf.GetInt32(17);
                                        deviceInfoBuf.HTimeHourStop2 = readerBuf.GetInt32(18);
                                        deviceInfoBuf.HTimeMinStop2 = readerBuf.GetInt32(19);
                                        deviceInfoBuf.refreshTime = readerBuf.GetInt32(20);
                                    }
                                }
                                readerBuf.Close();
                            }
                        }
                        sqlConnectionBuf.Close();
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
            return deviceInfoBuf;
        }

        public void Update(WidgetBarConfig_InfoDb serialPlot)
        {
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        try
                        {
                            string commandStringBuf = $"UPDATE {TableString} SET dashboardId=@dashboardId, deviceId=@deviceId, ParaName=@ParaName," +
                                $"LTimeHourStart1=@LTimeHourStart1, LTimeMinStart1=@LTimeMinStart1, LTimeHourStop1=@LTimeHourStop1, LTimeMinStop1=@LTimeMinStop1," +
                                $"LTimeHourStart2=@LTimeHourStart2, LTimeMinStart2=@LTimeMinStart2, LTimeHourStop2=@LTimeHourStop2, LTimeMinStop2=@LTimeMinStop2," +
                                $"HTimeHourStart1=@HTimeHourStart1, HTimeMinStart1=@HTimeMinStart1, HTimeHourStop1=@HTimeHourStop1, HTimeMinStop1=@HTimeMinStop1," +
                                $"HTimeHourStart2=@HTimeHourStart2, HTimeMinStart2=@HTimeMinStart2, HTimeHourStop2=@HTimeHourStop2, HTimeMinStop2=@HTimeMinStop2, refreshTime=@refreshTime " +
                                "WHERE ID=@ID";
                            using (SqlCommand command = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                            {
                                command.Parameters.AddWithValue("@dashboardId", serialPlot.dashboardId);
                                command.Parameters.AddWithValue("@deviceId", serialPlot.deviceId);
                                command.Parameters.AddWithValue("@ParaName", serialPlot.ParaName);
                                command.Parameters.AddWithValue("@LTimeHourStart1", serialPlot.LTimeHourStart1);
                                command.Parameters.AddWithValue("@LTimeMinStart1", serialPlot.LTimeMinStart1);
                                command.Parameters.AddWithValue("@LTimeHourStop1", serialPlot.LTimeHourStop1);
                                command.Parameters.AddWithValue("@LTimeMinStop1", serialPlot.LTimeMinStop1);
                                command.Parameters.AddWithValue("@LTimeHourStart2", serialPlot.LTimeHourStart2);
                                command.Parameters.AddWithValue("@LTimeMinStart2", serialPlot.LTimeMinStart2);
                                command.Parameters.AddWithValue("@LTimeHourStop2", serialPlot.LTimeHourStop2);
                                command.Parameters.AddWithValue("@LTimeMinStop2", serialPlot.LTimeMinStop2);
                                command.Parameters.AddWithValue("@HTimeHourStart1", serialPlot.HTimeHourStart1);
                                command.Parameters.AddWithValue("@HTimeMinStart1", serialPlot.HTimeMinStart1);
                                command.Parameters.AddWithValue("@HTimeHourStop1", serialPlot.HTimeHourStop1);
                                command.Parameters.AddWithValue("@HTimeMinStop1", serialPlot.HTimeMinStop1);
                                command.Parameters.AddWithValue("@HTimeHourStart2", serialPlot.HTimeHourStart2);
                                command.Parameters.AddWithValue("@HTimeMinStart2", serialPlot.HTimeMinStart2);
                                command.Parameters.AddWithValue("@HTimeHourStop2", serialPlot.HTimeHourStop2);
                                command.Parameters.AddWithValue("@HTimeMinStop2", serialPlot.HTimeMinStop2);
                                command.Parameters.AddWithValue("@refreshTime", serialPlot.refreshTime);
                                command.Parameters.AddWithValue("@ID", serialPlot.ID);
                                command.ExecuteNonQuery();
                            }
                        }
                        catch (Exception err)
                        {
                            //MessageBox.Show(err.ToString());
                        }
                        sqlConnectionBuf.Close();
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
        }
    }
}
