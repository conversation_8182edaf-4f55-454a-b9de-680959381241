﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using WindowsFormsCargill.Constant;
using System.Windows.Forms;

namespace WindowsFormsCargill.DB
{
    public class Dashboard_InfoDb
    {
        public int ID { get; set; }
        public Dashboard Type { get; set; }
        public string Name { get; set; }
        public string ImagePath { get; set; }
    }

    public class DashboardConfigDb
    {
        private string connectionString;
        private string TableString;

        public DashboardConfigDb(string connection)
        {
            connectionString = connection;
            TableString = "DashboardTable";
        }

        public void CreateTable()
        {
            try
            {
                using (SqlConnection sql_conn = new SqlConnection(connectionString))
                {
                    sql_conn.Open();
                    if (sql_conn.State == ConnectionState.Open)
                    {
                        SqlCommand sql_cmd = new SqlCommand();
                        sql_cmd.Connection = sql_conn;

                        sql_cmd.CommandText = "IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '" + TableString + "') " +
                                              "BEGIN CREATE TABLE " + TableString + "(ID INT PRIMARY KEY IDENTITY, Type INT, Name NVARCHAR(MAX), ImagePath NVARCHAR(MAX)) END";
                        sql_cmd.ExecuteNonQuery();
                    }
                    sql_conn.Close();
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
        }

        public Dashboard_InfoDb ReadById(int Id)
        {
            Dashboard_InfoDb deviceInfoBuf = new Dashboard_InfoDb();
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        string commandStringBuf = $"SELECT * FROM {TableString} WHERE ID = @Id";
                        using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                        {
                            commandBuf.Parameters.AddWithValue("@Id", Id);

                            using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                            {
                                while (readerBuf.Read())
                                {
                                    if (readerBuf.FieldCount == 4)
                                    {
                                        deviceInfoBuf.ID = readerBuf.GetInt32(0);
                                        deviceInfoBuf.Type = (Dashboard)readerBuf.GetInt32(1);
                                        deviceInfoBuf.Name = readerBuf["Name"].ToString();
                                        deviceInfoBuf.ImagePath = readerBuf["ImagePath"].ToString();
                                    }
                                }
                                readerBuf.Close();
                            }
                        }
                        sqlConnectionBuf.Close();
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
            return deviceInfoBuf;
        }

        public void Update(Dashboard_InfoDb serialPlot)
        {
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        try
                        {
                            string commandStringBuf = $"UPDATE {TableString} SET Type=@Type, Name=@Name, ImagePath=@ImagePath WHERE ID = @ID";
                            using (SqlCommand command = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                            {
                                command.Parameters.AddWithValue("@Type", (int)serialPlot.Type);
                                command.Parameters.AddWithValue("@Name", serialPlot.Name);
                                command.Parameters.AddWithValue("@ImagePath", serialPlot.ImagePath);
                                command.Parameters.AddWithValue("@ID", serialPlot.ID);

                                command.ExecuteNonQuery();
                            }
                        }
                        catch (Exception err)
                        {
                            //MessageBox.Show(err.ToString());
                        }
                        sqlConnectionBuf.Close();
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
        }
    }
}
