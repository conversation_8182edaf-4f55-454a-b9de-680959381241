<?xml version="1.0" encoding="utf-8" ?>
<!--
 * Copyright (c) 2005-2020 The OPC Foundation, Inc. All rights reserved.
 *
 * OPC Foundation MIT License 1.00
 * 
 * Permission is hereby granted, free of charge, to any person
 * obtaining a copy of this software and associated documentation
 * files (the "Software"), to deal in the Software without
 * restriction, including without limitation the rights to use,
 * copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following
 * conditions:
 * 
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 * OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 * HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 * WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 * OTHER DEALINGS IN THE SOFTWARE.
 *
 * The complete license agreement can be found here:
 * http://opcfoundation.org/License/MIT/1.00/
-->

<xs:schema id="SecuredApplication"
    targetNamespace="http://opcfoundation.org/UA/2011/03/SecuredApplication.xsd"
    elementFormDefault="qualified"
    xmlns="http://opcfoundation.org/UA/2011/03/SecuredApplication.xsd"
    xmlns:mstns="http://opcfoundation.org/UA/2011/03/SecuredApplication.xsd"
    xmlns:xs="http://www.w3.org/2001/XMLSchema"
>
  <xs:complexType name="SecuredApplication">
    <xs:sequence>
      <xs:element name="ApplicationName" type="xs:string" minOccurs="0" nillable="true"></xs:element>
      <xs:element name="ApplicationUri" type="xs:string" minOccurs="0" nillable="true"></xs:element>
      <xs:element name="ApplicationType" type="ApplicationType" minOccurs="0"></xs:element>
      <xs:element name="ProductName" type="xs:string" minOccurs="0"></xs:element>
      <xs:element name="ConfigurationMode" type="xs:string" minOccurs="0"></xs:element>
      <xs:element name="LastExportTime" type="xs:dateTime" minOccurs="0"></xs:element>
      <xs:element name="ConfigurationFile" type="xs:string" minOccurs="0"></xs:element>
      <xs:element name="ExecutableFile" type="xs:string" minOccurs="0"></xs:element>
      <xs:element name="ApplicationCertificate" type="CertificateIdentifier" minOccurs="0"></xs:element>
      <xs:element name="TrustedCertificateStore" type="CertificateStoreIdentifier" minOccurs="0"></xs:element>
      <xs:element name="TrustedCertificates" type="CertificateList"  minOccurs="0"></xs:element>
      <xs:element name="IssuerCertificateStore" type="CertificateStoreIdentifier" minOccurs="0"></xs:element>
      <xs:element name="IssuerCertificates" type="CertificateList"  minOccurs="0"></xs:element>
      <xs:element name="RejectedCertificatesStore" type="CertificateStoreIdentifier" minOccurs="0"></xs:element>
      <xs:element name="BaseAddresses" type="ListOfBaseAddresses" minOccurs="0"></xs:element>
      <xs:element name="SecurityProfiles" type="ListOfSecurityProfiles" minOccurs="0"></xs:element>
      <xs:element name="Extensions" type="ListOfExtensions" minOccurs="0"  />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SecuredApplication" type="SecuredApplication" />

  <xs:complexType name="ListOfExtensions">
    <xs:sequence>
      <xs:element name="Extension" minOccurs="0" maxOccurs="unbounded" nillable="true">
        <xs:complexType>
          <xs:sequence>
            <xs:any minOccurs="0" processContents="lax"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name ="ListOfBaseAddresses">
    <xs:sequence>
      <xs:element name="BaseAddress" type="xs:string" minOccurs="0" maxOccurs="unbounded"></xs:element>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name ="SecurityProfile">
    <xs:sequence>
      <xs:element name="ProfileUri" type="xs:string" minOccurs="0"></xs:element>
      <xs:element name="Enabled" type="xs:boolean" minOccurs="0"></xs:element>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name ="ListOfSecurityProfiles">
    <xs:sequence>
      <xs:element name="SecurityProfile" type="SecurityProfile" minOccurs="0" maxOccurs="unbounded"></xs:element>
    </xs:sequence>
  </xs:complexType>

  <xs:simpleType  name="ApplicationType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Server_0" />
      <xs:enumeration value="Client_1" />
      <xs:enumeration value="ClientAndServer_2" />
      <xs:enumeration value="DiscoveryServer_3" />
    </xs:restriction>
  </xs:simpleType>

  <xs:complexType name ="CertificateIdentifier">
    <xs:sequence>
      <xs:element name="StoreType" type="xs:string" minOccurs="0"></xs:element>
      <xs:element name="StorePath" type="xs:string" minOccurs="0"></xs:element>
      <xs:element name="SubjectName" type="xs:string" minOccurs="0"></xs:element>
      <xs:element name="Thumbprint" type="xs:string" minOccurs="0"></xs:element>
      <xs:element name="RawData" type="xs:base64Binary" minOccurs="0"></xs:element>
      <xs:element name="ValidationOptions" type="xs:int" minOccurs="0"></xs:element>
      <xs:element name="OfflineRevocationList" type="xs:base64Binary" minOccurs="0"></xs:element>
      <xs:element name="OnlineRevocationList" type="xs:string" minOccurs="0"></xs:element>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name ="ListOfCertificateIdentifier">
    <xs:sequence>
      <xs:element name="CertificateIdentifier" type="CertificateIdentifier" minOccurs="0" maxOccurs="unbounded"></xs:element>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name ="CertificateStoreIdentifier">
    <xs:sequence>
      <xs:element name="StoreType" type="xs:string" minOccurs="0"></xs:element>
      <xs:element name="StorePath" type="xs:string" minOccurs="0"></xs:element>
      <xs:element name="ValidationOptions" type="xs:int" minOccurs="0"></xs:element>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name ="CertificateList">
    <xs:sequence>
      <xs:element name="Certificates" type="ListOfCertificateIdentifier" minOccurs="0"></xs:element>
      <xs:element name="ValidationOptions" type="xs:int" minOccurs="0"></xs:element>
    </xs:sequence>
  </xs:complexType>

</xs:schema>
