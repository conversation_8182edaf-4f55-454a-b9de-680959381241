﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace WindowsFormsCargill.DB
{

    public class TempOpcMsgDeviceDb
    {
        public class TempOpcMsgDev_InfoDataDb
        {
            public int Id { get; set; }
            public int opcMsgId { get; set; }
            public int deviceId { get; set; }

        }
        private string connectionString;
        private string TableString;
        public TempOpcMsgDeviceDb(string connection)
        {
            {// Creat variables
                connectionString = connection;//connectionString = "Data Source= EnegryDatabase.db; Version = 3; New = True; Compress = True";
                TableString = "TempOpcMsgDeviceTable";
                //CreateTable();
            }
        }
        public void CreateTable()
        {
            try
            {
                // Create a new database connection:
                using (SqlConnection sqlite_conn = new SqlConnection(connectionString))
                {
                    sqlite_conn.Open();
                    if (sqlite_conn.State == ConnectionState.Open)
                    {
                        using (SqlCommand sqlCmd = new SqlCommand())
                        {
                            sqlCmd.Connection = sqlite_conn;
                            sqlCmd.CommandText = "IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @TableName) " +
                                                 "BEGIN " +
                                                 "CREATE TABLE " + TableString + " (Id INT PRIMARY KEY IDENTITY, opcMsgId INT, deviceId INT) " +
                                                 "END";
                            sqlCmd.Parameters.AddWithValue("@TableName", TableString);

                            sqlCmd.ExecuteNonQuery();
                        }
                    }
                    sqlite_conn.Close();
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
        }

        public List<TempOpcMsgDev_InfoDataDb> ReadAll()
        {
            List<TempOpcMsgDev_InfoDataDb> responseBuf = new List<TempOpcMsgDev_InfoDataDb>();
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        string commandStringBuf = "SELECT * FROM " + TableString;
                        using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                        {
                            using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                            {
                                // Call Read before accessing data.
                                while (readerBuf.Read())
                                {
                                    if (readerBuf.FieldCount == 3)
                                    {
                                        TempOpcMsgDev_InfoDataDb deviceInfoBuf = new TempOpcMsgDev_InfoDataDb();

                                        deviceInfoBuf.Id = readerBuf.GetInt32(0);
                                        deviceInfoBuf.opcMsgId = readerBuf.GetInt32(1);
                                        deviceInfoBuf.deviceId = readerBuf.GetInt32(2);

                                        responseBuf.Add(deviceInfoBuf);
                                    }
                                }
                                // Call Close when done reading.
                                readerBuf.Close();
                            }
                        }
                        sqlConnectionBuf.Close();
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
            return responseBuf;
        }

        public List<TempOpcMsgDev_InfoDataDb> ReadByDeviceId(int deviceId)
        {
            List<TempOpcMsgDev_InfoDataDb> responseBuf = new List<TempOpcMsgDev_InfoDataDb>();
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        try
                        {
                            string commandStringBuf = "SELECT * FROM " + TableString + " WHERE   deviceId  =" + deviceId;
                            using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                            {
                                using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                                {
                                    while (readerBuf.Read())
                                    {
                                        if (readerBuf.FieldCount == 3)
                                        {
                                            TempOpcMsgDev_InfoDataDb deviceInfoBuf = new TempOpcMsgDev_InfoDataDb();

                                            deviceInfoBuf.Id = readerBuf.GetInt32(0);
                                            deviceInfoBuf.opcMsgId = readerBuf.GetInt32(1);
                                            deviceInfoBuf.deviceId = readerBuf.GetInt32(2);

                                            responseBuf.Add(deviceInfoBuf);
                                        }
                                    }
                                    // Call Close when done reading.
                                    readerBuf.Close();
                                }
                            }

                        }
                        catch (Exception err)
                        {
                            //MessageBox.Show(err.ToString());
                        }
                        sqlConnectionBuf.Close();
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
            return responseBuf;
        }

        public List<TempOpcMsgDev_InfoDataDb> ReadBy_OpcmsgIdandDeviceId(int opcMsgId, int deviceId)
        {
            List<TempOpcMsgDev_InfoDataDb> responseBuf = new List<TempOpcMsgDev_InfoDataDb>();
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        try
                        {
                            string commandStringBuf = "SELECT * FROM " + TableString + " WHERE   opcMsgId  =" + opcMsgId + " AND deviceId =" + deviceId + " ;";
                            using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                            {
                                using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                                {
                                    while (readerBuf.Read())
                                    {
                                        if (readerBuf.FieldCount == 3)
                                        {
                                            TempOpcMsgDev_InfoDataDb deviceInfoBuf = new TempOpcMsgDev_InfoDataDb();

                                            deviceInfoBuf.Id = readerBuf.GetInt32(0);
                                            deviceInfoBuf.opcMsgId = readerBuf.GetInt32(1);
                                            deviceInfoBuf.deviceId = readerBuf.GetInt32(2);

                                            responseBuf.Add(deviceInfoBuf);
                                        }
                                    }
                                    // Call Close when done reading.
                                    readerBuf.Close();
                                }
                            }
                        }
                        catch (Exception err)
                        {
                            //MessageBox.Show(err.ToString());
                        }
                        sqlConnectionBuf.Close();
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
            return responseBuf;
        }

        public TempOpcMsgDev_InfoDataDb Add(TempOpcMsgDev_InfoDataDb newDeviceInfo)
        {
            TempOpcMsgDev_InfoDataDb responseBuf = null;
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        try
                        {
                            string commandStringBuf = "INSERT INTO " + TableString + " (opcMsgId,deviceId) VALUES " +
                                                                                "( @opcMsgId,@deviceId)";
                            using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                            {
                                commandBuf.Parameters.AddWithValue("@opcMsgId", newDeviceInfo.opcMsgId);
                                commandBuf.Parameters.AddWithValue("@deviceId", newDeviceInfo.deviceId);
                                commandBuf.ExecuteNonQuery();
                                responseBuf = newDeviceInfo;
                            }
                        }
                        catch (Exception err)
                        {
                            //MessageBox.Show(err.ToString());
                        }
                        sqlConnectionBuf.Close();
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
            return responseBuf;
        }
        public int DeleteByDeviceId(int deviceId)
        {
            int RowEffect = 0;
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        try
                        {
                            string commandStringBuf = "DELETE FROM " + TableString + " WHERE deviceId = " + deviceId + ";";
                            Console.WriteLine(commandStringBuf);
                            using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                            {
                                int rowEffect = commandBuf.ExecuteNonQuery();
                            }
                        }
                        catch (Exception err)
                        {
                            //MessageBox.Show(err.ToString());
                        }
                        sqlConnectionBuf.Close();
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
            return RowEffect;
        }

        public int DeleteByOpcmsgId(int opcmsgId)
        {
            int RowEffect = 0;
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        try
                        {
                            string commandStringBuf = "DELETE FROM " + TableString + " WHERE opcMsgId = " + opcmsgId + ";";
                            Console.WriteLine(commandStringBuf);
                            using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                            {
                                commandBuf.CommandText = commandStringBuf;

                                RowEffect = commandBuf.ExecuteNonQuery();
                            }
                        }
                        catch (Exception err)
                        {
                            //MessageBox.Show(err.ToString());
                        }
                        sqlConnectionBuf.Close();
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
            return RowEffect;
        }
    }
}
