﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Windows.Forms;

namespace WindowsFormsCargill.DB
{
    public partial class MsgOpc_InfoDataDb // for Read struct only
    {
        public int ID { get; set; }
        public int gatewayID { get; set; }
        public string AddressNode { get; set; } // address off struct node
        public int TimeGet { get; set; }

        public MsgOpc_InfoDataDb()
        {
            AddressNode = "";
        }
    }

    public class OpcMsgDB
    {
        private readonly string connectionString;
        private readonly string TableString;

        public OpcMsgDB(string connection)
        {
            connectionString = connection;
            TableString = "OpcConnectionMsgTable";
            // Uncomment if you want to create the table when initializing
            // CreateTable();
        }

        public void CreateTable()
        {
            try
            {
                using (SqlConnection sqlConnection = new SqlConnection(connectionString))
                {
                    sqlConnection.Open();
                    if (sqlConnection.State == ConnectionState.Open)
                    {
                        string commandString = $@"
                            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='{TableString}' AND xtype='U')
                            BEGIN
                                CREATE TABLE {TableString} (
                                    ID INT IDENTITY(1,1) PRIMARY KEY,
                                    gatewayID INT,
                                    AddressNode NVARCHAR(MAX),
                                    TimeGet INT
                                )
                            END";

                        using (SqlCommand command = new SqlCommand(commandString, sqlConnection))
                        {
                            command.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch (Exception err)
            {
                ShowError(err);
            }
        }

        public List<MsgOpc_InfoDataDb> ReadAll()
        {
            var responseBuf = new List<MsgOpc_InfoDataDb>();
            try
            {
                using (SqlConnection sqlConnection = new SqlConnection(connectionString))
                {
                    sqlConnection.Open();
                    if (sqlConnection.State == ConnectionState.Open)
                    {
                        string commandString = $"SELECT * FROM {TableString}";
                        using (SqlCommand command = new SqlCommand(commandString, sqlConnection))
                        {
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    var deviceInfo = new MsgOpc_InfoDataDb
                                    {
                                        ID = reader.GetInt32(0),
                                        gatewayID = reader.GetInt32(1),
                                        AddressNode = reader.GetString(2),
                                        TimeGet = reader.GetInt32(3)
                                    };

                                    responseBuf.Add(deviceInfo);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception err)
            {
                ShowError(err);
            }
            return responseBuf;
        }

        public List<int> ReadAllID()
        {
            var responseBuf = new List<int>();
            try
            {
                using (SqlConnection sqlConnection = new SqlConnection(connectionString))
                {
                    sqlConnection.Open();
                    if (sqlConnection.State == ConnectionState.Open)
                    {
                        string commandString = $"SELECT TOP 1000 ID FROM {TableString}";
                        using (SqlCommand command = new SqlCommand(commandString, sqlConnection))
                        {
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    responseBuf.Add(reader.GetInt32(0));
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception err)
            {
                ShowError(err);
            }
            return responseBuf;
        }

        public List<MsgOpc_InfoDataDb> ReadOpcMsgInfo_ByGatewayId(int gatewayId)
        {
            var responseBuf = new List<MsgOpc_InfoDataDb>();
            try
            {
                using (SqlConnection sqlConnection = new SqlConnection(connectionString))
                {
                    sqlConnection.Open();
                    if (sqlConnection.State == ConnectionState.Open)
                    {
                        string commandString = $"SELECT * FROM {TableString} WHERE gatewayID = @gatewayID";
                        using (SqlCommand command = new SqlCommand(commandString, sqlConnection))
                        {
                            command.Parameters.AddWithValue("@gatewayID", gatewayId);

                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    var deviceInfo = new MsgOpc_InfoDataDb
                                    {
                                        ID = reader.GetInt32(0),
                                        gatewayID = reader.GetInt32(1),
                                        AddressNode = reader.GetString(2),
                                        TimeGet = reader.GetInt32(3)
                                    };

                                    responseBuf.Add(deviceInfo);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception err)
            {
                ShowError(err);
            }
            return responseBuf;
        }

        public MsgOpc_InfoDataDb ReadById(int id)
        {
            var responseBuf = new MsgOpc_InfoDataDb();
            try
            {
                using (SqlConnection sqlConnection = new SqlConnection(connectionString))
                {
                    sqlConnection.Open();
                    if (sqlConnection.State == ConnectionState.Open)
                    {
                        string commandString = $"SELECT * FROM {TableString} WHERE ID = @ID";
                        using (SqlCommand command = new SqlCommand(commandString, sqlConnection))
                        {
                            command.Parameters.AddWithValue("@ID", id);

                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    responseBuf.ID = reader.GetInt32(0);
                                    responseBuf.gatewayID = reader.GetInt32(1);
                                    responseBuf.AddressNode = reader.GetString(2);
                                    responseBuf.TimeGet = reader.GetInt32(3);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception err)
            {
                ShowError(err);
            }
            return responseBuf;
        }

        public MsgOpc_InfoDataDb Add(MsgOpc_InfoDataDb newDeviceInfo)
        {
            try
            {
                using (SqlConnection sqlConnection = new SqlConnection(connectionString))
                {
                    sqlConnection.Open();
                    if (sqlConnection.State == ConnectionState.Open)
                    {
                        string commandString = $"INSERT INTO {TableString} (gatewayID, AddressNode, TimeGet) VALUES (@gatewayID, @AddressNode, @TimeGet);";
                        using (SqlCommand command = new SqlCommand(commandString, sqlConnection))
                        {
                            command.Parameters.AddWithValue("@gatewayID", newDeviceInfo.gatewayID);
                            command.Parameters.AddWithValue("@AddressNode", newDeviceInfo.AddressNode);
                            command.Parameters.AddWithValue("@TimeGet", newDeviceInfo.TimeGet);

                            command.ExecuteNonQuery();
                        }

                        return newDeviceInfo;
                    }
                }
            }
            catch (Exception err)
            {
                ShowError(err);
            }
            return null;
        }

        public int DeleteByGatewayID(int gatewayID)
        {
            int rowsAffected = 0;
            try
            {
                using (SqlConnection sqlConnection = new SqlConnection(connectionString))
                {
                    sqlConnection.Open();
                    if (sqlConnection.State == ConnectionState.Open)
                    {
                        string commandString = $"DELETE FROM {TableString} WHERE gatewayID = @gatewayID";
                        using (SqlCommand command = new SqlCommand(commandString, sqlConnection))
                        {
                            command.Parameters.AddWithValue("@gatewayID", gatewayID);
                            rowsAffected = command.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch (Exception err)
            {
                ShowError(err);
            }
            return rowsAffected;
        }

        public int DeleteByID(int id)
        {
            int rowsAffected = 0;
            try
            {
                using (SqlConnection sqlConnection = new SqlConnection(connectionString))
                {
                    sqlConnection.Open();
                    if (sqlConnection.State == ConnectionState.Open)
                    {
                        string commandString = $"DELETE FROM {TableString} WHERE ID = @ID";
                        using (SqlCommand command = new SqlCommand(commandString, sqlConnection))
                        {
                            command.Parameters.AddWithValue("@ID", id);
                            rowsAffected = command.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch (Exception err)
            {
                ShowError(err);
            }
            return rowsAffected;
        }

        public void UpdateMsgInfo(MsgOpc_InfoDataDb msgInfo)
        {
            try
            {
                using (SqlConnection sqlConnection = new SqlConnection(connectionString))
                {
                    sqlConnection.Open();
                    if (sqlConnection.State == ConnectionState.Open)
                    {
                        string commandString = $"UPDATE {TableString} SET gatewayID = @gatewayID, AddressNode = @AddressNode, TimeGet = @TimeGet WHERE ID = @ID";
                        using (SqlCommand command = new SqlCommand(commandString, sqlConnection))
                        {
                            command.Parameters.AddWithValue("@gatewayID", msgInfo.gatewayID);
                            command.Parameters.AddWithValue("@AddressNode", msgInfo.AddressNode);
                            command.Parameters.AddWithValue("@TimeGet", msgInfo.TimeGet);
                            command.Parameters.AddWithValue("@ID", msgInfo.ID);

                            command.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch (Exception err)
            {
                ShowError(err);
            }
        }

        private void ShowError(Exception err)
        {
            //MessageBox.Show(err.Message);
            Debug.WriteLine(err.Message);
        }
    }
}
