﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using WindowsFormsCargill.DB;
using System.Windows.Threading;
using WindowsFormsCargill.UI.DashboardEnergy;


namespace WindowsFormsCargill.UI.Widget.Device
{
    /// <summary>
    /// Interaction logic for DeviceWindowWidget.xaml
    /// </summary>
    public partial class DeviceWindowWidget : UserControl
    {
        int deviceID;
        string paraName;
        private readonly DispatcherTimer _dispatcherTimer;
        public EventHandler<object[]> SavePosButtonEvent;
        public class MetterPara
        {
            public string Name { get; set; }
            public double Value { get; set; }

            public DateTime Time { get; set; }
        }
        public class Data_Observer
        {
            public ObservableCollection<MetterPara> ListPara = new ObservableCollection<MetterPara>();
        }
        private Data_Observer DataObsever = new Data_Observer();

        public DeviceWindowWidget()
        {
            InitializeComponent();

            // Timer
            _dispatcherTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(5)
            };
            _dispatcherTimer.Tick += OnTimer;
            _dispatcherTimer.Start();
        }
        //protected override void OnClosing(CancelEventArgs e)
        //{
        //    //this.Visibility = Visibility.Hidden;
        //    //e.Cancel = true;
        //}

        private void OnTimer(object source, EventArgs e)
        {
            SetId(deviceID, paraName);
        }

        public void SetId(int deviceId, string Name)
        {
            // Update deviceid, paraName
            deviceID = deviceId;
            paraName = Name;
            XamlDeviceName.Text = Name;

            //List<TelemetryData> listTelemetry = MainWindow.apiDatabase.ReadLatestTelemetry_ByDeviceId(deviceId);

            // Update listTelemetry
            List<TelemetryData> listTelemetry = new List<TelemetryData>();
            for (int k = 0; k < MainWindow.systemState.listDevState.Count; k++)
            {
                if (MainWindow.systemState.listDevState[k].deviceId == deviceId)
                {
                    listTelemetry = MainWindow.systemState.listDevState[k].listTelemetry;
                }
            }

            DataObsever.ListPara.Clear();

            if (listTelemetry != null)
            {
                for (int i = 0; i < listTelemetry.Count; i++)
                {
                    MetterPara metter = new MetterPara();

                    metter.Name = listTelemetry[i].Name;
                    metter.Value = listTelemetry[i].Value;
                    metter.Time = listTelemetry[i].Time;
                    DataObsever.ListPara.Add(metter);
                }
            }

            XamlMeterPara.ItemsSource = DataObsever.ListPara;
            CollectionViewSource.GetDefaultView(XamlMeterPara.ItemsSource).Refresh();
        }

        private void SavePOS_BtOnClick(object sender, RoutedEventArgs e)
        {
            //MainWindow.uiEnegryMain.OnHandelUpdateProperty();
            if (SavePosButtonEvent != null)
            {
                object[] array = new object[2] { float.Parse(Xpos_Textbox.Text), float.Parse(Ypos_Textbox.Text)};

                SavePosButtonEvent.Invoke(this, array);
            }
            MainWindow.apiDatabase.UpdateDevicePosition(deviceID,float.Parse(Xpos_Textbox.Text), float.Parse(Ypos_Textbox.Text));
        }
    }
}
