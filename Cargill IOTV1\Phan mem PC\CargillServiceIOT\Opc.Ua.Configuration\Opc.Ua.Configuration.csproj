﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFramework>net48</TargetFramework>
	</PropertyGroup>
  <PropertyGroup>
    <AssemblyName>Opc.Ua.Configuration</AssemblyName>
    <TargetFrameworks>$(LibTargetFrameworks)</TargetFrameworks>
    <PackageId>OPCFoundation.NetStandard.Opc.Ua.Configuration</PackageId>
    <RootNamespace>Opc.Ua.Configuration</RootNamespace>
    <Description>OPC UA Configuration Class Library</Description>
    <IsPackable>true</IsPackable>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <PackageLicenseFile></PackageLicenseFile>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)' == 'Debug'">
    <PackageId>$(PackageId).Debug</PackageId>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Properties\**" />
    <EmbeddedResource Remove="Properties\**" />
    <None Remove="Properties\**" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Opc.Ua.Core\Opc.Ua.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System.Windows.Forms" />
  </ItemGroup>

  <PropertyGroup Condition="'$(SignAssembly)' == 'true'">
    <DefineConstants>$(DefineConstants);SIGNASSEMBLY</DefineConstants>
  </PropertyGroup>
  
  <Target Name="GetPackagingOutputs" />

</Project>
