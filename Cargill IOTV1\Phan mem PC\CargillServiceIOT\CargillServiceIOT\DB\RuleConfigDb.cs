﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Windows.Forms;

namespace WindowsFormsCargill.DB
{
    public enum FUNCTION_NAME
    {
        LimitLowFuntion,
        LimitHighFuntion
    }

    public class RuleConfig_InfoDb
    {
        public int ID { get; set; }
        public int DevId { get; set; }
        public string Name { get; set; }
        public FUNCTION_NAME FunctionName { get; set; }
        public string InputNam { get; set; }
        public float LimitValue { get; set; }
        public float Delta { get; set; }
        public string EventName { get; set; }
        public EVENT_TYPE EventType { get; set; }
        public DateTime TimeCreat { get; set; }
    }

    public class RuleConfigDb
    {
        private string connectionString;
        private string TableString;

        public RuleConfigDb(string connection)
        {
            connectionString = connection;
            TableString = "RuleConfigTable";
        }

        public void CreateTable()
        {
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        string checkTableQuery = $"IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '{TableString}') " +
                                                 $"CREATE TABLE {TableString} (ID INT PRIMARY KEY IDENTITY, DevId INT, Name NVARCHAR(255), FunctionName INT, InputNam NVARCHAR(255), LimitValue FLOAT, Delta FLOAT, EventName NVARCHAR(255), EventType INT, TimeCreat DATETIME)";
                        using (SqlCommand commandBuf = new SqlCommand(checkTableQuery, sqlConnectionBuf))
                        {
                            commandBuf.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
        }

        public List<RuleConfig_InfoDb> ReadAll()
        {
            List<RuleConfig_InfoDb> responseBuf = new List<RuleConfig_InfoDb>();
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        string commandStringBuf = $"SELECT * FROM {TableString}";
                        using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                        {
                            using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                            {
                                while (readerBuf.Read())
                                {
                                    if (readerBuf.FieldCount == 10)
                                    {
                                        RuleConfig_InfoDb deviceInfoBuf = new RuleConfig_InfoDb
                                        {
                                            ID = readerBuf.GetInt32(0),
                                            DevId = readerBuf.GetInt32(1),
                                            Name = readerBuf["Name"].ToString(),
                                            FunctionName = (FUNCTION_NAME)readerBuf.GetInt32(3),
                                            InputNam = readerBuf["InputNam"].ToString(),
                                            LimitValue = readerBuf.GetFloat(5),
                                            Delta = readerBuf.GetFloat(6),
                                            EventName = readerBuf["EventName"].ToString(),
                                            EventType = (EVENT_TYPE)readerBuf.GetInt32(8),
                                            TimeCreat = readerBuf.GetDateTime(9)
                                        };
                                        responseBuf.Add(deviceInfoBuf);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
            return responseBuf;
        }

        public List<RuleConfig_InfoDb> ReadByDeviceId(int deviceId)
        {
            List<RuleConfig_InfoDb> responseBuf = new List<RuleConfig_InfoDb>();
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        string commandStringBuf = $"SELECT * FROM {TableString} WHERE DevId = @deviceId";
                        using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                        {
                            commandBuf.Parameters.AddWithValue("@deviceId", deviceId);
                            using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                            {
                                while (readerBuf.Read())
                                {
                                    if (readerBuf.FieldCount == 10)
                                    {
                                        RuleConfig_InfoDb deviceInfoBuf = new RuleConfig_InfoDb();

                                        deviceInfoBuf.ID = readerBuf.GetInt32(0);
                                        deviceInfoBuf.DevId = readerBuf.GetInt32(1);
                                        deviceInfoBuf.Name = readerBuf["Name"].ToString();
                                        deviceInfoBuf.FunctionName = (FUNCTION_NAME)readerBuf.GetInt32(3);
                                        deviceInfoBuf.InputNam = readerBuf["InputNam"].ToString();
                                        deviceInfoBuf.LimitValue = readerBuf.GetFloat(5);
                                        deviceInfoBuf.Delta = readerBuf.GetFloat(6);
                                        deviceInfoBuf.EventName = readerBuf["EventName"].ToString();
                                        deviceInfoBuf.EventType = (EVENT_TYPE)readerBuf.GetInt32(8);
                                        deviceInfoBuf.TimeCreat = readerBuf.GetDateTime(9);
                                        
                                        responseBuf.Add(deviceInfoBuf);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
            return responseBuf;
        }

        public bool Add(RuleConfig_InfoDb newRule)
        {
            bool result = false;
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        string commandStringBuf = "INSERT INTO " + TableString + " (DevId, Name, FunctionName, InputNam, LimitValue, Delta, EventName, EventType, TimeCreat) VALUES " +
                                                  "(@DevId, @Name, @FunctionName, @InputNam, @LimitValue, @Delta, @EventName, @EventType, @TimeCreat)";
                        using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                        {
                            commandBuf.Parameters.AddWithValue("@DevId", newRule.DevId);
                            commandBuf.Parameters.AddWithValue("@Name", newRule.Name);
                            commandBuf.Parameters.AddWithValue("@FunctionName", (int)newRule.FunctionName);
                            commandBuf.Parameters.AddWithValue("@InputNam", newRule.InputNam);
                            commandBuf.Parameters.AddWithValue("@LimitValue", newRule.LimitValue);
                            commandBuf.Parameters.AddWithValue("@Delta", newRule.Delta);
                            commandBuf.Parameters.AddWithValue("@EventName", newRule.EventName);
                            commandBuf.Parameters.AddWithValue("@EventType", (int)newRule.EventType);
                            commandBuf.Parameters.AddWithValue("@TimeCreat", newRule.TimeCreat);

                            commandBuf.ExecuteNonQuery();
                            result = true;
                        }
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
            return result;
        }

        public int DeleteByID(int ruleId)
        {
            int responseBuf = 0;
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        string commandStringBuf = "DELETE FROM " + TableString + " WHERE ID = @ID";
                        using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                        {
                            commandBuf.Parameters.AddWithValue("@ID", ruleId);
                            commandBuf.ExecuteNonQuery();
                            responseBuf = ruleId;
                        }
                    }
                }
            }
            catch (Exception err)
            {
                StackFrame stackFrame = new StackTrace(1).GetFrame(1);
                //MessageBox.Show($"{stackFrame.GetFileName()}\n{stackFrame.GetMethod()}\n{stackFrame.GetFileLineNumber()}\n{err}");
            }
            return responseBuf;
        }

        public void UpdateRule(RuleConfig_InfoDb rule)
        {
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        string commandStringBuf = "UPDATE " + TableString + " SET DevId = @DevId, Name = @Name, FunctionName = @FunctionName, InputNam = @InputNam, LimitValue = @LimitValue, Delta = @Delta, EventName = @EventName, EventType = @EventType, TimeCreat = @TimeCreat WHERE ID = @ID";
                        using (SqlCommand command = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                        {
                            command.Parameters.AddWithValue("@DevId", rule.DevId);
                            command.Parameters.AddWithValue("@Name", rule.Name);
                            command.Parameters.AddWithValue("@FunctionName", (int)rule.FunctionName);
                            command.Parameters.AddWithValue("@InputNam", rule.InputNam);
                            command.Parameters.AddWithValue("@LimitValue", rule.LimitValue);
                            command.Parameters.AddWithValue("@Delta", rule.Delta);
                            command.Parameters.AddWithValue("@EventName", rule.EventName);
                            command.Parameters.AddWithValue("@EventType", (int)rule.EventType);
                            command.Parameters.AddWithValue("@TimeCreat", rule.TimeCreat);
                            command.Parameters.AddWithValue("@ID", rule.ID);

                            command.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch (Exception err)
            {
                StackFrame stackFrame = new StackTrace(1).GetFrame(1);
                //MessageBox.Show($"{stackFrame.GetFileName()}\n{stackFrame.GetMethod()}\n{stackFrame.GetFileLineNumber()}\n{err}");
            }
        }
    }
}
