<?xml version="1.0" encoding="utf-8" ?>
<!--
 * Copyright (c) 2005-2022 The OPC Foundation, Inc. All rights reserved.
 *
 * OPC Foundation MIT License 1.00
 * 
 * Permission is hereby granted, free of charge, to any person
 * obtaining a copy of this software and associated documentation
 * files (the "Software"), to deal in the Software without
 * restriction, including without limitation the rights to use,
 * copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following
 * conditions:
 * 
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 * OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 * HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 * WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 * OTHER DEALINGS IN THE SOFTWARE.
 *
 * The complete license agreement can be found here:
 * http://opcfoundation.org/License/MIT/1.00/
-->

<xs:schema
  xmlns:xs="http://www.w3.org/2001/XMLSchema"
  xmlns:ua="http://opcfoundation.org/UA/2008/02/Types.xsd"
  xmlns:tns="http://opcfoundation.org/UA/2008/02/Types.xsd"
  targetNamespace="http://opcfoundation.org/UA/2008/02/Types.xsd"
  elementFormDefault="qualified"
>
  <xs:annotation>
    <xs:appinfo>
      <tns:Model ModelUri="http://opcfoundation.org/UA/" Version="1.04.11" PublicationDate="2022-03-29T00:00:00Z" />
    </xs:appinfo>
  </xs:annotation>
  
  <xs:element name="Boolean" type="xs:boolean" />

  <xs:complexType name="ListOfBoolean">
    <xs:sequence>
      <xs:element name="Boolean" type="xs:boolean" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfBoolean" type="tns:ListOfBoolean" nillable="true"></xs:element>

  <xs:element name="SByte" type="xs:byte" />

  <xs:complexType name="ListOfSByte">
    <xs:sequence>
      <xs:element name="SByte" type="xs:byte" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfSByte" type="tns:ListOfSByte" nillable="true"></xs:element>

  <xs:element name="Byte" type="xs:unsignedByte" />

  <xs:complexType name="ListOfByte">
    <xs:sequence>
      <xs:element name="Byte" type="xs:unsignedByte" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfByte" type="tns:ListOfByte" nillable="true"></xs:element>

  <xs:element name="Int16" type="xs:short" />

  <xs:complexType name="ListOfInt16">
    <xs:sequence>
      <xs:element name="Int16" type="xs:short" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfInt16" type="tns:ListOfInt16" nillable="true"></xs:element>

  <xs:element name="UInt16" type="xs:unsignedShort" />

  <xs:complexType name="ListOfUInt16">
    <xs:sequence>
      <xs:element name="UInt16" type="xs:unsignedShort" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfUInt16" type="tns:ListOfUInt16" nillable="true"></xs:element>

  <xs:element name="Int32" type="xs:int" />

  <xs:complexType name="ListOfInt32">
    <xs:sequence>
      <xs:element name="Int32" type="xs:int" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfInt32" type="tns:ListOfInt32" nillable="true"></xs:element>

  <xs:element name="UInt32" type="xs:unsignedInt" />

  <xs:complexType name="ListOfUInt32">
    <xs:sequence>
      <xs:element name="UInt32" type="xs:unsignedInt" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfUInt32" type="tns:ListOfUInt32" nillable="true"></xs:element>

  <xs:element name="Int64" type="xs:long" />

  <xs:complexType name="ListOfInt64">
    <xs:sequence>
      <xs:element name="Int64" type="xs:long" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfInt64" type="tns:ListOfInt64" nillable="true"></xs:element>

  <xs:element name="UInt64" type="xs:unsignedLong" />

  <xs:complexType name="ListOfUInt64">
    <xs:sequence>
      <xs:element name="UInt64" type="xs:unsignedLong" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfUInt64" type="tns:ListOfUInt64" nillable="true"></xs:element>

  <xs:element name="Float" type="xs:float" />

  <xs:complexType name="ListOfFloat">
    <xs:sequence>
      <xs:element name="Float" type="xs:float" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfFloat" type="tns:ListOfFloat" nillable="true"></xs:element>

  <xs:element name="Double" type="xs:double" />

  <xs:complexType name="ListOfDouble">
    <xs:sequence>
      <xs:element name="Double" type="xs:double" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfDouble" type="tns:ListOfDouble" nillable="true"></xs:element>

  <xs:element name="String" nillable="true" type="xs:string" />

  <xs:complexType name="ListOfString">
    <xs:sequence>
      <xs:element name="String" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfString" type="tns:ListOfString" nillable="true"></xs:element>

  <xs:element name="DateTime" nillable="true" type="xs:dateTime" />

  <xs:complexType name="ListOfDateTime">
    <xs:sequence>
      <xs:element name="DateTime" type="xs:dateTime" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfDateTime" type="tns:ListOfDateTime" nillable="true"></xs:element>

  <xs:complexType name="Guid">
    <xs:annotation>
      <xs:appinfo>
        <IsValueType xmlns="http://schemas.microsoft.com/2003/10/Serialization/">true</IsValueType>
      </xs:appinfo>
    </xs:annotation>
    <xs:sequence>
      <xs:element name="String" type="xs:string" minOccurs="0" maxOccurs="1" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Guid" type="tns:Guid" nillable="true"></xs:element>

  <xs:complexType name="ListOfGuid">
    <xs:sequence>
      <xs:element name="Guid" type="tns:Guid" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfGuid" type="tns:ListOfGuid" nillable="true"></xs:element>

  <xs:element name="ByteString" nillable="true" type="xs:base64Binary" />

  <xs:complexType name="ListOfByteString">
    <xs:sequence>
      <xs:element name="ByteString" type="xs:base64Binary" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfByteString" type="tns:ListOfByteString" nillable="true"></xs:element>

  <xs:complexType name="ListOfXmlElement">
    <xs:sequence>
      <xs:element name="XmlElement" minOccurs="0" maxOccurs="unbounded" nillable="true">
        <xs:complexType>
          <xs:sequence>
            <xs:any minOccurs="0" processContents="lax"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfXmlElement" type="tns:ListOfXmlElement" nillable="true"></xs:element>

  <xs:complexType name="NodeId">
    <xs:sequence>
      <xs:element name="Identifier" type="xs:string" minOccurs="0" maxOccurs="1" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="NodeId" type="tns:NodeId" nillable="true"></xs:element>

  <xs:complexType name="ListOfNodeId">
    <xs:sequence>
      <xs:element name="NodeId" type="tns:NodeId" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfNodeId" type="tns:ListOfNodeId" nillable="true"></xs:element>

  <xs:complexType name="ExpandedNodeId">
    <xs:sequence>
      <xs:element name="Identifier" type="xs:string" minOccurs="0" maxOccurs="1" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ExpandedNodeId" type="tns:ExpandedNodeId" nillable="true"></xs:element>

  <xs:complexType name="ListOfExpandedNodeId">
    <xs:sequence>
      <xs:element name="ExpandedNodeId" type="tns:ExpandedNodeId" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfExpandedNodeId" type="tns:ListOfExpandedNodeId" nillable="true"></xs:element>

  <xs:complexType name="StatusCode">
    <xs:annotation>
      <xs:appinfo>
        <IsValueType xmlns="http://schemas.microsoft.com/2003/10/Serialization/">true</IsValueType>
      </xs:appinfo>
    </xs:annotation>
    <xs:sequence>
      <xs:element name="Code" type="xs:unsignedInt" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="StatusCode" type="tns:StatusCode"></xs:element>

  <xs:complexType name="ListOfStatusCode">
    <xs:sequence>
      <xs:element name="StatusCode" type="tns:StatusCode" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfStatusCode" type="tns:ListOfStatusCode" nillable="true"></xs:element>

  <xs:complexType name="DiagnosticInfo">
    <xs:sequence>
      <xs:element name="SymbolicId" type="xs:int" minOccurs="0" />
      <xs:element name="NamespaceUri" type="xs:int" minOccurs="0" />
      <xs:element name="Locale" type="xs:int" minOccurs="0" />
      <xs:element name="LocalizedText" type="xs:int" minOccurs="0" />
      <xs:element name="AdditionalInfo" type="xs:string" minOccurs="0" />
      <xs:element name="InnerStatusCode" type="tns:StatusCode" minOccurs="0" />
      <xs:element name="InnerDiagnosticInfo" type="tns:DiagnosticInfo" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="DiagnosticInfo" type="tns:DiagnosticInfo" nillable="true"></xs:element>

  <xs:complexType name="ListOfDiagnosticInfo">
    <xs:sequence>
      <xs:element name="DiagnosticInfo" type="tns:DiagnosticInfo" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfDiagnosticInfo" type="tns:ListOfDiagnosticInfo" nillable="true"></xs:element>

  <xs:complexType name="LocalizedText">
    <xs:sequence>
      <xs:element name="Locale" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="Text" type="xs:string" minOccurs="0"  nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="LocalizedText" type="tns:LocalizedText" nillable="true" />

  <xs:complexType name="ListOfLocalizedText">
    <xs:sequence>
      <xs:element name="LocalizedText" type="tns:LocalizedText" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfLocalizedText" type="tns:ListOfLocalizedText" nillable="true"></xs:element>

  <xs:complexType name="QualifiedName">
    <xs:sequence>
      <xs:element name="NamespaceIndex" type="xs:unsignedShort" minOccurs="0" />
      <xs:element name="Name" type="xs:string" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="QualifiedName" type="tns:QualifiedName" nillable="true" />

  <xs:complexType name="ListOfQualifiedName">
    <xs:sequence>
      <xs:element name="QualifiedName" type="tns:QualifiedName" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfQualifiedName" type="tns:ListOfQualifiedName" nillable="true"></xs:element>

  <!--
    Some environments require a WSDL/XSD which explicitly defines all possible types.
    The UA WSDL/XSD can be modified to support these environments by replacing the
    definitions of the ExtensionObjectBody and VariantValue complex types with the
    definitions in the comments shown here. Developers would then define subtypes
    of the ExtensionObjectBody type which explicitly declare a choice between all of the
    complex types used by the system. The ExampleExtensionObjectBody subtype is provides
    a template based on a few common UA-defined complex types.
    -->

  <!--
    <xs:complexType name="ExtensionObjectBody" />

    <xs:complexType name="ExampleExtensionObjectBody">
      <xs:complexContent>
        <xs:extension base="tns:ExtensionObjectBody">
          <xs:choice>
            <xs:element name="Argument" type="tns:Argument" minOccurs="0" nillable="true" />
            <xs:element name="UserIdentityToken" type="tns:UserIdentityToken" minOccurs="0" nillable="true" />
            <xs:element name="UserNameIdentityToken" type="tns:UserNameIdentityToken" minOccurs="0" nillable="true" />
          </xs:choice>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="ExtensionObject">
      <xs:sequence>
        <xs:element name="TypeId" type="tns:ExpandedNodeId" minOccurs="0" nillable="true" />
        <xs:element name="Body" minOccurs="0" type="tns:ExtensionObjectBody" nillable="true" />
      </xs:sequence>
    </xs:complexType>
    <xs:element name="ExtensionObject" type="tns:ExtensionObject" nillable="true" />
    -->

  <xs:complexType name="ExtensionObject">
    <xs:sequence>
      <xs:element name="TypeId" type="tns:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="Body" minOccurs="0" nillable="true">
        <xs:complexType>
          <xs:sequence>
            <xs:any minOccurs="0" processContents="lax"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ExtensionObject" type="tns:ExtensionObject" nillable="true" />

  <xs:complexType name="ListOfExtensionObject">
    <xs:sequence>
      <xs:element name="ExtensionObject" type="tns:ExtensionObject" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfExtensionObject" type="tns:ListOfExtensionObject" nillable="true"></xs:element>

  <!--
    Some WSDL/XML compilers have issues with the xs:choice construct. For that reason
    the default declaration of a Variant uses xs:any construct. The schema acutually
    defined by the specification is provided by the Matrix and VariantValue complex types
    shown in comments below. Application developers can replace the VariantValue declaration
    with the specific declaration if they have a development environment that can handle
    the xs:choice construct in a reasonable way.
    -->

  <!--
    <xs:complexType name="Matrix">
      <xs:sequence>
        <xs:element name="Dimensions" type="tns:ListOfUInt32" minOccurs="0" nillable="true" />
        <xs:element name="Value" minOccurs="0" nillable="true">
          <xs:complexType mixed="false">
            <xs:choice maxOccurs="unbounded">
              <xs:element name="Boolean" type="xs:boolean" minOccurs="0" />
              <xs:element name="SByte" type="xs:byte" minOccurs="0" />
              <xs:element name="Byte" type="xs:unsignedByte" minOccurs="0" />
              <xs:element name="Int16" type="xs:short" minOccurs="0" />
              <xs:element name="UInt16" type="xs:unsignedShort" minOccurs="0" />
              <xs:element name="Int32" type="xs:int" minOccurs="0" />
              <xs:element name="UInt32" type="xs:unsignedInt" minOccurs="0" />
              <xs:element name="Int64" type="xs:long" minOccurs="0" />
              <xs:element name="UInt64" type="xs:unsignedLong" minOccurs="0" />
              <xs:element name="Float" type="xs:float" minOccurs="0" />
              <xs:element name="Double" type="xs:double" minOccurs="0" />
              <xs:element name="String" type="xs:string" minOccurs="0" />
              <xs:element name="DateTime" type="xs:dateTime" minOccurs="0" />
              <xs:element name="Guid" type="tns:Guid" minOccurs="0" />
              <xs:element name="ByteString" type="xs:base64Binary" minOccurs="0" />
              <xs:element name="XmlElement" minOccurs="0" nillable="true">
                <xs:complexType>
                  <xs:sequence>
                    <xs:any minOccurs="0" processContents="lax" />
                  </xs:sequence>
                </xs:complexType>
              </xs:element>
              <xs:element name="StatusCode" type="tns:StatusCode" minOccurs="0" />
              <xs:element name="NodeId" type="tns:NodeId" minOccurs="0" />
              <xs:element name="ExpandedNodeId" type="tns:ExpandedNodeId" minOccurs="0" />
              <xs:element name="QualifiedName" type="tns:QualifiedName" minOccurs="0" />
              <xs:element name="LocalizedText" type="tns:LocalizedText" minOccurs="0" />
              <xs:element name="ExtensionObject" type="tns:ExtensionObject" minOccurs="0" />
              <xs:element name="Variant" type="tns:Variant" minOccurs="0" />
            </xs:choice>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
    <xs:element name="Matrix" type="tns:Matrix" nillable="true" />

    <xs:complexType name="VariantValue">
      <xs:choice>
        <xs:element name="Boolean" type="xs:boolean" minOccurs="0" />
        <xs:element name="SByte" type="xs:byte" minOccurs="0" />
        <xs:element name="Byte" type="xs:unsignedByte" minOccurs="0" />
        <xs:element name="Int16" type="xs:short" minOccurs="0" />
        <xs:element name="UInt16" type="xs:unsignedShort" minOccurs="0" />
        <xs:element name="Int32" type="xs:int" minOccurs="0" />
        <xs:element name="UInt32" type="xs:unsignedInt" minOccurs="0" />
        <xs:element name="Int64" type="xs:long" minOccurs="0" />
        <xs:element name="UInt64" type="xs:unsignedLong" minOccurs="0" />
        <xs:element name="Float" type="xs:float" minOccurs="0" />
        <xs:element name="Double" type="xs:double" minOccurs="0" />
        <xs:element name="String" type="xs:string" minOccurs="0" />
        <xs:element name="DateTime" type="xs:dateTime" minOccurs="0" />
        <xs:element name="Guid" type="tns:Guid" minOccurs="0" />
        <xs:element name="ByteString" type="xs:base64Binary" minOccurs="0" />
        <xs:element name="XmlElement" minOccurs="0" nillable="true">
          <xs:complexType>
            <xs:sequence>
              <xs:any minOccurs="0" processContents="lax" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="StatusCode" type="tns:StatusCode" minOccurs="0" />
        <xs:element name="NodeId" type="tns:NodeId" minOccurs="0" />
        <xs:element name="ExpandedNodeId" type="tns:ExpandedNodeId" minOccurs="0" />
        <xs:element name="QualifiedName" type="tns:QualifiedName" minOccurs="0" />
        <xs:element name="LocalizedText" type="tns:LocalizedText" minOccurs="0" />
        <xs:element name="ExtensionObject" type="tns:ExtensionObject" minOccurs="0" />
        <xs:element name="ListOfBoolean" type="tns:ListOfBoolean" minOccurs="0" />
        <xs:element name="ListOfSByte" type="tns:ListOfSByte" minOccurs="0" />
        <xs:element name="ListOfByte" type="tns:ListOfByte" minOccurs="0" />
        <xs:element name="ListOfInt16" type="tns:ListOfInt16" minOccurs="0" />
        <xs:element name="ListOfUInt16" type="tns:ListOfUInt16" minOccurs="0" />
        <xs:element name="ListOfInt32" type="tns:ListOfInt32" minOccurs="0" />
        <xs:element name="ListOfUInt32" type="tns:ListOfUInt32" minOccurs="0" />
        <xs:element name="ListOfInt64" type="tns:ListOfInt64" minOccurs="0" />
        <xs:element name="ListOfUInt64" type="tns:ListOfUInt64" minOccurs="0" />
        <xs:element name="ListOfFloat" type="tns:ListOfFloat" minOccurs="0" />
        <xs:element name="ListOfDouble" type="tns:ListOfDouble" minOccurs="0" />
        <xs:element name="ListOfString" type="tns:ListOfString" minOccurs="0" />
        <xs:element name="ListOfDateTime" type="tns:ListOfDateTime" minOccurs="0" />
        <xs:element name="ListOfGuid" type="tns:ListOfGuid" minOccurs="0" />
        <xs:element name="ListOfByteString" type="tns:ListOfByteString" minOccurs="0" />
        <xs:element name="ListOfXmlElement" type="tns:ListOfXmlElement" minOccurs="0" />
        <xs:element name="ListOfStatusCode" type="tns:ListOfStatusCode" minOccurs="0" />
        <xs:element name="ListOfNodeId" type="tns:ListOfNodeId" minOccurs="0" />
        <xs:element name="ListOfExpandedNodeId" type="tns:ListOfExpandedNodeId" minOccurs="0" />
        <xs:element name="ListOfQualifiedName" type="tns:ListOfQualifiedName" minOccurs="0" />
        <xs:element name="ListOfLocalizedText" type="tns:ListOfLocalizedText" minOccurs="0" />
        <xs:element name="ListOfExtensionObject" type="tns:ListOfExtensionObject" minOccurs="0" />
        <xs:element name="ListOfVariant" type="tns:ListOfVariant" minOccurs="0" />
        <xs:element name="Matrix" type="tns:Matrix" minOccurs="0" />
      </xs:choice>
    </xs:complexType>

    <xs:complexType name="Variant">
      <xs:sequence>
        <xs:element name="Value" type="tns:VariantValue" minOccurs="0" nillable="true" />
      </xs:sequence>
    </xs:complexType>
    <xs:element name="Variant" type="tns:Variant" nillable="true" />
    -->

  <xs:complexType name="Variant">
    <xs:annotation>
      <xs:appinfo>
        <IsValueType xmlns="http://schemas.microsoft.com/2003/10/Serialization/">true</IsValueType>
      </xs:appinfo>
    </xs:annotation>
    <xs:sequence>
      <xs:element name="Value" minOccurs="0" nillable="true">
        <xs:complexType>
          <xs:sequence>
            <xs:any minOccurs="0" processContents="lax" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Variant" type="tns:Variant" nillable="true" />

  <xs:complexType name="ListOfVariant">
    <xs:sequence>
      <xs:element name="Variant" type="tns:Variant" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfVariant" type="tns:ListOfVariant" nillable="true"></xs:element>

  <xs:complexType name="DataValue">
    <xs:sequence>
      <xs:element name="Value" type="tns:Variant" minOccurs="0" />
      <xs:element name="StatusCode" type="tns:StatusCode" minOccurs="0" />
      <xs:element name="SourceTimestamp" type="xs:dateTime" minOccurs="0" />
      <xs:element name="SourcePicoseconds" type="xs:unsignedShort" minOccurs="0" />
      <xs:element name="ServerTimestamp" type="xs:dateTime" minOccurs="0" />
      <xs:element name="ServerPicoseconds" type="xs:unsignedShort" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="DataValue" type="tns:DataValue" nillable="true"/>

  <xs:complexType name="ListOfDataValue">
    <xs:sequence>
      <xs:element name="DataValue" type="tns:DataValue" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfDataValue" type="tns:ListOfDataValue" nillable="true"></xs:element>

  <xs:element name="InvokeServiceRequest" type="xs:base64Binary" nillable="true" />
  <xs:element name="InvokeServiceResponse" type="xs:base64Binary" nillable="true" />

  <xs:element name="ImageBMP" type="xs:base64Binary" />

  <xs:element name="ImageGIF" type="xs:base64Binary" />

  <xs:element name="ImageJPG" type="xs:base64Binary" />

  <xs:element name="ImagePNG" type="xs:base64Binary" />

  <xs:element name="AudioDataType" type="xs:base64Binary" />

  <xs:complexType name="Union">
    <xs:sequence>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Union" type="tns:Union" />

  <xs:complexType name="ListOfUnion">
    <xs:sequence>
      <xs:element name="Union" type="tns:Union" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfUnion" type="tns:ListOfUnion" nillable="true"></xs:element>

  <xs:element name="BitFieldMaskDataType" type="xs:unsignedLong" />

  <xs:complexType name="KeyValuePair">
    <xs:sequence>
      <xs:element name="Key" type="ua:QualifiedName" minOccurs="0" nillable="true" />
      <xs:element name="Value" type="ua:Variant" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="KeyValuePair" type="tns:KeyValuePair" />

  <xs:complexType name="ListOfKeyValuePair">
    <xs:sequence>
      <xs:element name="KeyValuePair" type="tns:KeyValuePair" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfKeyValuePair" type="tns:ListOfKeyValuePair" nillable="true"></xs:element>

  <xs:complexType name="AdditionalParametersType">
    <xs:sequence>
      <xs:element name="Parameters" type="tns:ListOfKeyValuePair" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="AdditionalParametersType" type="tns:AdditionalParametersType" />

  <xs:complexType name="EphemeralKeyType">
    <xs:sequence>
      <xs:element name="PublicKey" type="xs:base64Binary" minOccurs="0" nillable="true" />
      <xs:element name="Signature" type="xs:base64Binary" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="EphemeralKeyType" type="tns:EphemeralKeyType" />

  <xs:complexType name="EndpointType">
    <xs:sequence>
      <xs:element name="EndpointUrl" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="SecurityMode" type="tns:MessageSecurityMode" minOccurs="0" />
      <xs:element name="SecurityPolicyUri" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="TransportProfileUri" type="xs:string" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="EndpointType" type="tns:EndpointType" />

  <xs:complexType name="ListOfEndpointType">
    <xs:sequence>
      <xs:element name="EndpointType" type="tns:EndpointType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfEndpointType" type="tns:ListOfEndpointType" nillable="true"></xs:element>

  <xs:complexType name="RationalNumber">
    <xs:sequence>
      <xs:element name="Numerator" type="xs:int" minOccurs="0" />
      <xs:element name="Denominator" type="xs:unsignedInt" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RationalNumber" type="tns:RationalNumber" />

  <xs:complexType name="ListOfRationalNumber">
    <xs:sequence>
      <xs:element name="RationalNumber" type="tns:RationalNumber" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfRationalNumber" type="tns:ListOfRationalNumber" nillable="true"></xs:element>

  <xs:complexType name="Vector">
    <xs:sequence>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Vector" type="tns:Vector" />

  <xs:complexType name="ListOfVector">
    <xs:sequence>
      <xs:element name="Vector" type="tns:Vector" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfVector" type="tns:ListOfVector" nillable="true"></xs:element>

  <xs:complexType name="ThreeDVector">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:Vector">
        <xs:sequence>
          <xs:element name="X" type="xs:double" minOccurs="0" />
          <xs:element name="Y" type="xs:double" minOccurs="0" />
          <xs:element name="Z" type="xs:double" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="ThreeDVector" type="tns:ThreeDVector" />

  <xs:complexType name="ListOfThreeDVector">
    <xs:sequence>
      <xs:element name="ThreeDVector" type="tns:ThreeDVector" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfThreeDVector" type="tns:ListOfThreeDVector" nillable="true"></xs:element>

  <xs:complexType name="CartesianCoordinates">
    <xs:sequence>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CartesianCoordinates" type="tns:CartesianCoordinates" />

  <xs:complexType name="ListOfCartesianCoordinates">
    <xs:sequence>
      <xs:element name="CartesianCoordinates" type="tns:CartesianCoordinates" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfCartesianCoordinates" type="tns:ListOfCartesianCoordinates" nillable="true"></xs:element>

  <xs:complexType name="ThreeDCartesianCoordinates">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:CartesianCoordinates">
        <xs:sequence>
          <xs:element name="X" type="xs:double" minOccurs="0" />
          <xs:element name="Y" type="xs:double" minOccurs="0" />
          <xs:element name="Z" type="xs:double" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="ThreeDCartesianCoordinates" type="tns:ThreeDCartesianCoordinates" />

  <xs:complexType name="ListOfThreeDCartesianCoordinates">
    <xs:sequence>
      <xs:element name="ThreeDCartesianCoordinates" type="tns:ThreeDCartesianCoordinates" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfThreeDCartesianCoordinates" type="tns:ListOfThreeDCartesianCoordinates" nillable="true"></xs:element>

  <xs:complexType name="Orientation">
    <xs:sequence>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Orientation" type="tns:Orientation" />

  <xs:complexType name="ListOfOrientation">
    <xs:sequence>
      <xs:element name="Orientation" type="tns:Orientation" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfOrientation" type="tns:ListOfOrientation" nillable="true"></xs:element>

  <xs:complexType name="ThreeDOrientation">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:Orientation">
        <xs:sequence>
          <xs:element name="A" type="xs:double" minOccurs="0" />
          <xs:element name="B" type="xs:double" minOccurs="0" />
          <xs:element name="C" type="xs:double" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="ThreeDOrientation" type="tns:ThreeDOrientation" />

  <xs:complexType name="ListOfThreeDOrientation">
    <xs:sequence>
      <xs:element name="ThreeDOrientation" type="tns:ThreeDOrientation" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfThreeDOrientation" type="tns:ListOfThreeDOrientation" nillable="true"></xs:element>

  <xs:complexType name="Frame">
    <xs:sequence>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Frame" type="tns:Frame" />

  <xs:complexType name="ListOfFrame">
    <xs:sequence>
      <xs:element name="Frame" type="tns:Frame" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfFrame" type="tns:ListOfFrame" nillable="true"></xs:element>

  <xs:complexType name="ThreeDFrame">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:Frame">
        <xs:sequence>
          <xs:element name="CartesianCoordinates" type="tns:ThreeDCartesianCoordinates" minOccurs="0" nillable="true" />
          <xs:element name="Orientation" type="tns:ThreeDOrientation" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="ThreeDFrame" type="tns:ThreeDFrame" />

  <xs:complexType name="ListOfThreeDFrame">
    <xs:sequence>
      <xs:element name="ThreeDFrame" type="tns:ThreeDFrame" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfThreeDFrame" type="tns:ListOfThreeDFrame" nillable="true"></xs:element>

  <xs:simpleType  name="OpenFileMode">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Read_1" />
      <xs:enumeration value="Write_2" />
      <xs:enumeration value="EraseExisting_4" />
      <xs:enumeration value="Append_8" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="OpenFileMode" type="tns:OpenFileMode" />

  <xs:complexType name="ListOfOpenFileMode">
    <xs:sequence>
      <xs:element name="OpenFileMode" type="tns:OpenFileMode" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfOpenFileMode" type="tns:ListOfOpenFileMode" nillable="true"></xs:element>

  <xs:simpleType  name="IdentityCriteriaType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="UserName_1" />
      <xs:enumeration value="Thumbprint_2" />
      <xs:enumeration value="Role_3" />
      <xs:enumeration value="GroupId_4" />
      <xs:enumeration value="Anonymous_5" />
      <xs:enumeration value="AuthenticatedUser_6" />
      <xs:enumeration value="Application_7" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="IdentityCriteriaType" type="tns:IdentityCriteriaType" />

  <xs:complexType name="ListOfIdentityCriteriaType">
    <xs:sequence>
      <xs:element name="IdentityCriteriaType" type="tns:IdentityCriteriaType" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfIdentityCriteriaType" type="tns:ListOfIdentityCriteriaType" nillable="true"></xs:element>

  <xs:complexType name="IdentityMappingRuleType">
    <xs:sequence>
      <xs:element name="CriteriaType" type="tns:IdentityCriteriaType" minOccurs="0" />
      <xs:element name="Criteria" type="xs:string" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="IdentityMappingRuleType" type="tns:IdentityMappingRuleType" />

  <xs:complexType name="ListOfIdentityMappingRuleType">
    <xs:sequence>
      <xs:element name="IdentityMappingRuleType" type="tns:IdentityMappingRuleType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfIdentityMappingRuleType" type="tns:ListOfIdentityMappingRuleType" nillable="true"></xs:element>

  <xs:complexType name="CurrencyUnitType">
    <xs:sequence>
      <xs:element name="NumericCode" type="xs:short" minOccurs="0" />
      <xs:element name="Exponent" type="xs:byte" minOccurs="0" />
      <xs:element name="AlphabeticCode" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="Currency" type="ua:LocalizedText" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CurrencyUnitType" type="tns:CurrencyUnitType" />

  <xs:complexType name="ListOfCurrencyUnitType">
    <xs:sequence>
      <xs:element name="CurrencyUnitType" type="tns:CurrencyUnitType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfCurrencyUnitType" type="tns:ListOfCurrencyUnitType" nillable="true"></xs:element>

  <xs:simpleType  name="TrustListMasks">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None_0" />
      <xs:enumeration value="TrustedCertificates_1" />
      <xs:enumeration value="TrustedCrls_2" />
      <xs:enumeration value="IssuerCertificates_4" />
      <xs:enumeration value="IssuerCrls_8" />
      <xs:enumeration value="All_15" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="TrustListMasks" type="tns:TrustListMasks" />

  <xs:complexType name="TrustListDataType">
    <xs:sequence>
      <xs:element name="SpecifiedLists" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="TrustedCertificates" type="ua:ListOfByteString" minOccurs="0" nillable="true" />
      <xs:element name="TrustedCrls" type="ua:ListOfByteString" minOccurs="0" nillable="true" />
      <xs:element name="IssuerCertificates" type="ua:ListOfByteString" minOccurs="0" nillable="true" />
      <xs:element name="IssuerCrls" type="ua:ListOfByteString" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="TrustListDataType" type="tns:TrustListDataType" />

  <xs:complexType name="ListOfTrustListDataType">
    <xs:sequence>
      <xs:element name="TrustListDataType" type="tns:TrustListDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfTrustListDataType" type="tns:ListOfTrustListDataType" nillable="true"></xs:element>

  <xs:complexType name="DecimalDataType">
    <xs:sequence>
      <xs:element name="Scale" type="xs:short" minOccurs="0" />
      <xs:element name="Value" type="xs:base64Binary" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="DecimalDataType" type="tns:DecimalDataType" />

  <xs:complexType name="DataTypeSchemaHeader">
    <xs:sequence>
      <xs:element name="Namespaces" type="ua:ListOfString" minOccurs="0" nillable="true" />
      <xs:element name="StructureDataTypes" type="tns:ListOfStructureDescription" minOccurs="0" nillable="true" />
      <xs:element name="EnumDataTypes" type="tns:ListOfEnumDescription" minOccurs="0" nillable="true" />
      <xs:element name="SimpleDataTypes" type="tns:ListOfSimpleTypeDescription" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="DataTypeSchemaHeader" type="tns:DataTypeSchemaHeader" />

  <xs:complexType name="ListOfDataTypeSchemaHeader">
    <xs:sequence>
      <xs:element name="DataTypeSchemaHeader" type="tns:DataTypeSchemaHeader" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfDataTypeSchemaHeader" type="tns:ListOfDataTypeSchemaHeader" nillable="true"></xs:element>

  <xs:complexType name="DataTypeDescription">
    <xs:sequence>
      <xs:element name="DataTypeId" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="Name" type="ua:QualifiedName" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="DataTypeDescription" type="tns:DataTypeDescription" />

  <xs:complexType name="ListOfDataTypeDescription">
    <xs:sequence>
      <xs:element name="DataTypeDescription" type="tns:DataTypeDescription" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfDataTypeDescription" type="tns:ListOfDataTypeDescription" nillable="true"></xs:element>

  <xs:complexType name="StructureDescription">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:DataTypeDescription">
        <xs:sequence>
          <xs:element name="StructureDefinition" type="tns:StructureDefinition" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="StructureDescription" type="tns:StructureDescription" />

  <xs:complexType name="ListOfStructureDescription">
    <xs:sequence>
      <xs:element name="StructureDescription" type="tns:StructureDescription" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfStructureDescription" type="tns:ListOfStructureDescription" nillable="true"></xs:element>

  <xs:complexType name="EnumDescription">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:DataTypeDescription">
        <xs:sequence>
          <xs:element name="EnumDefinition" type="tns:EnumDefinition" minOccurs="0" nillable="true" />
          <xs:element name="BuiltInType" type="xs:unsignedByte" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EnumDescription" type="tns:EnumDescription" />

  <xs:complexType name="ListOfEnumDescription">
    <xs:sequence>
      <xs:element name="EnumDescription" type="tns:EnumDescription" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfEnumDescription" type="tns:ListOfEnumDescription" nillable="true"></xs:element>

  <xs:complexType name="SimpleTypeDescription">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:DataTypeDescription">
        <xs:sequence>
          <xs:element name="BaseDataType" type="ua:NodeId" minOccurs="0" nillable="true" />
          <xs:element name="BuiltInType" type="xs:unsignedByte" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="SimpleTypeDescription" type="tns:SimpleTypeDescription" />

  <xs:complexType name="ListOfSimpleTypeDescription">
    <xs:sequence>
      <xs:element name="SimpleTypeDescription" type="tns:SimpleTypeDescription" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfSimpleTypeDescription" type="tns:ListOfSimpleTypeDescription" nillable="true"></xs:element>

  <xs:complexType name="UABinaryFileDataType">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:DataTypeSchemaHeader">
        <xs:sequence>
          <xs:element name="SchemaLocation" type="xs:string" minOccurs="0" nillable="true" />
          <xs:element name="FileHeader" type="tns:ListOfKeyValuePair" minOccurs="0" nillable="true" />
          <xs:element name="Body" type="ua:Variant" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="UABinaryFileDataType" type="tns:UABinaryFileDataType" />

  <xs:complexType name="ListOfUABinaryFileDataType">
    <xs:sequence>
      <xs:element name="UABinaryFileDataType" type="tns:UABinaryFileDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfUABinaryFileDataType" type="tns:ListOfUABinaryFileDataType" nillable="true"></xs:element>

  <xs:simpleType  name="PubSubState">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Disabled_0" />
      <xs:enumeration value="Paused_1" />
      <xs:enumeration value="Operational_2" />
      <xs:enumeration value="Error_3" />
      <xs:enumeration value="PreOperational_4" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="PubSubState" type="tns:PubSubState" />

  <xs:complexType name="ListOfPubSubState">
    <xs:sequence>
      <xs:element name="PubSubState" type="tns:PubSubState" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfPubSubState" type="tns:ListOfPubSubState" nillable="true"></xs:element>

  <xs:complexType name="DataSetMetaDataType">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:DataTypeSchemaHeader">
        <xs:sequence>
          <xs:element name="Name" type="xs:string" minOccurs="0" nillable="true" />
          <xs:element name="Description" type="ua:LocalizedText" minOccurs="0" nillable="true" />
          <xs:element name="Fields" type="tns:ListOfFieldMetaData" minOccurs="0" nillable="true" />
          <xs:element name="DataSetClassId" type="ua:Guid" minOccurs="0" />
          <xs:element name="ConfigurationVersion" type="tns:ConfigurationVersionDataType" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="DataSetMetaDataType" type="tns:DataSetMetaDataType" />

  <xs:complexType name="ListOfDataSetMetaDataType">
    <xs:sequence>
      <xs:element name="DataSetMetaDataType" type="tns:DataSetMetaDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfDataSetMetaDataType" type="tns:ListOfDataSetMetaDataType" nillable="true"></xs:element>

  <xs:complexType name="FieldMetaData">
    <xs:sequence>
      <xs:element name="Name" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="Description" type="ua:LocalizedText" minOccurs="0" nillable="true" />
      <xs:element name="FieldFlags" type="tns:DataSetFieldFlags" minOccurs="0" />
      <xs:element name="BuiltInType" type="xs:unsignedByte" minOccurs="0" />
      <xs:element name="DataType" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="ValueRank" type="xs:int" minOccurs="0" />
      <xs:element name="ArrayDimensions" type="ua:ListOfUInt32" minOccurs="0" nillable="true" />
      <xs:element name="MaxStringLength" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="DataSetFieldId" type="ua:Guid" minOccurs="0" />
      <xs:element name="Properties" type="tns:ListOfKeyValuePair" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="FieldMetaData" type="tns:FieldMetaData" />

  <xs:complexType name="ListOfFieldMetaData">
    <xs:sequence>
      <xs:element name="FieldMetaData" type="tns:FieldMetaData" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfFieldMetaData" type="tns:ListOfFieldMetaData" nillable="true"></xs:element>

  <xs:simpleType  name="DataSetFieldFlags">
    <xs:restriction base="xs:unsignedShort">
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="DataSetFieldFlags" type="tns:DataSetFieldFlags" />

  <xs:complexType name="ConfigurationVersionDataType">
    <xs:sequence>
      <xs:element name="MajorVersion" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="MinorVersion" type="xs:unsignedInt" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ConfigurationVersionDataType" type="tns:ConfigurationVersionDataType" />

  <xs:complexType name="ListOfConfigurationVersionDataType">
    <xs:sequence>
      <xs:element name="ConfigurationVersionDataType" type="tns:ConfigurationVersionDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfConfigurationVersionDataType" type="tns:ListOfConfigurationVersionDataType" nillable="true"></xs:element>

  <xs:complexType name="PublishedDataSetDataType">
    <xs:sequence>
      <xs:element name="Name" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="DataSetFolder" type="ua:ListOfString" minOccurs="0" nillable="true" />
      <xs:element name="DataSetMetaData" type="tns:DataSetMetaDataType" minOccurs="0" nillable="true" />
      <xs:element name="ExtensionFields" type="tns:ListOfKeyValuePair" minOccurs="0" nillable="true" />
      <xs:element name="DataSetSource" type="ua:ExtensionObject" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="PublishedDataSetDataType" type="tns:PublishedDataSetDataType" />

  <xs:complexType name="ListOfPublishedDataSetDataType">
    <xs:sequence>
      <xs:element name="PublishedDataSetDataType" type="tns:PublishedDataSetDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfPublishedDataSetDataType" type="tns:ListOfPublishedDataSetDataType" nillable="true"></xs:element>

  <xs:complexType name="PublishedDataSetSourceDataType">
    <xs:sequence>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="PublishedDataSetSourceDataType" type="tns:PublishedDataSetSourceDataType" />

  <xs:complexType name="ListOfPublishedDataSetSourceDataType">
    <xs:sequence>
      <xs:element name="PublishedDataSetSourceDataType" type="tns:PublishedDataSetSourceDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfPublishedDataSetSourceDataType" type="tns:ListOfPublishedDataSetSourceDataType" nillable="true"></xs:element>

  <xs:complexType name="PublishedVariableDataType">
    <xs:sequence>
      <xs:element name="PublishedVariable" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="AttributeId" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="SamplingIntervalHint" type="xs:double" minOccurs="0" />
      <xs:element name="DeadbandType" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="DeadbandValue" type="xs:double" minOccurs="0" />
      <xs:element name="IndexRange" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="SubstituteValue" type="ua:Variant" minOccurs="0" />
      <xs:element name="MetaDataProperties" type="ua:ListOfQualifiedName" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="PublishedVariableDataType" type="tns:PublishedVariableDataType" />

  <xs:complexType name="ListOfPublishedVariableDataType">
    <xs:sequence>
      <xs:element name="PublishedVariableDataType" type="tns:PublishedVariableDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfPublishedVariableDataType" type="tns:ListOfPublishedVariableDataType" nillable="true"></xs:element>

  <xs:complexType name="PublishedDataItemsDataType">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:PublishedDataSetSourceDataType">
        <xs:sequence>
          <xs:element name="PublishedData" type="tns:ListOfPublishedVariableDataType" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="PublishedDataItemsDataType" type="tns:PublishedDataItemsDataType" />

  <xs:complexType name="ListOfPublishedDataItemsDataType">
    <xs:sequence>
      <xs:element name="PublishedDataItemsDataType" type="tns:PublishedDataItemsDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfPublishedDataItemsDataType" type="tns:ListOfPublishedDataItemsDataType" nillable="true"></xs:element>

  <xs:complexType name="PublishedEventsDataType">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:PublishedDataSetSourceDataType">
        <xs:sequence>
          <xs:element name="EventNotifier" type="ua:NodeId" minOccurs="0" nillable="true" />
          <xs:element name="SelectedFields" type="tns:ListOfSimpleAttributeOperand" minOccurs="0" nillable="true" />
          <xs:element name="Filter" type="tns:ContentFilter" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="PublishedEventsDataType" type="tns:PublishedEventsDataType" />

  <xs:complexType name="ListOfPublishedEventsDataType">
    <xs:sequence>
      <xs:element name="PublishedEventsDataType" type="tns:PublishedEventsDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfPublishedEventsDataType" type="tns:ListOfPublishedEventsDataType" nillable="true"></xs:element>

  <xs:simpleType  name="DataSetFieldContentMask">
    <xs:restriction base="xs:unsignedInt">
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="DataSetFieldContentMask" type="tns:DataSetFieldContentMask" />

  <xs:complexType name="ListOfDataSetFieldContentMask">
    <xs:sequence>
      <xs:element name="DataSetFieldContentMask" type="tns:DataSetFieldContentMask" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfDataSetFieldContentMask" type="tns:ListOfDataSetFieldContentMask" nillable="true"></xs:element>

  <xs:complexType name="DataSetWriterDataType">
    <xs:sequence>
      <xs:element name="Name" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="Enabled" type="xs:boolean" minOccurs="0" />
      <xs:element name="DataSetWriterId" type="xs:unsignedShort" minOccurs="0" />
      <xs:element name="DataSetFieldContentMask" type="tns:DataSetFieldContentMask" minOccurs="0" />
      <xs:element name="KeyFrameCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="DataSetName" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="DataSetWriterProperties" type="tns:ListOfKeyValuePair" minOccurs="0" nillable="true" />
      <xs:element name="TransportSettings" type="ua:ExtensionObject" minOccurs="0" nillable="true" />
      <xs:element name="MessageSettings" type="ua:ExtensionObject" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="DataSetWriterDataType" type="tns:DataSetWriterDataType" />

  <xs:complexType name="ListOfDataSetWriterDataType">
    <xs:sequence>
      <xs:element name="DataSetWriterDataType" type="tns:DataSetWriterDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfDataSetWriterDataType" type="tns:ListOfDataSetWriterDataType" nillable="true"></xs:element>

  <xs:complexType name="DataSetWriterTransportDataType">
    <xs:sequence>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="DataSetWriterTransportDataType" type="tns:DataSetWriterTransportDataType" />

  <xs:complexType name="ListOfDataSetWriterTransportDataType">
    <xs:sequence>
      <xs:element name="DataSetWriterTransportDataType" type="tns:DataSetWriterTransportDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfDataSetWriterTransportDataType" type="tns:ListOfDataSetWriterTransportDataType" nillable="true"></xs:element>

  <xs:complexType name="DataSetWriterMessageDataType">
    <xs:sequence>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="DataSetWriterMessageDataType" type="tns:DataSetWriterMessageDataType" />

  <xs:complexType name="ListOfDataSetWriterMessageDataType">
    <xs:sequence>
      <xs:element name="DataSetWriterMessageDataType" type="tns:DataSetWriterMessageDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfDataSetWriterMessageDataType" type="tns:ListOfDataSetWriterMessageDataType" nillable="true"></xs:element>

  <xs:complexType name="PubSubGroupDataType">
    <xs:sequence>
      <xs:element name="Name" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="Enabled" type="xs:boolean" minOccurs="0" />
      <xs:element name="SecurityMode" type="tns:MessageSecurityMode" minOccurs="0" />
      <xs:element name="SecurityGroupId" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="SecurityKeyServices" type="tns:ListOfEndpointDescription" minOccurs="0" nillable="true" />
      <xs:element name="MaxNetworkMessageSize" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="GroupProperties" type="tns:ListOfKeyValuePair" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="PubSubGroupDataType" type="tns:PubSubGroupDataType" />

  <xs:complexType name="ListOfPubSubGroupDataType">
    <xs:sequence>
      <xs:element name="PubSubGroupDataType" type="tns:PubSubGroupDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfPubSubGroupDataType" type="tns:ListOfPubSubGroupDataType" nillable="true"></xs:element>

  <xs:complexType name="WriterGroupDataType">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:PubSubGroupDataType">
        <xs:sequence>
          <xs:element name="WriterGroupId" type="xs:unsignedShort" minOccurs="0" />
          <xs:element name="PublishingInterval" type="xs:double" minOccurs="0" />
          <xs:element name="KeepAliveTime" type="xs:double" minOccurs="0" />
          <xs:element name="Priority" type="xs:unsignedByte" minOccurs="0" />
          <xs:element name="LocaleIds" type="ua:ListOfString" minOccurs="0" nillable="true" />
          <xs:element name="HeaderLayoutUri" type="xs:string" minOccurs="0" nillable="true" />
          <xs:element name="TransportSettings" type="ua:ExtensionObject" minOccurs="0" nillable="true" />
          <xs:element name="MessageSettings" type="ua:ExtensionObject" minOccurs="0" nillable="true" />
          <xs:element name="DataSetWriters" type="tns:ListOfDataSetWriterDataType" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="WriterGroupDataType" type="tns:WriterGroupDataType" />

  <xs:complexType name="ListOfWriterGroupDataType">
    <xs:sequence>
      <xs:element name="WriterGroupDataType" type="tns:WriterGroupDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfWriterGroupDataType" type="tns:ListOfWriterGroupDataType" nillable="true"></xs:element>

  <xs:complexType name="WriterGroupTransportDataType">
    <xs:sequence>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="WriterGroupTransportDataType" type="tns:WriterGroupTransportDataType" />

  <xs:complexType name="ListOfWriterGroupTransportDataType">
    <xs:sequence>
      <xs:element name="WriterGroupTransportDataType" type="tns:WriterGroupTransportDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfWriterGroupTransportDataType" type="tns:ListOfWriterGroupTransportDataType" nillable="true"></xs:element>

  <xs:complexType name="WriterGroupMessageDataType">
    <xs:sequence>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="WriterGroupMessageDataType" type="tns:WriterGroupMessageDataType" />

  <xs:complexType name="ListOfWriterGroupMessageDataType">
    <xs:sequence>
      <xs:element name="WriterGroupMessageDataType" type="tns:WriterGroupMessageDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfWriterGroupMessageDataType" type="tns:ListOfWriterGroupMessageDataType" nillable="true"></xs:element>

  <xs:complexType name="PubSubConnectionDataType">
    <xs:sequence>
      <xs:element name="Name" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="Enabled" type="xs:boolean" minOccurs="0" />
      <xs:element name="PublisherId" type="ua:Variant" minOccurs="0" />
      <xs:element name="TransportProfileUri" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="Address" type="ua:ExtensionObject" minOccurs="0" nillable="true" />
      <xs:element name="ConnectionProperties" type="tns:ListOfKeyValuePair" minOccurs="0" nillable="true" />
      <xs:element name="TransportSettings" type="ua:ExtensionObject" minOccurs="0" nillable="true" />
      <xs:element name="WriterGroups" type="tns:ListOfWriterGroupDataType" minOccurs="0" nillable="true" />
      <xs:element name="ReaderGroups" type="tns:ListOfReaderGroupDataType" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="PubSubConnectionDataType" type="tns:PubSubConnectionDataType" />

  <xs:complexType name="ListOfPubSubConnectionDataType">
    <xs:sequence>
      <xs:element name="PubSubConnectionDataType" type="tns:PubSubConnectionDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfPubSubConnectionDataType" type="tns:ListOfPubSubConnectionDataType" nillable="true"></xs:element>

  <xs:complexType name="ConnectionTransportDataType">
    <xs:sequence>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ConnectionTransportDataType" type="tns:ConnectionTransportDataType" />

  <xs:complexType name="ListOfConnectionTransportDataType">
    <xs:sequence>
      <xs:element name="ConnectionTransportDataType" type="tns:ConnectionTransportDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfConnectionTransportDataType" type="tns:ListOfConnectionTransportDataType" nillable="true"></xs:element>

  <xs:complexType name="NetworkAddressDataType">
    <xs:sequence>
      <xs:element name="NetworkInterface" type="xs:string" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="NetworkAddressDataType" type="tns:NetworkAddressDataType" />

  <xs:complexType name="ListOfNetworkAddressDataType">
    <xs:sequence>
      <xs:element name="NetworkAddressDataType" type="tns:NetworkAddressDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfNetworkAddressDataType" type="tns:ListOfNetworkAddressDataType" nillable="true"></xs:element>

  <xs:complexType name="NetworkAddressUrlDataType">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:NetworkAddressDataType">
        <xs:sequence>
          <xs:element name="Url" type="xs:string" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="NetworkAddressUrlDataType" type="tns:NetworkAddressUrlDataType" />

  <xs:complexType name="ListOfNetworkAddressUrlDataType">
    <xs:sequence>
      <xs:element name="NetworkAddressUrlDataType" type="tns:NetworkAddressUrlDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfNetworkAddressUrlDataType" type="tns:ListOfNetworkAddressUrlDataType" nillable="true"></xs:element>

  <xs:complexType name="ReaderGroupDataType">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:PubSubGroupDataType">
        <xs:sequence>
          <xs:element name="TransportSettings" type="ua:ExtensionObject" minOccurs="0" nillable="true" />
          <xs:element name="MessageSettings" type="ua:ExtensionObject" minOccurs="0" nillable="true" />
          <xs:element name="DataSetReaders" type="tns:ListOfDataSetReaderDataType" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="ReaderGroupDataType" type="tns:ReaderGroupDataType" />

  <xs:complexType name="ListOfReaderGroupDataType">
    <xs:sequence>
      <xs:element name="ReaderGroupDataType" type="tns:ReaderGroupDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfReaderGroupDataType" type="tns:ListOfReaderGroupDataType" nillable="true"></xs:element>

  <xs:complexType name="ReaderGroupTransportDataType">
    <xs:sequence>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ReaderGroupTransportDataType" type="tns:ReaderGroupTransportDataType" />

  <xs:complexType name="ListOfReaderGroupTransportDataType">
    <xs:sequence>
      <xs:element name="ReaderGroupTransportDataType" type="tns:ReaderGroupTransportDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfReaderGroupTransportDataType" type="tns:ListOfReaderGroupTransportDataType" nillable="true"></xs:element>

  <xs:complexType name="ReaderGroupMessageDataType">
    <xs:sequence>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ReaderGroupMessageDataType" type="tns:ReaderGroupMessageDataType" />

  <xs:complexType name="ListOfReaderGroupMessageDataType">
    <xs:sequence>
      <xs:element name="ReaderGroupMessageDataType" type="tns:ReaderGroupMessageDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfReaderGroupMessageDataType" type="tns:ListOfReaderGroupMessageDataType" nillable="true"></xs:element>

  <xs:complexType name="DataSetReaderDataType">
    <xs:sequence>
      <xs:element name="Name" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="Enabled" type="xs:boolean" minOccurs="0" />
      <xs:element name="PublisherId" type="ua:Variant" minOccurs="0" />
      <xs:element name="WriterGroupId" type="xs:unsignedShort" minOccurs="0" />
      <xs:element name="DataSetWriterId" type="xs:unsignedShort" minOccurs="0" />
      <xs:element name="DataSetMetaData" type="tns:DataSetMetaDataType" minOccurs="0" nillable="true" />
      <xs:element name="DataSetFieldContentMask" type="tns:DataSetFieldContentMask" minOccurs="0" />
      <xs:element name="MessageReceiveTimeout" type="xs:double" minOccurs="0" />
      <xs:element name="KeyFrameCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="HeaderLayoutUri" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="SecurityMode" type="tns:MessageSecurityMode" minOccurs="0" />
      <xs:element name="SecurityGroupId" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="SecurityKeyServices" type="tns:ListOfEndpointDescription" minOccurs="0" nillable="true" />
      <xs:element name="DataSetReaderProperties" type="tns:ListOfKeyValuePair" minOccurs="0" nillable="true" />
      <xs:element name="TransportSettings" type="ua:ExtensionObject" minOccurs="0" nillable="true" />
      <xs:element name="MessageSettings" type="ua:ExtensionObject" minOccurs="0" nillable="true" />
      <xs:element name="SubscribedDataSet" type="ua:ExtensionObject" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="DataSetReaderDataType" type="tns:DataSetReaderDataType" />

  <xs:complexType name="ListOfDataSetReaderDataType">
    <xs:sequence>
      <xs:element name="DataSetReaderDataType" type="tns:DataSetReaderDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfDataSetReaderDataType" type="tns:ListOfDataSetReaderDataType" nillable="true"></xs:element>

  <xs:complexType name="DataSetReaderTransportDataType">
    <xs:sequence>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="DataSetReaderTransportDataType" type="tns:DataSetReaderTransportDataType" />

  <xs:complexType name="ListOfDataSetReaderTransportDataType">
    <xs:sequence>
      <xs:element name="DataSetReaderTransportDataType" type="tns:DataSetReaderTransportDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfDataSetReaderTransportDataType" type="tns:ListOfDataSetReaderTransportDataType" nillable="true"></xs:element>

  <xs:complexType name="DataSetReaderMessageDataType">
    <xs:sequence>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="DataSetReaderMessageDataType" type="tns:DataSetReaderMessageDataType" />

  <xs:complexType name="ListOfDataSetReaderMessageDataType">
    <xs:sequence>
      <xs:element name="DataSetReaderMessageDataType" type="tns:DataSetReaderMessageDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfDataSetReaderMessageDataType" type="tns:ListOfDataSetReaderMessageDataType" nillable="true"></xs:element>

  <xs:complexType name="SubscribedDataSetDataType">
    <xs:sequence>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SubscribedDataSetDataType" type="tns:SubscribedDataSetDataType" />

  <xs:complexType name="ListOfSubscribedDataSetDataType">
    <xs:sequence>
      <xs:element name="SubscribedDataSetDataType" type="tns:SubscribedDataSetDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfSubscribedDataSetDataType" type="tns:ListOfSubscribedDataSetDataType" nillable="true"></xs:element>

  <xs:complexType name="TargetVariablesDataType">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:SubscribedDataSetDataType">
        <xs:sequence>
          <xs:element name="TargetVariables" type="tns:ListOfFieldTargetDataType" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="TargetVariablesDataType" type="tns:TargetVariablesDataType" />

  <xs:complexType name="ListOfTargetVariablesDataType">
    <xs:sequence>
      <xs:element name="TargetVariablesDataType" type="tns:TargetVariablesDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfTargetVariablesDataType" type="tns:ListOfTargetVariablesDataType" nillable="true"></xs:element>

  <xs:complexType name="FieldTargetDataType">
    <xs:sequence>
      <xs:element name="DataSetFieldId" type="ua:Guid" minOccurs="0" />
      <xs:element name="ReceiverIndexRange" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="TargetNodeId" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="AttributeId" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="WriteIndexRange" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="OverrideValueHandling" type="tns:OverrideValueHandling" minOccurs="0" />
      <xs:element name="OverrideValue" type="ua:Variant" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="FieldTargetDataType" type="tns:FieldTargetDataType" />

  <xs:complexType name="ListOfFieldTargetDataType">
    <xs:sequence>
      <xs:element name="FieldTargetDataType" type="tns:FieldTargetDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfFieldTargetDataType" type="tns:ListOfFieldTargetDataType" nillable="true"></xs:element>

  <xs:simpleType  name="OverrideValueHandling">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Disabled_0" />
      <xs:enumeration value="LastUsableValue_1" />
      <xs:enumeration value="OverrideValue_2" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="OverrideValueHandling" type="tns:OverrideValueHandling" />

  <xs:complexType name="ListOfOverrideValueHandling">
    <xs:sequence>
      <xs:element name="OverrideValueHandling" type="tns:OverrideValueHandling" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfOverrideValueHandling" type="tns:ListOfOverrideValueHandling" nillable="true"></xs:element>

  <xs:complexType name="SubscribedDataSetMirrorDataType">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:SubscribedDataSetDataType">
        <xs:sequence>
          <xs:element name="ParentNodeName" type="xs:string" minOccurs="0" nillable="true" />
          <xs:element name="RolePermissions" type="tns:ListOfRolePermissionType" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="SubscribedDataSetMirrorDataType" type="tns:SubscribedDataSetMirrorDataType" />

  <xs:complexType name="ListOfSubscribedDataSetMirrorDataType">
    <xs:sequence>
      <xs:element name="SubscribedDataSetMirrorDataType" type="tns:SubscribedDataSetMirrorDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfSubscribedDataSetMirrorDataType" type="tns:ListOfSubscribedDataSetMirrorDataType" nillable="true"></xs:element>

  <xs:complexType name="PubSubConfigurationDataType">
    <xs:sequence>
      <xs:element name="PublishedDataSets" type="tns:ListOfPublishedDataSetDataType" minOccurs="0" nillable="true" />
      <xs:element name="Connections" type="tns:ListOfPubSubConnectionDataType" minOccurs="0" nillable="true" />
      <xs:element name="Enabled" type="xs:boolean" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="PubSubConfigurationDataType" type="tns:PubSubConfigurationDataType" />

  <xs:complexType name="ListOfPubSubConfigurationDataType">
    <xs:sequence>
      <xs:element name="PubSubConfigurationDataType" type="tns:PubSubConfigurationDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfPubSubConfigurationDataType" type="tns:ListOfPubSubConfigurationDataType" nillable="true"></xs:element>

  <xs:simpleType  name="DataSetOrderingType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Undefined_0" />
      <xs:enumeration value="AscendingWriterId_1" />
      <xs:enumeration value="AscendingWriterIdSingle_2" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="DataSetOrderingType" type="tns:DataSetOrderingType" />

  <xs:complexType name="ListOfDataSetOrderingType">
    <xs:sequence>
      <xs:element name="DataSetOrderingType" type="tns:DataSetOrderingType" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfDataSetOrderingType" type="tns:ListOfDataSetOrderingType" nillable="true"></xs:element>

  <xs:simpleType  name="UadpNetworkMessageContentMask">
    <xs:restriction base="xs:unsignedInt">
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="UadpNetworkMessageContentMask" type="tns:UadpNetworkMessageContentMask" />

  <xs:complexType name="ListOfUadpNetworkMessageContentMask">
    <xs:sequence>
      <xs:element name="UadpNetworkMessageContentMask" type="tns:UadpNetworkMessageContentMask" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfUadpNetworkMessageContentMask" type="tns:ListOfUadpNetworkMessageContentMask" nillable="true"></xs:element>

  <xs:complexType name="UadpWriterGroupMessageDataType">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:WriterGroupMessageDataType">
        <xs:sequence>
          <xs:element name="GroupVersion" type="xs:unsignedInt" minOccurs="0" />
          <xs:element name="DataSetOrdering" type="tns:DataSetOrderingType" minOccurs="0" />
          <xs:element name="NetworkMessageContentMask" type="tns:UadpNetworkMessageContentMask" minOccurs="0" />
          <xs:element name="SamplingOffset" type="xs:double" minOccurs="0" />
          <xs:element name="PublishingOffset" type="ua:ListOfDouble" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="UadpWriterGroupMessageDataType" type="tns:UadpWriterGroupMessageDataType" />

  <xs:complexType name="ListOfUadpWriterGroupMessageDataType">
    <xs:sequence>
      <xs:element name="UadpWriterGroupMessageDataType" type="tns:UadpWriterGroupMessageDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfUadpWriterGroupMessageDataType" type="tns:ListOfUadpWriterGroupMessageDataType" nillable="true"></xs:element>

  <xs:simpleType  name="UadpDataSetMessageContentMask">
    <xs:restriction base="xs:unsignedInt">
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="UadpDataSetMessageContentMask" type="tns:UadpDataSetMessageContentMask" />

  <xs:complexType name="ListOfUadpDataSetMessageContentMask">
    <xs:sequence>
      <xs:element name="UadpDataSetMessageContentMask" type="tns:UadpDataSetMessageContentMask" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfUadpDataSetMessageContentMask" type="tns:ListOfUadpDataSetMessageContentMask" nillable="true"></xs:element>

  <xs:complexType name="UadpDataSetWriterMessageDataType">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:DataSetWriterMessageDataType">
        <xs:sequence>
          <xs:element name="DataSetMessageContentMask" type="tns:UadpDataSetMessageContentMask" minOccurs="0" />
          <xs:element name="ConfiguredSize" type="xs:unsignedShort" minOccurs="0" />
          <xs:element name="NetworkMessageNumber" type="xs:unsignedShort" minOccurs="0" />
          <xs:element name="DataSetOffset" type="xs:unsignedShort" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="UadpDataSetWriterMessageDataType" type="tns:UadpDataSetWriterMessageDataType" />

  <xs:complexType name="ListOfUadpDataSetWriterMessageDataType">
    <xs:sequence>
      <xs:element name="UadpDataSetWriterMessageDataType" type="tns:UadpDataSetWriterMessageDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfUadpDataSetWriterMessageDataType" type="tns:ListOfUadpDataSetWriterMessageDataType" nillable="true"></xs:element>

  <xs:complexType name="UadpDataSetReaderMessageDataType">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:DataSetReaderMessageDataType">
        <xs:sequence>
          <xs:element name="GroupVersion" type="xs:unsignedInt" minOccurs="0" />
          <xs:element name="NetworkMessageNumber" type="xs:unsignedShort" minOccurs="0" />
          <xs:element name="DataSetOffset" type="xs:unsignedShort" minOccurs="0" />
          <xs:element name="DataSetClassId" type="ua:Guid" minOccurs="0" />
          <xs:element name="NetworkMessageContentMask" type="tns:UadpNetworkMessageContentMask" minOccurs="0" />
          <xs:element name="DataSetMessageContentMask" type="tns:UadpDataSetMessageContentMask" minOccurs="0" />
          <xs:element name="PublishingInterval" type="xs:double" minOccurs="0" />
          <xs:element name="ReceiveOffset" type="xs:double" minOccurs="0" />
          <xs:element name="ProcessingOffset" type="xs:double" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="UadpDataSetReaderMessageDataType" type="tns:UadpDataSetReaderMessageDataType" />

  <xs:complexType name="ListOfUadpDataSetReaderMessageDataType">
    <xs:sequence>
      <xs:element name="UadpDataSetReaderMessageDataType" type="tns:UadpDataSetReaderMessageDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfUadpDataSetReaderMessageDataType" type="tns:ListOfUadpDataSetReaderMessageDataType" nillable="true"></xs:element>

  <xs:simpleType  name="JsonNetworkMessageContentMask">
    <xs:restriction base="xs:unsignedInt">
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="JsonNetworkMessageContentMask" type="tns:JsonNetworkMessageContentMask" />

  <xs:complexType name="ListOfJsonNetworkMessageContentMask">
    <xs:sequence>
      <xs:element name="JsonNetworkMessageContentMask" type="tns:JsonNetworkMessageContentMask" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfJsonNetworkMessageContentMask" type="tns:ListOfJsonNetworkMessageContentMask" nillable="true"></xs:element>

  <xs:complexType name="JsonWriterGroupMessageDataType">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:WriterGroupMessageDataType">
        <xs:sequence>
          <xs:element name="NetworkMessageContentMask" type="tns:JsonNetworkMessageContentMask" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="JsonWriterGroupMessageDataType" type="tns:JsonWriterGroupMessageDataType" />

  <xs:complexType name="ListOfJsonWriterGroupMessageDataType">
    <xs:sequence>
      <xs:element name="JsonWriterGroupMessageDataType" type="tns:JsonWriterGroupMessageDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfJsonWriterGroupMessageDataType" type="tns:ListOfJsonWriterGroupMessageDataType" nillable="true"></xs:element>

  <xs:simpleType  name="JsonDataSetMessageContentMask">
    <xs:restriction base="xs:unsignedInt">
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="JsonDataSetMessageContentMask" type="tns:JsonDataSetMessageContentMask" />

  <xs:complexType name="ListOfJsonDataSetMessageContentMask">
    <xs:sequence>
      <xs:element name="JsonDataSetMessageContentMask" type="tns:JsonDataSetMessageContentMask" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfJsonDataSetMessageContentMask" type="tns:ListOfJsonDataSetMessageContentMask" nillable="true"></xs:element>

  <xs:complexType name="JsonDataSetWriterMessageDataType">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:DataSetWriterMessageDataType">
        <xs:sequence>
          <xs:element name="DataSetMessageContentMask" type="tns:JsonDataSetMessageContentMask" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="JsonDataSetWriterMessageDataType" type="tns:JsonDataSetWriterMessageDataType" />

  <xs:complexType name="ListOfJsonDataSetWriterMessageDataType">
    <xs:sequence>
      <xs:element name="JsonDataSetWriterMessageDataType" type="tns:JsonDataSetWriterMessageDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfJsonDataSetWriterMessageDataType" type="tns:ListOfJsonDataSetWriterMessageDataType" nillable="true"></xs:element>

  <xs:complexType name="JsonDataSetReaderMessageDataType">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:DataSetReaderMessageDataType">
        <xs:sequence>
          <xs:element name="NetworkMessageContentMask" type="tns:JsonNetworkMessageContentMask" minOccurs="0" />
          <xs:element name="DataSetMessageContentMask" type="tns:JsonDataSetMessageContentMask" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="JsonDataSetReaderMessageDataType" type="tns:JsonDataSetReaderMessageDataType" />

  <xs:complexType name="ListOfJsonDataSetReaderMessageDataType">
    <xs:sequence>
      <xs:element name="JsonDataSetReaderMessageDataType" type="tns:JsonDataSetReaderMessageDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfJsonDataSetReaderMessageDataType" type="tns:ListOfJsonDataSetReaderMessageDataType" nillable="true"></xs:element>

  <xs:complexType name="DatagramConnectionTransportDataType">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:ConnectionTransportDataType">
        <xs:sequence>
          <xs:element name="DiscoveryAddress" type="ua:ExtensionObject" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="DatagramConnectionTransportDataType" type="tns:DatagramConnectionTransportDataType" />

  <xs:complexType name="ListOfDatagramConnectionTransportDataType">
    <xs:sequence>
      <xs:element name="DatagramConnectionTransportDataType" type="tns:DatagramConnectionTransportDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfDatagramConnectionTransportDataType" type="tns:ListOfDatagramConnectionTransportDataType" nillable="true"></xs:element>

  <xs:complexType name="DatagramWriterGroupTransportDataType">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:WriterGroupTransportDataType">
        <xs:sequence>
          <xs:element name="MessageRepeatCount" type="xs:unsignedByte" minOccurs="0" />
          <xs:element name="MessageRepeatDelay" type="xs:double" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="DatagramWriterGroupTransportDataType" type="tns:DatagramWriterGroupTransportDataType" />

  <xs:complexType name="ListOfDatagramWriterGroupTransportDataType">
    <xs:sequence>
      <xs:element name="DatagramWriterGroupTransportDataType" type="tns:DatagramWriterGroupTransportDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfDatagramWriterGroupTransportDataType" type="tns:ListOfDatagramWriterGroupTransportDataType" nillable="true"></xs:element>

  <xs:complexType name="BrokerConnectionTransportDataType">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:ConnectionTransportDataType">
        <xs:sequence>
          <xs:element name="ResourceUri" type="xs:string" minOccurs="0" nillable="true" />
          <xs:element name="AuthenticationProfileUri" type="xs:string" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="BrokerConnectionTransportDataType" type="tns:BrokerConnectionTransportDataType" />

  <xs:complexType name="ListOfBrokerConnectionTransportDataType">
    <xs:sequence>
      <xs:element name="BrokerConnectionTransportDataType" type="tns:BrokerConnectionTransportDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfBrokerConnectionTransportDataType" type="tns:ListOfBrokerConnectionTransportDataType" nillable="true"></xs:element>

  <xs:simpleType  name="BrokerTransportQualityOfService">
    <xs:restriction base="xs:string">
      <xs:enumeration value="NotSpecified_0" />
      <xs:enumeration value="BestEffort_1" />
      <xs:enumeration value="AtLeastOnce_2" />
      <xs:enumeration value="AtMostOnce_3" />
      <xs:enumeration value="ExactlyOnce_4" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="BrokerTransportQualityOfService" type="tns:BrokerTransportQualityOfService" />

  <xs:complexType name="ListOfBrokerTransportQualityOfService">
    <xs:sequence>
      <xs:element name="BrokerTransportQualityOfService" type="tns:BrokerTransportQualityOfService" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfBrokerTransportQualityOfService" type="tns:ListOfBrokerTransportQualityOfService" nillable="true"></xs:element>

  <xs:complexType name="BrokerWriterGroupTransportDataType">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:WriterGroupTransportDataType">
        <xs:sequence>
          <xs:element name="QueueName" type="xs:string" minOccurs="0" nillable="true" />
          <xs:element name="ResourceUri" type="xs:string" minOccurs="0" nillable="true" />
          <xs:element name="AuthenticationProfileUri" type="xs:string" minOccurs="0" nillable="true" />
          <xs:element name="RequestedDeliveryGuarantee" type="tns:BrokerTransportQualityOfService" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="BrokerWriterGroupTransportDataType" type="tns:BrokerWriterGroupTransportDataType" />

  <xs:complexType name="ListOfBrokerWriterGroupTransportDataType">
    <xs:sequence>
      <xs:element name="BrokerWriterGroupTransportDataType" type="tns:BrokerWriterGroupTransportDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfBrokerWriterGroupTransportDataType" type="tns:ListOfBrokerWriterGroupTransportDataType" nillable="true"></xs:element>

  <xs:complexType name="BrokerDataSetWriterTransportDataType">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:DataSetWriterTransportDataType">
        <xs:sequence>
          <xs:element name="QueueName" type="xs:string" minOccurs="0" nillable="true" />
          <xs:element name="ResourceUri" type="xs:string" minOccurs="0" nillable="true" />
          <xs:element name="AuthenticationProfileUri" type="xs:string" minOccurs="0" nillable="true" />
          <xs:element name="RequestedDeliveryGuarantee" type="tns:BrokerTransportQualityOfService" minOccurs="0" />
          <xs:element name="MetaDataQueueName" type="xs:string" minOccurs="0" nillable="true" />
          <xs:element name="MetaDataUpdateTime" type="xs:double" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="BrokerDataSetWriterTransportDataType" type="tns:BrokerDataSetWriterTransportDataType" />

  <xs:complexType name="ListOfBrokerDataSetWriterTransportDataType">
    <xs:sequence>
      <xs:element name="BrokerDataSetWriterTransportDataType" type="tns:BrokerDataSetWriterTransportDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfBrokerDataSetWriterTransportDataType" type="tns:ListOfBrokerDataSetWriterTransportDataType" nillable="true"></xs:element>

  <xs:complexType name="BrokerDataSetReaderTransportDataType">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:DataSetReaderTransportDataType">
        <xs:sequence>
          <xs:element name="QueueName" type="xs:string" minOccurs="0" nillable="true" />
          <xs:element name="ResourceUri" type="xs:string" minOccurs="0" nillable="true" />
          <xs:element name="AuthenticationProfileUri" type="xs:string" minOccurs="0" nillable="true" />
          <xs:element name="RequestedDeliveryGuarantee" type="tns:BrokerTransportQualityOfService" minOccurs="0" />
          <xs:element name="MetaDataQueueName" type="xs:string" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="BrokerDataSetReaderTransportDataType" type="tns:BrokerDataSetReaderTransportDataType" />

  <xs:complexType name="ListOfBrokerDataSetReaderTransportDataType">
    <xs:sequence>
      <xs:element name="BrokerDataSetReaderTransportDataType" type="tns:BrokerDataSetReaderTransportDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfBrokerDataSetReaderTransportDataType" type="tns:ListOfBrokerDataSetReaderTransportDataType" nillable="true"></xs:element>

  <xs:simpleType  name="DiagnosticsLevel">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Basic_0" />
      <xs:enumeration value="Advanced_1" />
      <xs:enumeration value="Info_2" />
      <xs:enumeration value="Log_3" />
      <xs:enumeration value="Debug_4" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="DiagnosticsLevel" type="tns:DiagnosticsLevel" />

  <xs:complexType name="ListOfDiagnosticsLevel">
    <xs:sequence>
      <xs:element name="DiagnosticsLevel" type="tns:DiagnosticsLevel" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfDiagnosticsLevel" type="tns:ListOfDiagnosticsLevel" nillable="true"></xs:element>

  <xs:simpleType  name="PubSubDiagnosticsCounterClassification">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Information_0" />
      <xs:enumeration value="Error_1" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="PubSubDiagnosticsCounterClassification" type="tns:PubSubDiagnosticsCounterClassification" />

  <xs:complexType name="ListOfPubSubDiagnosticsCounterClassification">
    <xs:sequence>
      <xs:element name="PubSubDiagnosticsCounterClassification" type="tns:PubSubDiagnosticsCounterClassification" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfPubSubDiagnosticsCounterClassification" type="tns:ListOfPubSubDiagnosticsCounterClassification" nillable="true"></xs:element>

  <xs:complexType name="AliasNameDataType">
    <xs:sequence>
      <xs:element name="AliasName" type="ua:QualifiedName" minOccurs="0" nillable="true" />
      <xs:element name="ReferencedNodes" type="ua:ListOfExpandedNodeId" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="AliasNameDataType" type="tns:AliasNameDataType" />

  <xs:complexType name="ListOfAliasNameDataType">
    <xs:sequence>
      <xs:element name="AliasNameDataType" type="tns:AliasNameDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfAliasNameDataType" type="tns:ListOfAliasNameDataType" nillable="true"></xs:element>

  <xs:simpleType  name="Duplex">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Full_0" />
      <xs:enumeration value="Half_1" />
      <xs:enumeration value="Unknown_2" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="Duplex" type="tns:Duplex" />

  <xs:complexType name="ListOfDuplex">
    <xs:sequence>
      <xs:element name="Duplex" type="tns:Duplex" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfDuplex" type="tns:ListOfDuplex" nillable="true"></xs:element>

  <xs:simpleType  name="InterfaceAdminStatus">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Up_0" />
      <xs:enumeration value="Down_1" />
      <xs:enumeration value="Testing_2" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="InterfaceAdminStatus" type="tns:InterfaceAdminStatus" />

  <xs:complexType name="ListOfInterfaceAdminStatus">
    <xs:sequence>
      <xs:element name="InterfaceAdminStatus" type="tns:InterfaceAdminStatus" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfInterfaceAdminStatus" type="tns:ListOfInterfaceAdminStatus" nillable="true"></xs:element>

  <xs:simpleType  name="InterfaceOperStatus">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Up_0" />
      <xs:enumeration value="Down_1" />
      <xs:enumeration value="Testing_2" />
      <xs:enumeration value="Unknown_3" />
      <xs:enumeration value="Dormant_4" />
      <xs:enumeration value="NotPresent_5" />
      <xs:enumeration value="LowerLayerDown_6" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="InterfaceOperStatus" type="tns:InterfaceOperStatus" />

  <xs:complexType name="ListOfInterfaceOperStatus">
    <xs:sequence>
      <xs:element name="InterfaceOperStatus" type="tns:InterfaceOperStatus" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfInterfaceOperStatus" type="tns:ListOfInterfaceOperStatus" nillable="true"></xs:element>

  <xs:simpleType  name="NegotiationStatus">
    <xs:restriction base="xs:string">
      <xs:enumeration value="InProgress_0" />
      <xs:enumeration value="Complete_1" />
      <xs:enumeration value="Failed_2" />
      <xs:enumeration value="Unknown_3" />
      <xs:enumeration value="NoNegotiation_4" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="NegotiationStatus" type="tns:NegotiationStatus" />

  <xs:complexType name="ListOfNegotiationStatus">
    <xs:sequence>
      <xs:element name="NegotiationStatus" type="tns:NegotiationStatus" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfNegotiationStatus" type="tns:ListOfNegotiationStatus" nillable="true"></xs:element>

  <xs:simpleType  name="TsnFailureCode">
    <xs:restriction base="xs:string">
      <xs:enumeration value="NoFailure_0" />
      <xs:enumeration value="InsufficientBandwidth_1" />
      <xs:enumeration value="InsufficientResources_2" />
      <xs:enumeration value="InsufficientTrafficClassBandwidth_3" />
      <xs:enumeration value="StreamIdInUse_4" />
      <xs:enumeration value="StreamDestinationAddressInUse_5" />
      <xs:enumeration value="StreamPreemptedByHigherRank_6" />
      <xs:enumeration value="LatencyHasChanged_7" />
      <xs:enumeration value="EgressPortNotAvbCapable_8" />
      <xs:enumeration value="UseDifferentDestinationAddress_9" />
      <xs:enumeration value="OutOfMsrpResources_10" />
      <xs:enumeration value="OutOfMmrpResources_11" />
      <xs:enumeration value="CannotStoreDestinationAddress_12" />
      <xs:enumeration value="PriorityIsNotAnSrcClass_13" />
      <xs:enumeration value="MaxFrameSizeTooLarge_14" />
      <xs:enumeration value="MaxFanInPortsLimitReached_15" />
      <xs:enumeration value="FirstValueChangedForStreamId_16" />
      <xs:enumeration value="VlanBlockedOnEgress_17" />
      <xs:enumeration value="VlanTaggingDisabledOnEgress_18" />
      <xs:enumeration value="SrClassPriorityMismatch_19" />
      <xs:enumeration value="FeatureNotPropagated_20" />
      <xs:enumeration value="MaxLatencyExceeded_21" />
      <xs:enumeration value="BridgeDoesNotProvideNetworkId_22" />
      <xs:enumeration value="StreamTransformNotSupported_23" />
      <xs:enumeration value="StreamIdTypeNotSupported_24" />
      <xs:enumeration value="FeatureNotSupported_25" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="TsnFailureCode" type="tns:TsnFailureCode" />

  <xs:complexType name="ListOfTsnFailureCode">
    <xs:sequence>
      <xs:element name="TsnFailureCode" type="tns:TsnFailureCode" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfTsnFailureCode" type="tns:ListOfTsnFailureCode" nillable="true"></xs:element>

  <xs:simpleType  name="TsnStreamState">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Disabled_0" />
      <xs:enumeration value="Configuring_1" />
      <xs:enumeration value="Ready_2" />
      <xs:enumeration value="Operational_3" />
      <xs:enumeration value="Error_4" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="TsnStreamState" type="tns:TsnStreamState" />

  <xs:complexType name="ListOfTsnStreamState">
    <xs:sequence>
      <xs:element name="TsnStreamState" type="tns:TsnStreamState" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfTsnStreamState" type="tns:ListOfTsnStreamState" nillable="true"></xs:element>

  <xs:simpleType  name="TsnTalkerStatus">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None_0" />
      <xs:enumeration value="Ready_1" />
      <xs:enumeration value="Failed_2" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="TsnTalkerStatus" type="tns:TsnTalkerStatus" />

  <xs:complexType name="ListOfTsnTalkerStatus">
    <xs:sequence>
      <xs:element name="TsnTalkerStatus" type="tns:TsnTalkerStatus" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfTsnTalkerStatus" type="tns:ListOfTsnTalkerStatus" nillable="true"></xs:element>

  <xs:simpleType  name="TsnListenerStatus">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None_0" />
      <xs:enumeration value="Ready_1" />
      <xs:enumeration value="PartialFailed_2" />
      <xs:enumeration value="Failed_3" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="TsnListenerStatus" type="tns:TsnListenerStatus" />

  <xs:complexType name="ListOfTsnListenerStatus">
    <xs:sequence>
      <xs:element name="TsnListenerStatus" type="tns:TsnListenerStatus" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfTsnListenerStatus" type="tns:ListOfTsnListenerStatus" nillable="true"></xs:element>

  <xs:complexType name="UnsignedRationalNumber">
    <xs:sequence>
      <xs:element name="Numerator" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="Denominator" type="xs:unsignedInt" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="UnsignedRationalNumber" type="tns:UnsignedRationalNumber" />

  <xs:complexType name="ListOfUnsignedRationalNumber">
    <xs:sequence>
      <xs:element name="UnsignedRationalNumber" type="tns:UnsignedRationalNumber" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfUnsignedRationalNumber" type="tns:ListOfUnsignedRationalNumber" nillable="true"></xs:element>

  <xs:simpleType  name="IdType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Numeric_0" />
      <xs:enumeration value="String_1" />
      <xs:enumeration value="Guid_2" />
      <xs:enumeration value="Opaque_3" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="IdType" type="tns:IdType" />

  <xs:complexType name="ListOfIdType">
    <xs:sequence>
      <xs:element name="IdType" type="tns:IdType" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfIdType" type="tns:ListOfIdType" nillable="true"></xs:element>

  <xs:simpleType  name="NodeClass">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Unspecified_0" />
      <xs:enumeration value="Object_1" />
      <xs:enumeration value="Variable_2" />
      <xs:enumeration value="Method_4" />
      <xs:enumeration value="ObjectType_8" />
      <xs:enumeration value="VariableType_16" />
      <xs:enumeration value="ReferenceType_32" />
      <xs:enumeration value="DataType_64" />
      <xs:enumeration value="View_128" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="NodeClass" type="tns:NodeClass" />

  <xs:simpleType  name="PermissionType">
    <xs:restriction base="xs:unsignedInt">
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="PermissionType" type="tns:PermissionType" />

  <xs:simpleType  name="AccessLevelType">
    <xs:restriction base="xs:unsignedByte">
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="AccessLevelType" type="tns:AccessLevelType" />

  <xs:simpleType  name="AccessLevelExType">
    <xs:restriction base="xs:unsignedInt">
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="AccessLevelExType" type="tns:AccessLevelExType" />

  <xs:simpleType  name="EventNotifierType">
    <xs:restriction base="xs:unsignedByte">
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="EventNotifierType" type="tns:EventNotifierType" />

  <xs:complexType name="RolePermissionType">
    <xs:sequence>
      <xs:element name="RoleId" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="Permissions" type="tns:PermissionType" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RolePermissionType" type="tns:RolePermissionType" />

  <xs:complexType name="ListOfRolePermissionType">
    <xs:sequence>
      <xs:element name="RolePermissionType" type="tns:RolePermissionType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfRolePermissionType" type="tns:ListOfRolePermissionType" nillable="true"></xs:element>

  <xs:complexType name="DataTypeDefinition">
    <xs:sequence>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="DataTypeDefinition" type="tns:DataTypeDefinition" />

  <xs:complexType name="ListOfDataTypeDefinition">
    <xs:sequence>
      <xs:element name="DataTypeDefinition" type="tns:DataTypeDefinition" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfDataTypeDefinition" type="tns:ListOfDataTypeDefinition" nillable="true"></xs:element>

  <xs:simpleType  name="StructureType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Structure_0" />
      <xs:enumeration value="StructureWithOptionalFields_1" />
      <xs:enumeration value="Union_2" />
      <xs:enumeration value="StructureWithSubtypedValues_3" />
      <xs:enumeration value="UnionWithSubtypedValues_4" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="StructureType" type="tns:StructureType" />

  <xs:complexType name="StructureField">
    <xs:sequence>
      <xs:element name="Name" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="Description" type="ua:LocalizedText" minOccurs="0" nillable="true" />
      <xs:element name="DataType" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="ValueRank" type="xs:int" minOccurs="0" />
      <xs:element name="ArrayDimensions" type="ua:ListOfUInt32" minOccurs="0" nillable="true" />
      <xs:element name="MaxStringLength" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="IsOptional" type="xs:boolean" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="StructureField" type="tns:StructureField" />

  <xs:complexType name="ListOfStructureField">
    <xs:sequence>
      <xs:element name="StructureField" type="tns:StructureField" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfStructureField" type="tns:ListOfStructureField" nillable="true"></xs:element>

  <xs:complexType name="StructureDefinition">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:DataTypeDefinition">
        <xs:sequence>
          <xs:element name="DefaultEncodingId" type="ua:NodeId" minOccurs="0" nillable="true" />
          <xs:element name="BaseDataType" type="ua:NodeId" minOccurs="0" nillable="true" />
          <xs:element name="StructureType" type="tns:StructureType" minOccurs="0" />
          <xs:element name="Fields" type="tns:ListOfStructureField" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="StructureDefinition" type="tns:StructureDefinition" />

  <xs:complexType name="ListOfStructureDefinition">
    <xs:sequence>
      <xs:element name="StructureDefinition" type="tns:StructureDefinition" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfStructureDefinition" type="tns:ListOfStructureDefinition" nillable="true"></xs:element>

  <xs:complexType name="EnumDefinition">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:DataTypeDefinition">
        <xs:sequence>
          <xs:element name="Fields" type="tns:ListOfEnumField" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EnumDefinition" type="tns:EnumDefinition" />

  <xs:complexType name="ListOfEnumDefinition">
    <xs:sequence>
      <xs:element name="EnumDefinition" type="tns:EnumDefinition" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfEnumDefinition" type="tns:ListOfEnumDefinition" nillable="true"></xs:element>

  <xs:complexType name="Node">
    <xs:sequence>
      <xs:element name="NodeId" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="NodeClass" type="tns:NodeClass" minOccurs="0" />
      <xs:element name="BrowseName" type="ua:QualifiedName" minOccurs="0" nillable="true" />
      <xs:element name="DisplayName" type="ua:LocalizedText" minOccurs="0" nillable="true" />
      <xs:element name="Description" type="ua:LocalizedText" minOccurs="0" nillable="true" />
      <xs:element name="WriteMask" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="UserWriteMask" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="RolePermissions" type="tns:ListOfRolePermissionType" minOccurs="0" nillable="true" />
      <xs:element name="UserRolePermissions" type="tns:ListOfRolePermissionType" minOccurs="0" nillable="true" />
      <xs:element name="AccessRestrictions" type="xs:unsignedShort" minOccurs="0" />
      <xs:element name="References" type="tns:ListOfReferenceNode" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Node" type="tns:Node" />

  <xs:complexType name="ListOfNode">
    <xs:sequence>
      <xs:element name="Node" type="tns:Node" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfNode" type="tns:ListOfNode" nillable="true"></xs:element>

  <xs:complexType name="InstanceNode">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:Node">
        <xs:sequence>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="InstanceNode" type="tns:InstanceNode" />

  <xs:complexType name="TypeNode">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:Node">
        <xs:sequence>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="TypeNode" type="tns:TypeNode" />

  <xs:complexType name="ObjectNode">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:InstanceNode">
        <xs:sequence>
          <xs:element name="EventNotifier" type="xs:unsignedByte" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="ObjectNode" type="tns:ObjectNode" />

  <xs:complexType name="ObjectTypeNode">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:TypeNode">
        <xs:sequence>
          <xs:element name="IsAbstract" type="xs:boolean" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="ObjectTypeNode" type="tns:ObjectTypeNode" />

  <xs:complexType name="VariableNode">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:InstanceNode">
        <xs:sequence>
          <xs:element name="Value" type="ua:Variant" minOccurs="0" />
          <xs:element name="DataType" type="ua:NodeId" minOccurs="0" nillable="true" />
          <xs:element name="ValueRank" type="xs:int" minOccurs="0" />
          <xs:element name="ArrayDimensions" type="ua:ListOfUInt32" minOccurs="0" nillable="true" />
          <xs:element name="AccessLevel" type="xs:unsignedByte" minOccurs="0" />
          <xs:element name="UserAccessLevel" type="xs:unsignedByte" minOccurs="0" />
          <xs:element name="MinimumSamplingInterval" type="xs:double" minOccurs="0" />
          <xs:element name="Historizing" type="xs:boolean" minOccurs="0" />
          <xs:element name="AccessLevelEx" type="xs:unsignedInt" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="VariableNode" type="tns:VariableNode" />

  <xs:complexType name="VariableTypeNode">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:TypeNode">
        <xs:sequence>
          <xs:element name="Value" type="ua:Variant" minOccurs="0" />
          <xs:element name="DataType" type="ua:NodeId" minOccurs="0" nillable="true" />
          <xs:element name="ValueRank" type="xs:int" minOccurs="0" />
          <xs:element name="ArrayDimensions" type="ua:ListOfUInt32" minOccurs="0" nillable="true" />
          <xs:element name="IsAbstract" type="xs:boolean" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="VariableTypeNode" type="tns:VariableTypeNode" />

  <xs:complexType name="ReferenceTypeNode">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:TypeNode">
        <xs:sequence>
          <xs:element name="IsAbstract" type="xs:boolean" minOccurs="0" />
          <xs:element name="Symmetric" type="xs:boolean" minOccurs="0" />
          <xs:element name="InverseName" type="ua:LocalizedText" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="ReferenceTypeNode" type="tns:ReferenceTypeNode" />

  <xs:complexType name="MethodNode">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:InstanceNode">
        <xs:sequence>
          <xs:element name="Executable" type="xs:boolean" minOccurs="0" />
          <xs:element name="UserExecutable" type="xs:boolean" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="MethodNode" type="tns:MethodNode" />

  <xs:complexType name="ViewNode">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:InstanceNode">
        <xs:sequence>
          <xs:element name="ContainsNoLoops" type="xs:boolean" minOccurs="0" />
          <xs:element name="EventNotifier" type="xs:unsignedByte" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="ViewNode" type="tns:ViewNode" />

  <xs:complexType name="DataTypeNode">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:TypeNode">
        <xs:sequence>
          <xs:element name="IsAbstract" type="xs:boolean" minOccurs="0" />
          <xs:element name="DataTypeDefinition" type="ua:ExtensionObject" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="DataTypeNode" type="tns:DataTypeNode" />

  <xs:complexType name="ReferenceNode">
    <xs:sequence>
      <xs:element name="ReferenceTypeId" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="IsInverse" type="xs:boolean" minOccurs="0" />
      <xs:element name="TargetId" type="ua:ExpandedNodeId" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ReferenceNode" type="tns:ReferenceNode" />

  <xs:complexType name="ListOfReferenceNode">
    <xs:sequence>
      <xs:element name="ReferenceNode" type="tns:ReferenceNode" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfReferenceNode" type="tns:ListOfReferenceNode" nillable="true"></xs:element>

  <xs:complexType name="Argument">
    <xs:sequence>
      <xs:element name="Name" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="DataType" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="ValueRank" type="xs:int" minOccurs="0" />
      <xs:element name="ArrayDimensions" type="ua:ListOfUInt32" minOccurs="0" nillable="true" />
      <xs:element name="Description" type="ua:LocalizedText" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Argument" type="tns:Argument" />

  <xs:complexType name="ListOfArgument">
    <xs:sequence>
      <xs:element name="Argument" type="tns:Argument" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfArgument" type="tns:ListOfArgument" nillable="true"></xs:element>

  <xs:complexType name="EnumValueType">
    <xs:sequence>
      <xs:element name="Value" type="xs:long" minOccurs="0" />
      <xs:element name="DisplayName" type="ua:LocalizedText" minOccurs="0" nillable="true" />
      <xs:element name="Description" type="ua:LocalizedText" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="EnumValueType" type="tns:EnumValueType" />

  <xs:complexType name="ListOfEnumValueType">
    <xs:sequence>
      <xs:element name="EnumValueType" type="tns:EnumValueType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfEnumValueType" type="tns:ListOfEnumValueType" nillable="true"></xs:element>

  <xs:complexType name="EnumField">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:EnumValueType">
        <xs:sequence>
          <xs:element name="Name" type="xs:string" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EnumField" type="tns:EnumField" />

  <xs:complexType name="ListOfEnumField">
    <xs:sequence>
      <xs:element name="EnumField" type="tns:EnumField" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfEnumField" type="tns:ListOfEnumField" nillable="true"></xs:element>

  <xs:complexType name="OptionSet">
    <xs:sequence>
      <xs:element name="Value" type="xs:base64Binary" minOccurs="0" nillable="true" />
      <xs:element name="ValidBits" type="xs:base64Binary" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="OptionSet" type="tns:OptionSet" />

  <xs:complexType name="ListOfOptionSet">
    <xs:sequence>
      <xs:element name="OptionSet" type="tns:OptionSet" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfOptionSet" type="tns:ListOfOptionSet" nillable="true"></xs:element>

  <xs:element name="NormalizedString" type="xs:string" />

  <xs:element name="DecimalString" type="xs:string" />

  <xs:element name="DurationString" type="xs:string" />

  <xs:element name="TimeString" type="xs:string" />

  <xs:element name="DateString" type="xs:string" />

  <xs:element name="Duration" type="xs:double" />

  <xs:element name="UtcTime" type="xs:dateTime" />

  <xs:element name="Time" type="xs:string" />

  <xs:element name="Date" type="xs:dateTime" />

  <xs:element name="LocaleId" type="xs:string" />

  <xs:complexType name="TimeZoneDataType">
    <xs:sequence>
      <xs:element name="Offset" type="xs:short" minOccurs="0" />
      <xs:element name="DaylightSavingInOffset" type="xs:boolean" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="TimeZoneDataType" type="tns:TimeZoneDataType" />

  <xs:complexType name="ListOfTimeZoneDataType">
    <xs:sequence>
      <xs:element name="TimeZoneDataType" type="tns:TimeZoneDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfTimeZoneDataType" type="tns:ListOfTimeZoneDataType" nillable="true"></xs:element>

  <xs:element name="Index" type="xs:unsignedInt" />

  <xs:element name="IntegerId" type="xs:unsignedInt" />

  <xs:simpleType  name="ApplicationType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Server_0" />
      <xs:enumeration value="Client_1" />
      <xs:enumeration value="ClientAndServer_2" />
      <xs:enumeration value="DiscoveryServer_3" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="ApplicationType" type="tns:ApplicationType" />

  <xs:complexType name="ApplicationDescription">
    <xs:sequence>
      <xs:element name="ApplicationUri" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="ProductUri" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="ApplicationName" type="ua:LocalizedText" minOccurs="0" nillable="true" />
      <xs:element name="ApplicationType" type="tns:ApplicationType" minOccurs="0" />
      <xs:element name="GatewayServerUri" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="DiscoveryProfileUri" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="DiscoveryUrls" type="ua:ListOfString" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ApplicationDescription" type="tns:ApplicationDescription" />

  <xs:complexType name="ListOfApplicationDescription">
    <xs:sequence>
      <xs:element name="ApplicationDescription" type="tns:ApplicationDescription" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfApplicationDescription" type="tns:ListOfApplicationDescription" nillable="true"></xs:element>

  <xs:complexType name="RequestHeader">
    <xs:sequence>
      <xs:element name="AuthenticationToken" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="Timestamp" type="xs:dateTime" minOccurs="0" />
      <xs:element name="RequestHandle" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="ReturnDiagnostics" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="AuditEntryId" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="TimeoutHint" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="AdditionalHeader" type="ua:ExtensionObject" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RequestHeader" type="tns:RequestHeader" />

  <xs:complexType name="ResponseHeader">
    <xs:sequence>
      <xs:element name="Timestamp" type="xs:dateTime" minOccurs="0" />
      <xs:element name="RequestHandle" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="ServiceResult" type="ua:StatusCode" minOccurs="0" />
      <xs:element name="ServiceDiagnostics" type="ua:DiagnosticInfo" minOccurs="0" nillable="true" />
      <xs:element name="StringTable" type="ua:ListOfString" minOccurs="0" nillable="true" />
      <xs:element name="AdditionalHeader" type="ua:ExtensionObject" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ResponseHeader" type="tns:ResponseHeader" />

  <xs:element name="VersionTime" type="xs:unsignedInt" />

  <xs:complexType name="ServiceFault">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ServiceFault" type="tns:ServiceFault" />

  <xs:complexType name="SessionlessInvokeRequestType">
    <xs:sequence>
      <xs:element name="UrisVersion" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="NamespaceUris" type="ua:ListOfString" minOccurs="0" nillable="true" />
      <xs:element name="ServerUris" type="ua:ListOfString" minOccurs="0" nillable="true" />
      <xs:element name="LocaleIds" type="ua:ListOfString" minOccurs="0" nillable="true" />
      <xs:element name="ServiceId" type="xs:unsignedInt" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SessionlessInvokeRequestType" type="tns:SessionlessInvokeRequestType" />

  <xs:complexType name="SessionlessInvokeResponseType">
    <xs:sequence>
      <xs:element name="NamespaceUris" type="ua:ListOfString" minOccurs="0" nillable="true" />
      <xs:element name="ServerUris" type="ua:ListOfString" minOccurs="0" nillable="true" />
      <xs:element name="ServiceId" type="xs:unsignedInt" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SessionlessInvokeResponseType" type="tns:SessionlessInvokeResponseType" />

  <xs:complexType name="FindServersRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="EndpointUrl" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="LocaleIds" type="ua:ListOfString" minOccurs="0" nillable="true" />
      <xs:element name="ServerUris" type="ua:ListOfString" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="FindServersRequest" type="tns:FindServersRequest" />

  <xs:complexType name="FindServersResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="Servers" type="tns:ListOfApplicationDescription" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="FindServersResponse" type="tns:FindServersResponse" />

  <xs:complexType name="ServerOnNetwork">
    <xs:sequence>
      <xs:element name="RecordId" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="ServerName" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="DiscoveryUrl" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="ServerCapabilities" type="ua:ListOfString" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ServerOnNetwork" type="tns:ServerOnNetwork" />

  <xs:complexType name="ListOfServerOnNetwork">
    <xs:sequence>
      <xs:element name="ServerOnNetwork" type="tns:ServerOnNetwork" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfServerOnNetwork" type="tns:ListOfServerOnNetwork" nillable="true"></xs:element>

  <xs:complexType name="FindServersOnNetworkRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="StartingRecordId" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="MaxRecordsToReturn" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="ServerCapabilityFilter" type="ua:ListOfString" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="FindServersOnNetworkRequest" type="tns:FindServersOnNetworkRequest" />

  <xs:complexType name="FindServersOnNetworkResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="LastCounterResetTime" type="xs:dateTime" minOccurs="0" />
      <xs:element name="Servers" type="tns:ListOfServerOnNetwork" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="FindServersOnNetworkResponse" type="tns:FindServersOnNetworkResponse" />

  <xs:element name="ApplicationInstanceCertificate" type="xs:base64Binary" />

  <xs:simpleType  name="MessageSecurityMode">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Invalid_0" />
      <xs:enumeration value="None_1" />
      <xs:enumeration value="Sign_2" />
      <xs:enumeration value="SignAndEncrypt_3" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="MessageSecurityMode" type="tns:MessageSecurityMode" />

  <xs:simpleType  name="UserTokenType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Anonymous_0" />
      <xs:enumeration value="UserName_1" />
      <xs:enumeration value="Certificate_2" />
      <xs:enumeration value="IssuedToken_3" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="UserTokenType" type="tns:UserTokenType" />

  <xs:complexType name="UserTokenPolicy">
    <xs:sequence>
      <xs:element name="PolicyId" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="TokenType" type="tns:UserTokenType" minOccurs="0" />
      <xs:element name="IssuedTokenType" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="IssuerEndpointUrl" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="SecurityPolicyUri" type="xs:string" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="UserTokenPolicy" type="tns:UserTokenPolicy" />

  <xs:complexType name="ListOfUserTokenPolicy">
    <xs:sequence>
      <xs:element name="UserTokenPolicy" type="tns:UserTokenPolicy" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfUserTokenPolicy" type="tns:ListOfUserTokenPolicy" nillable="true"></xs:element>

  <xs:complexType name="EndpointDescription">
    <xs:sequence>
      <xs:element name="EndpointUrl" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="Server" type="tns:ApplicationDescription" minOccurs="0" nillable="true" />
      <xs:element name="ServerCertificate" type="xs:base64Binary" minOccurs="0" nillable="true" />
      <xs:element name="SecurityMode" type="tns:MessageSecurityMode" minOccurs="0" />
      <xs:element name="SecurityPolicyUri" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="UserIdentityTokens" type="tns:ListOfUserTokenPolicy" minOccurs="0" nillable="true" />
      <xs:element name="TransportProfileUri" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="SecurityLevel" type="xs:unsignedByte" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="EndpointDescription" type="tns:EndpointDescription" />

  <xs:complexType name="ListOfEndpointDescription">
    <xs:sequence>
      <xs:element name="EndpointDescription" type="tns:EndpointDescription" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfEndpointDescription" type="tns:ListOfEndpointDescription" nillable="true"></xs:element>

  <xs:complexType name="GetEndpointsRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="EndpointUrl" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="LocaleIds" type="ua:ListOfString" minOccurs="0" nillable="true" />
      <xs:element name="ProfileUris" type="ua:ListOfString" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="GetEndpointsRequest" type="tns:GetEndpointsRequest" />

  <xs:complexType name="GetEndpointsResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="Endpoints" type="tns:ListOfEndpointDescription" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="GetEndpointsResponse" type="tns:GetEndpointsResponse" />

  <xs:complexType name="RegisteredServer">
    <xs:sequence>
      <xs:element name="ServerUri" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="ProductUri" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="ServerNames" type="ua:ListOfLocalizedText" minOccurs="0" nillable="true" />
      <xs:element name="ServerType" type="tns:ApplicationType" minOccurs="0" />
      <xs:element name="GatewayServerUri" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="DiscoveryUrls" type="ua:ListOfString" minOccurs="0" nillable="true" />
      <xs:element name="SemaphoreFilePath" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="IsOnline" type="xs:boolean" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RegisteredServer" type="tns:RegisteredServer" />

  <xs:complexType name="ListOfRegisteredServer">
    <xs:sequence>
      <xs:element name="RegisteredServer" type="tns:RegisteredServer" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfRegisteredServer" type="tns:ListOfRegisteredServer" nillable="true"></xs:element>

  <xs:complexType name="RegisterServerRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="Server" type="tns:RegisteredServer" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RegisterServerRequest" type="tns:RegisterServerRequest" />

  <xs:complexType name="RegisterServerResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RegisterServerResponse" type="tns:RegisterServerResponse" />

  <xs:complexType name="DiscoveryConfiguration">
    <xs:sequence>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="DiscoveryConfiguration" type="tns:DiscoveryConfiguration" />

  <xs:complexType name="MdnsDiscoveryConfiguration">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:DiscoveryConfiguration">
        <xs:sequence>
          <xs:element name="MdnsServerName" type="xs:string" minOccurs="0" nillable="true" />
          <xs:element name="ServerCapabilities" type="ua:ListOfString" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="MdnsDiscoveryConfiguration" type="tns:MdnsDiscoveryConfiguration" />

  <xs:complexType name="RegisterServer2Request">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="Server" type="tns:RegisteredServer" minOccurs="0" nillable="true" />
      <xs:element name="DiscoveryConfiguration" type="ua:ListOfExtensionObject" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RegisterServer2Request" type="tns:RegisterServer2Request" />

  <xs:complexType name="RegisterServer2Response">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="ConfigurationResults" type="ua:ListOfStatusCode" minOccurs="0" nillable="true" />
      <xs:element name="DiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RegisterServer2Response" type="tns:RegisterServer2Response" />

  <xs:simpleType  name="SecurityTokenRequestType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Issue_0" />
      <xs:enumeration value="Renew_1" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="SecurityTokenRequestType" type="tns:SecurityTokenRequestType" />

  <xs:complexType name="ChannelSecurityToken">
    <xs:sequence>
      <xs:element name="ChannelId" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="TokenId" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="CreatedAt" type="xs:dateTime" minOccurs="0" />
      <xs:element name="RevisedLifetime" type="xs:unsignedInt" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ChannelSecurityToken" type="tns:ChannelSecurityToken" />

  <xs:complexType name="OpenSecureChannelRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="ClientProtocolVersion" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="RequestType" type="tns:SecurityTokenRequestType" minOccurs="0" />
      <xs:element name="SecurityMode" type="tns:MessageSecurityMode" minOccurs="0" />
      <xs:element name="ClientNonce" type="xs:base64Binary" minOccurs="0" nillable="true" />
      <xs:element name="RequestedLifetime" type="xs:unsignedInt" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="OpenSecureChannelRequest" type="tns:OpenSecureChannelRequest" />

  <xs:complexType name="OpenSecureChannelResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="ServerProtocolVersion" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="SecurityToken" type="tns:ChannelSecurityToken" minOccurs="0" nillable="true" />
      <xs:element name="ServerNonce" type="xs:base64Binary" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="OpenSecureChannelResponse" type="tns:OpenSecureChannelResponse" />

  <xs:complexType name="CloseSecureChannelRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CloseSecureChannelRequest" type="tns:CloseSecureChannelRequest" />

  <xs:complexType name="CloseSecureChannelResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CloseSecureChannelResponse" type="tns:CloseSecureChannelResponse" />

  <xs:complexType name="SignedSoftwareCertificate">
    <xs:sequence>
      <xs:element name="CertificateData" type="xs:base64Binary" minOccurs="0" nillable="true" />
      <xs:element name="Signature" type="xs:base64Binary" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SignedSoftwareCertificate" type="tns:SignedSoftwareCertificate" />

  <xs:complexType name="ListOfSignedSoftwareCertificate">
    <xs:sequence>
      <xs:element name="SignedSoftwareCertificate" type="tns:SignedSoftwareCertificate" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfSignedSoftwareCertificate" type="tns:ListOfSignedSoftwareCertificate" nillable="true"></xs:element>

  <xs:element name="SessionAuthenticationToken" type="ua:NodeId" />

  <xs:complexType name="SignatureData">
    <xs:sequence>
      <xs:element name="Algorithm" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="Signature" type="xs:base64Binary" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SignatureData" type="tns:SignatureData" />

  <xs:complexType name="CreateSessionRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="ClientDescription" type="tns:ApplicationDescription" minOccurs="0" nillable="true" />
      <xs:element name="ServerUri" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="EndpointUrl" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="SessionName" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="ClientNonce" type="xs:base64Binary" minOccurs="0" nillable="true" />
      <xs:element name="ClientCertificate" type="xs:base64Binary" minOccurs="0" nillable="true" />
      <xs:element name="RequestedSessionTimeout" type="xs:double" minOccurs="0" />
      <xs:element name="MaxResponseMessageSize" type="xs:unsignedInt" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CreateSessionRequest" type="tns:CreateSessionRequest" />

  <xs:complexType name="CreateSessionResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="SessionId" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="AuthenticationToken" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="RevisedSessionTimeout" type="xs:double" minOccurs="0" />
      <xs:element name="ServerNonce" type="xs:base64Binary" minOccurs="0" nillable="true" />
      <xs:element name="ServerCertificate" type="xs:base64Binary" minOccurs="0" nillable="true" />
      <xs:element name="ServerEndpoints" type="tns:ListOfEndpointDescription" minOccurs="0" nillable="true" />
      <xs:element name="ServerSoftwareCertificates" type="tns:ListOfSignedSoftwareCertificate" minOccurs="0" nillable="true" />
      <xs:element name="ServerSignature" type="tns:SignatureData" minOccurs="0" nillable="true" />
      <xs:element name="MaxRequestMessageSize" type="xs:unsignedInt" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CreateSessionResponse" type="tns:CreateSessionResponse" />

  <xs:complexType name="UserIdentityToken">
    <xs:sequence>
      <xs:element name="PolicyId" type="xs:string" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="UserIdentityToken" type="tns:UserIdentityToken" />

  <xs:complexType name="AnonymousIdentityToken">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:UserIdentityToken">
        <xs:sequence>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="AnonymousIdentityToken" type="tns:AnonymousIdentityToken" />

  <xs:complexType name="UserNameIdentityToken">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:UserIdentityToken">
        <xs:sequence>
          <xs:element name="UserName" type="xs:string" minOccurs="0" nillable="true" />
          <xs:element name="Password" type="xs:base64Binary" minOccurs="0" nillable="true" />
          <xs:element name="EncryptionAlgorithm" type="xs:string" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="UserNameIdentityToken" type="tns:UserNameIdentityToken" />

  <xs:complexType name="X509IdentityToken">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:UserIdentityToken">
        <xs:sequence>
          <xs:element name="CertificateData" type="xs:base64Binary" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="X509IdentityToken" type="tns:X509IdentityToken" />

  <xs:complexType name="IssuedIdentityToken">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:UserIdentityToken">
        <xs:sequence>
          <xs:element name="TokenData" type="xs:base64Binary" minOccurs="0" nillable="true" />
          <xs:element name="EncryptionAlgorithm" type="xs:string" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="IssuedIdentityToken" type="tns:IssuedIdentityToken" />

  <xs:element name="RsaEncryptedSecret" type="ua:Variant" />

  <xs:element name="EccEncryptedSecret" type="ua:Variant" />

  <xs:complexType name="ActivateSessionRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="ClientSignature" type="tns:SignatureData" minOccurs="0" nillable="true" />
      <xs:element name="ClientSoftwareCertificates" type="tns:ListOfSignedSoftwareCertificate" minOccurs="0" nillable="true" />
      <xs:element name="LocaleIds" type="ua:ListOfString" minOccurs="0" nillable="true" />
      <xs:element name="UserIdentityToken" type="ua:ExtensionObject" minOccurs="0" nillable="true" />
      <xs:element name="UserTokenSignature" type="tns:SignatureData" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ActivateSessionRequest" type="tns:ActivateSessionRequest" />

  <xs:complexType name="ActivateSessionResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="ServerNonce" type="xs:base64Binary" minOccurs="0" nillable="true" />
      <xs:element name="Results" type="ua:ListOfStatusCode" minOccurs="0" nillable="true" />
      <xs:element name="DiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ActivateSessionResponse" type="tns:ActivateSessionResponse" />

  <xs:complexType name="CloseSessionRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="DeleteSubscriptions" type="xs:boolean" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CloseSessionRequest" type="tns:CloseSessionRequest" />

  <xs:complexType name="CloseSessionResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CloseSessionResponse" type="tns:CloseSessionResponse" />

  <xs:complexType name="CancelRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="RequestHandle" type="xs:unsignedInt" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CancelRequest" type="tns:CancelRequest" />

  <xs:complexType name="CancelResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="CancelCount" type="xs:unsignedInt" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CancelResponse" type="tns:CancelResponse" />

  <xs:simpleType  name="NodeAttributesMask">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None_0" />
      <xs:enumeration value="AccessLevel_1" />
      <xs:enumeration value="ArrayDimensions_2" />
      <xs:enumeration value="BrowseName_4" />
      <xs:enumeration value="ContainsNoLoops_8" />
      <xs:enumeration value="DataType_16" />
      <xs:enumeration value="Description_32" />
      <xs:enumeration value="DisplayName_64" />
      <xs:enumeration value="EventNotifier_128" />
      <xs:enumeration value="Executable_256" />
      <xs:enumeration value="Historizing_512" />
      <xs:enumeration value="InverseName_1024" />
      <xs:enumeration value="IsAbstract_2048" />
      <xs:enumeration value="MinimumSamplingInterval_4096" />
      <xs:enumeration value="NodeClass_8192" />
      <xs:enumeration value="NodeId_16384" />
      <xs:enumeration value="Symmetric_32768" />
      <xs:enumeration value="UserAccessLevel_65536" />
      <xs:enumeration value="UserExecutable_131072" />
      <xs:enumeration value="UserWriteMask_262144" />
      <xs:enumeration value="ValueRank_524288" />
      <xs:enumeration value="WriteMask_1048576" />
      <xs:enumeration value="Value_2097152" />
      <xs:enumeration value="DataTypeDefinition_4194304" />
      <xs:enumeration value="RolePermissions_8388608" />
      <xs:enumeration value="AccessRestrictions_16777216" />
      <xs:enumeration value="All_33554431" />
      <xs:enumeration value="BaseNode_26501220" />
      <xs:enumeration value="Object_26501348" />
      <xs:enumeration value="ObjectType_26503268" />
      <xs:enumeration value="Variable_26571383" />
      <xs:enumeration value="VariableType_28600438" />
      <xs:enumeration value="Method_26632548" />
      <xs:enumeration value="ReferenceType_26537060" />
      <xs:enumeration value="View_26501356" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="NodeAttributesMask" type="tns:NodeAttributesMask" />

  <xs:complexType name="NodeAttributes">
    <xs:sequence>
      <xs:element name="SpecifiedAttributes" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="DisplayName" type="ua:LocalizedText" minOccurs="0" nillable="true" />
      <xs:element name="Description" type="ua:LocalizedText" minOccurs="0" nillable="true" />
      <xs:element name="WriteMask" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="UserWriteMask" type="xs:unsignedInt" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="NodeAttributes" type="tns:NodeAttributes" />

  <xs:complexType name="ObjectAttributes">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:NodeAttributes">
        <xs:sequence>
          <xs:element name="EventNotifier" type="xs:unsignedByte" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="ObjectAttributes" type="tns:ObjectAttributes" />

  <xs:complexType name="VariableAttributes">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:NodeAttributes">
        <xs:sequence>
          <xs:element name="Value" type="ua:Variant" minOccurs="0" />
          <xs:element name="DataType" type="ua:NodeId" minOccurs="0" nillable="true" />
          <xs:element name="ValueRank" type="xs:int" minOccurs="0" />
          <xs:element name="ArrayDimensions" type="ua:ListOfUInt32" minOccurs="0" nillable="true" />
          <xs:element name="AccessLevel" type="xs:unsignedByte" minOccurs="0" />
          <xs:element name="UserAccessLevel" type="xs:unsignedByte" minOccurs="0" />
          <xs:element name="MinimumSamplingInterval" type="xs:double" minOccurs="0" />
          <xs:element name="Historizing" type="xs:boolean" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="VariableAttributes" type="tns:VariableAttributes" />

  <xs:complexType name="MethodAttributes">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:NodeAttributes">
        <xs:sequence>
          <xs:element name="Executable" type="xs:boolean" minOccurs="0" />
          <xs:element name="UserExecutable" type="xs:boolean" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="MethodAttributes" type="tns:MethodAttributes" />

  <xs:complexType name="ObjectTypeAttributes">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:NodeAttributes">
        <xs:sequence>
          <xs:element name="IsAbstract" type="xs:boolean" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="ObjectTypeAttributes" type="tns:ObjectTypeAttributes" />

  <xs:complexType name="VariableTypeAttributes">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:NodeAttributes">
        <xs:sequence>
          <xs:element name="Value" type="ua:Variant" minOccurs="0" />
          <xs:element name="DataType" type="ua:NodeId" minOccurs="0" nillable="true" />
          <xs:element name="ValueRank" type="xs:int" minOccurs="0" />
          <xs:element name="ArrayDimensions" type="ua:ListOfUInt32" minOccurs="0" nillable="true" />
          <xs:element name="IsAbstract" type="xs:boolean" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="VariableTypeAttributes" type="tns:VariableTypeAttributes" />

  <xs:complexType name="ReferenceTypeAttributes">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:NodeAttributes">
        <xs:sequence>
          <xs:element name="IsAbstract" type="xs:boolean" minOccurs="0" />
          <xs:element name="Symmetric" type="xs:boolean" minOccurs="0" />
          <xs:element name="InverseName" type="ua:LocalizedText" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="ReferenceTypeAttributes" type="tns:ReferenceTypeAttributes" />

  <xs:complexType name="DataTypeAttributes">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:NodeAttributes">
        <xs:sequence>
          <xs:element name="IsAbstract" type="xs:boolean" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="DataTypeAttributes" type="tns:DataTypeAttributes" />

  <xs:complexType name="ViewAttributes">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:NodeAttributes">
        <xs:sequence>
          <xs:element name="ContainsNoLoops" type="xs:boolean" minOccurs="0" />
          <xs:element name="EventNotifier" type="xs:unsignedByte" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="ViewAttributes" type="tns:ViewAttributes" />

  <xs:complexType name="GenericAttributeValue">
    <xs:sequence>
      <xs:element name="AttributeId" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="Value" type="ua:Variant" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="GenericAttributeValue" type="tns:GenericAttributeValue" />

  <xs:complexType name="ListOfGenericAttributeValue">
    <xs:sequence>
      <xs:element name="GenericAttributeValue" type="tns:GenericAttributeValue" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfGenericAttributeValue" type="tns:ListOfGenericAttributeValue" nillable="true"></xs:element>

  <xs:complexType name="GenericAttributes">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:NodeAttributes">
        <xs:sequence>
          <xs:element name="AttributeValues" type="tns:ListOfGenericAttributeValue" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="GenericAttributes" type="tns:GenericAttributes" />

  <xs:complexType name="AddNodesItem">
    <xs:sequence>
      <xs:element name="ParentNodeId" type="ua:ExpandedNodeId" minOccurs="0" nillable="true" />
      <xs:element name="ReferenceTypeId" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="RequestedNewNodeId" type="ua:ExpandedNodeId" minOccurs="0" nillable="true" />
      <xs:element name="BrowseName" type="ua:QualifiedName" minOccurs="0" nillable="true" />
      <xs:element name="NodeClass" type="tns:NodeClass" minOccurs="0" />
      <xs:element name="NodeAttributes" type="ua:ExtensionObject" minOccurs="0" nillable="true" />
      <xs:element name="TypeDefinition" type="ua:ExpandedNodeId" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="AddNodesItem" type="tns:AddNodesItem" />

  <xs:complexType name="ListOfAddNodesItem">
    <xs:sequence>
      <xs:element name="AddNodesItem" type="tns:AddNodesItem" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfAddNodesItem" type="tns:ListOfAddNodesItem" nillable="true"></xs:element>

  <xs:complexType name="AddNodesResult">
    <xs:sequence>
      <xs:element name="StatusCode" type="ua:StatusCode" minOccurs="0" />
      <xs:element name="AddedNodeId" type="ua:NodeId" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="AddNodesResult" type="tns:AddNodesResult" />

  <xs:complexType name="ListOfAddNodesResult">
    <xs:sequence>
      <xs:element name="AddNodesResult" type="tns:AddNodesResult" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfAddNodesResult" type="tns:ListOfAddNodesResult" nillable="true"></xs:element>

  <xs:complexType name="AddNodesRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="NodesToAdd" type="tns:ListOfAddNodesItem" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="AddNodesRequest" type="tns:AddNodesRequest" />

  <xs:complexType name="AddNodesResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="Results" type="tns:ListOfAddNodesResult" minOccurs="0" nillable="true" />
      <xs:element name="DiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="AddNodesResponse" type="tns:AddNodesResponse" />

  <xs:complexType name="AddReferencesItem">
    <xs:sequence>
      <xs:element name="SourceNodeId" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="ReferenceTypeId" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="IsForward" type="xs:boolean" minOccurs="0" />
      <xs:element name="TargetServerUri" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="TargetNodeId" type="ua:ExpandedNodeId" minOccurs="0" nillable="true" />
      <xs:element name="TargetNodeClass" type="tns:NodeClass" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="AddReferencesItem" type="tns:AddReferencesItem" />

  <xs:complexType name="ListOfAddReferencesItem">
    <xs:sequence>
      <xs:element name="AddReferencesItem" type="tns:AddReferencesItem" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfAddReferencesItem" type="tns:ListOfAddReferencesItem" nillable="true"></xs:element>

  <xs:complexType name="AddReferencesRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="ReferencesToAdd" type="tns:ListOfAddReferencesItem" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="AddReferencesRequest" type="tns:AddReferencesRequest" />

  <xs:complexType name="AddReferencesResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="Results" type="ua:ListOfStatusCode" minOccurs="0" nillable="true" />
      <xs:element name="DiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="AddReferencesResponse" type="tns:AddReferencesResponse" />

  <xs:complexType name="DeleteNodesItem">
    <xs:sequence>
      <xs:element name="NodeId" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="DeleteTargetReferences" type="xs:boolean" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="DeleteNodesItem" type="tns:DeleteNodesItem" />

  <xs:complexType name="ListOfDeleteNodesItem">
    <xs:sequence>
      <xs:element name="DeleteNodesItem" type="tns:DeleteNodesItem" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfDeleteNodesItem" type="tns:ListOfDeleteNodesItem" nillable="true"></xs:element>

  <xs:complexType name="DeleteNodesRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="NodesToDelete" type="tns:ListOfDeleteNodesItem" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="DeleteNodesRequest" type="tns:DeleteNodesRequest" />

  <xs:complexType name="DeleteNodesResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="Results" type="ua:ListOfStatusCode" minOccurs="0" nillable="true" />
      <xs:element name="DiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="DeleteNodesResponse" type="tns:DeleteNodesResponse" />

  <xs:complexType name="DeleteReferencesItem">
    <xs:sequence>
      <xs:element name="SourceNodeId" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="ReferenceTypeId" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="IsForward" type="xs:boolean" minOccurs="0" />
      <xs:element name="TargetNodeId" type="ua:ExpandedNodeId" minOccurs="0" nillable="true" />
      <xs:element name="DeleteBidirectional" type="xs:boolean" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="DeleteReferencesItem" type="tns:DeleteReferencesItem" />

  <xs:complexType name="ListOfDeleteReferencesItem">
    <xs:sequence>
      <xs:element name="DeleteReferencesItem" type="tns:DeleteReferencesItem" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfDeleteReferencesItem" type="tns:ListOfDeleteReferencesItem" nillable="true"></xs:element>

  <xs:complexType name="DeleteReferencesRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="ReferencesToDelete" type="tns:ListOfDeleteReferencesItem" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="DeleteReferencesRequest" type="tns:DeleteReferencesRequest" />

  <xs:complexType name="DeleteReferencesResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="Results" type="ua:ListOfStatusCode" minOccurs="0" nillable="true" />
      <xs:element name="DiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="DeleteReferencesResponse" type="tns:DeleteReferencesResponse" />

  <xs:simpleType  name="AttributeWriteMask">
    <xs:restriction base="xs:unsignedInt">
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="AttributeWriteMask" type="tns:AttributeWriteMask" />

  <xs:simpleType  name="BrowseDirection">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Forward_0" />
      <xs:enumeration value="Inverse_1" />
      <xs:enumeration value="Both_2" />
      <xs:enumeration value="Invalid_3" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="BrowseDirection" type="tns:BrowseDirection" />

  <xs:complexType name="ViewDescription">
    <xs:sequence>
      <xs:element name="ViewId" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="Timestamp" type="xs:dateTime" minOccurs="0" />
      <xs:element name="ViewVersion" type="xs:unsignedInt" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ViewDescription" type="tns:ViewDescription" />

  <xs:complexType name="BrowseDescription">
    <xs:sequence>
      <xs:element name="NodeId" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="BrowseDirection" type="tns:BrowseDirection" minOccurs="0" />
      <xs:element name="ReferenceTypeId" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="IncludeSubtypes" type="xs:boolean" minOccurs="0" />
      <xs:element name="NodeClassMask" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="ResultMask" type="xs:unsignedInt" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="BrowseDescription" type="tns:BrowseDescription" />

  <xs:complexType name="ListOfBrowseDescription">
    <xs:sequence>
      <xs:element name="BrowseDescription" type="tns:BrowseDescription" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfBrowseDescription" type="tns:ListOfBrowseDescription" nillable="true"></xs:element>

  <xs:simpleType  name="BrowseResultMask">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None_0" />
      <xs:enumeration value="ReferenceTypeId_1" />
      <xs:enumeration value="IsForward_2" />
      <xs:enumeration value="NodeClass_4" />
      <xs:enumeration value="BrowseName_8" />
      <xs:enumeration value="DisplayName_16" />
      <xs:enumeration value="TypeDefinition_32" />
      <xs:enumeration value="All_63" />
      <xs:enumeration value="ReferenceTypeInfo_3" />
      <xs:enumeration value="TargetInfo_60" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="BrowseResultMask" type="tns:BrowseResultMask" />

  <xs:complexType name="ReferenceDescription">
    <xs:sequence>
      <xs:element name="ReferenceTypeId" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="IsForward" type="xs:boolean" minOccurs="0" />
      <xs:element name="NodeId" type="ua:ExpandedNodeId" minOccurs="0" nillable="true" />
      <xs:element name="BrowseName" type="ua:QualifiedName" minOccurs="0" nillable="true" />
      <xs:element name="DisplayName" type="ua:LocalizedText" minOccurs="0" nillable="true" />
      <xs:element name="NodeClass" type="tns:NodeClass" minOccurs="0" />
      <xs:element name="TypeDefinition" type="ua:ExpandedNodeId" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ReferenceDescription" type="tns:ReferenceDescription" />

  <xs:complexType name="ListOfReferenceDescription">
    <xs:sequence>
      <xs:element name="ReferenceDescription" type="tns:ReferenceDescription" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfReferenceDescription" type="tns:ListOfReferenceDescription" nillable="true"></xs:element>

  <xs:element name="ContinuationPoint" type="xs:base64Binary" />

  <xs:complexType name="BrowseResult">
    <xs:sequence>
      <xs:element name="StatusCode" type="ua:StatusCode" minOccurs="0" />
      <xs:element name="ContinuationPoint" type="xs:base64Binary" minOccurs="0" nillable="true" />
      <xs:element name="References" type="tns:ListOfReferenceDescription" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="BrowseResult" type="tns:BrowseResult" />

  <xs:complexType name="ListOfBrowseResult">
    <xs:sequence>
      <xs:element name="BrowseResult" type="tns:BrowseResult" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfBrowseResult" type="tns:ListOfBrowseResult" nillable="true"></xs:element>

  <xs:complexType name="BrowseRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="View" type="tns:ViewDescription" minOccurs="0" nillable="true" />
      <xs:element name="RequestedMaxReferencesPerNode" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="NodesToBrowse" type="tns:ListOfBrowseDescription" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="BrowseRequest" type="tns:BrowseRequest" />

  <xs:complexType name="BrowseResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="Results" type="tns:ListOfBrowseResult" minOccurs="0" nillable="true" />
      <xs:element name="DiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="BrowseResponse" type="tns:BrowseResponse" />

  <xs:complexType name="BrowseNextRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="ReleaseContinuationPoints" type="xs:boolean" minOccurs="0" />
      <xs:element name="ContinuationPoints" type="ua:ListOfByteString" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="BrowseNextRequest" type="tns:BrowseNextRequest" />

  <xs:complexType name="BrowseNextResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="Results" type="tns:ListOfBrowseResult" minOccurs="0" nillable="true" />
      <xs:element name="DiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="BrowseNextResponse" type="tns:BrowseNextResponse" />

  <xs:complexType name="RelativePathElement">
    <xs:sequence>
      <xs:element name="ReferenceTypeId" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="IsInverse" type="xs:boolean" minOccurs="0" />
      <xs:element name="IncludeSubtypes" type="xs:boolean" minOccurs="0" />
      <xs:element name="TargetName" type="ua:QualifiedName" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RelativePathElement" type="tns:RelativePathElement" />

  <xs:complexType name="ListOfRelativePathElement">
    <xs:sequence>
      <xs:element name="RelativePathElement" type="tns:RelativePathElement" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfRelativePathElement" type="tns:ListOfRelativePathElement" nillable="true"></xs:element>

  <xs:complexType name="RelativePath">
    <xs:sequence>
      <xs:element name="Elements" type="tns:ListOfRelativePathElement" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RelativePath" type="tns:RelativePath" />

  <xs:complexType name="BrowsePath">
    <xs:sequence>
      <xs:element name="StartingNode" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="RelativePath" type="tns:RelativePath" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="BrowsePath" type="tns:BrowsePath" />

  <xs:complexType name="ListOfBrowsePath">
    <xs:sequence>
      <xs:element name="BrowsePath" type="tns:BrowsePath" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfBrowsePath" type="tns:ListOfBrowsePath" nillable="true"></xs:element>

  <xs:complexType name="BrowsePathTarget">
    <xs:sequence>
      <xs:element name="TargetId" type="ua:ExpandedNodeId" minOccurs="0" nillable="true" />
      <xs:element name="RemainingPathIndex" type="xs:unsignedInt" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="BrowsePathTarget" type="tns:BrowsePathTarget" />

  <xs:complexType name="ListOfBrowsePathTarget">
    <xs:sequence>
      <xs:element name="BrowsePathTarget" type="tns:BrowsePathTarget" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfBrowsePathTarget" type="tns:ListOfBrowsePathTarget" nillable="true"></xs:element>

  <xs:complexType name="BrowsePathResult">
    <xs:sequence>
      <xs:element name="StatusCode" type="ua:StatusCode" minOccurs="0" />
      <xs:element name="Targets" type="tns:ListOfBrowsePathTarget" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="BrowsePathResult" type="tns:BrowsePathResult" />

  <xs:complexType name="ListOfBrowsePathResult">
    <xs:sequence>
      <xs:element name="BrowsePathResult" type="tns:BrowsePathResult" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfBrowsePathResult" type="tns:ListOfBrowsePathResult" nillable="true"></xs:element>

  <xs:complexType name="TranslateBrowsePathsToNodeIdsRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="BrowsePaths" type="tns:ListOfBrowsePath" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="TranslateBrowsePathsToNodeIdsRequest" type="tns:TranslateBrowsePathsToNodeIdsRequest" />

  <xs:complexType name="TranslateBrowsePathsToNodeIdsResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="Results" type="tns:ListOfBrowsePathResult" minOccurs="0" nillable="true" />
      <xs:element name="DiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="TranslateBrowsePathsToNodeIdsResponse" type="tns:TranslateBrowsePathsToNodeIdsResponse" />

  <xs:complexType name="RegisterNodesRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="NodesToRegister" type="ua:ListOfNodeId" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RegisterNodesRequest" type="tns:RegisterNodesRequest" />

  <xs:complexType name="RegisterNodesResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="RegisteredNodeIds" type="ua:ListOfNodeId" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RegisterNodesResponse" type="tns:RegisterNodesResponse" />

  <xs:complexType name="UnregisterNodesRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="NodesToUnregister" type="ua:ListOfNodeId" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="UnregisterNodesRequest" type="tns:UnregisterNodesRequest" />

  <xs:complexType name="UnregisterNodesResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="UnregisterNodesResponse" type="tns:UnregisterNodesResponse" />

  <xs:element name="Counter" type="xs:unsignedInt" />

  <xs:element name="NumericRange" type="xs:string" />

  <xs:complexType name="EndpointConfiguration">
    <xs:sequence>
      <xs:element name="OperationTimeout" type="xs:int" minOccurs="0" />
      <xs:element name="UseBinaryEncoding" type="xs:boolean" minOccurs="0" />
      <xs:element name="MaxStringLength" type="xs:int" minOccurs="0" />
      <xs:element name="MaxByteStringLength" type="xs:int" minOccurs="0" />
      <xs:element name="MaxArrayLength" type="xs:int" minOccurs="0" />
      <xs:element name="MaxMessageSize" type="xs:int" minOccurs="0" />
      <xs:element name="MaxBufferSize" type="xs:int" minOccurs="0" />
      <xs:element name="ChannelLifetime" type="xs:int" minOccurs="0" />
      <xs:element name="SecurityTokenLifetime" type="xs:int" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="EndpointConfiguration" type="tns:EndpointConfiguration" />

  <xs:complexType name="ListOfEndpointConfiguration">
    <xs:sequence>
      <xs:element name="EndpointConfiguration" type="tns:EndpointConfiguration" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfEndpointConfiguration" type="tns:ListOfEndpointConfiguration" nillable="true"></xs:element>

  <xs:complexType name="QueryDataDescription">
    <xs:sequence>
      <xs:element name="RelativePath" type="tns:RelativePath" minOccurs="0" nillable="true" />
      <xs:element name="AttributeId" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="IndexRange" type="xs:string" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="QueryDataDescription" type="tns:QueryDataDescription" />

  <xs:complexType name="ListOfQueryDataDescription">
    <xs:sequence>
      <xs:element name="QueryDataDescription" type="tns:QueryDataDescription" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfQueryDataDescription" type="tns:ListOfQueryDataDescription" nillable="true"></xs:element>

  <xs:complexType name="NodeTypeDescription">
    <xs:sequence>
      <xs:element name="TypeDefinitionNode" type="ua:ExpandedNodeId" minOccurs="0" nillable="true" />
      <xs:element name="IncludeSubTypes" type="xs:boolean" minOccurs="0" />
      <xs:element name="DataToReturn" type="tns:ListOfQueryDataDescription" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="NodeTypeDescription" type="tns:NodeTypeDescription" />

  <xs:complexType name="ListOfNodeTypeDescription">
    <xs:sequence>
      <xs:element name="NodeTypeDescription" type="tns:NodeTypeDescription" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfNodeTypeDescription" type="tns:ListOfNodeTypeDescription" nillable="true"></xs:element>

  <xs:simpleType  name="FilterOperator">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Equals_0" />
      <xs:enumeration value="IsNull_1" />
      <xs:enumeration value="GreaterThan_2" />
      <xs:enumeration value="LessThan_3" />
      <xs:enumeration value="GreaterThanOrEqual_4" />
      <xs:enumeration value="LessThanOrEqual_5" />
      <xs:enumeration value="Like_6" />
      <xs:enumeration value="Not_7" />
      <xs:enumeration value="Between_8" />
      <xs:enumeration value="InList_9" />
      <xs:enumeration value="And_10" />
      <xs:enumeration value="Or_11" />
      <xs:enumeration value="Cast_12" />
      <xs:enumeration value="InView_13" />
      <xs:enumeration value="OfType_14" />
      <xs:enumeration value="RelatedTo_15" />
      <xs:enumeration value="BitwiseAnd_16" />
      <xs:enumeration value="BitwiseOr_17" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="FilterOperator" type="tns:FilterOperator" />

  <xs:complexType name="QueryDataSet">
    <xs:sequence>
      <xs:element name="NodeId" type="ua:ExpandedNodeId" minOccurs="0" nillable="true" />
      <xs:element name="TypeDefinitionNode" type="ua:ExpandedNodeId" minOccurs="0" nillable="true" />
      <xs:element name="Values" type="ua:ListOfVariant" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="QueryDataSet" type="tns:QueryDataSet" />

  <xs:complexType name="ListOfQueryDataSet">
    <xs:sequence>
      <xs:element name="QueryDataSet" type="tns:QueryDataSet" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfQueryDataSet" type="tns:ListOfQueryDataSet" nillable="true"></xs:element>

  <xs:complexType name="NodeReference">
    <xs:sequence>
      <xs:element name="NodeId" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="ReferenceTypeId" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="IsForward" type="xs:boolean" minOccurs="0" />
      <xs:element name="ReferencedNodeIds" type="ua:ListOfNodeId" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="NodeReference" type="tns:NodeReference" />

  <xs:complexType name="ListOfNodeReference">
    <xs:sequence>
      <xs:element name="NodeReference" type="tns:NodeReference" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfNodeReference" type="tns:ListOfNodeReference" nillable="true"></xs:element>

  <xs:complexType name="ContentFilterElement">
    <xs:sequence>
      <xs:element name="FilterOperator" type="tns:FilterOperator" minOccurs="0" />
      <xs:element name="FilterOperands" type="ua:ListOfExtensionObject" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ContentFilterElement" type="tns:ContentFilterElement" />

  <xs:complexType name="ListOfContentFilterElement">
    <xs:sequence>
      <xs:element name="ContentFilterElement" type="tns:ContentFilterElement" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfContentFilterElement" type="tns:ListOfContentFilterElement" nillable="true"></xs:element>

  <xs:complexType name="ContentFilter">
    <xs:sequence>
      <xs:element name="Elements" type="tns:ListOfContentFilterElement" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ContentFilter" type="tns:ContentFilter" />

  <xs:complexType name="ListOfContentFilter">
    <xs:sequence>
      <xs:element name="ContentFilter" type="tns:ContentFilter" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfContentFilter" type="tns:ListOfContentFilter" nillable="true"></xs:element>

  <xs:complexType name="FilterOperand">
    <xs:sequence>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="FilterOperand" type="tns:FilterOperand" />

  <xs:complexType name="ElementOperand">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:FilterOperand">
        <xs:sequence>
          <xs:element name="Index" type="xs:unsignedInt" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="ElementOperand" type="tns:ElementOperand" />

  <xs:complexType name="LiteralOperand">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:FilterOperand">
        <xs:sequence>
          <xs:element name="Value" type="ua:Variant" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="LiteralOperand" type="tns:LiteralOperand" />

  <xs:complexType name="AttributeOperand">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:FilterOperand">
        <xs:sequence>
          <xs:element name="NodeId" type="ua:NodeId" minOccurs="0" nillable="true" />
          <xs:element name="Alias" type="xs:string" minOccurs="0" nillable="true" />
          <xs:element name="BrowsePath" type="tns:RelativePath" minOccurs="0" nillable="true" />
          <xs:element name="AttributeId" type="xs:unsignedInt" minOccurs="0" />
          <xs:element name="IndexRange" type="xs:string" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="AttributeOperand" type="tns:AttributeOperand" />

  <xs:complexType name="SimpleAttributeOperand">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:FilterOperand">
        <xs:sequence>
          <xs:element name="TypeDefinitionId" type="ua:NodeId" minOccurs="0" nillable="true" />
          <xs:element name="BrowsePath" type="ua:ListOfQualifiedName" minOccurs="0" nillable="true" />
          <xs:element name="AttributeId" type="xs:unsignedInt" minOccurs="0" />
          <xs:element name="IndexRange" type="xs:string" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="SimpleAttributeOperand" type="tns:SimpleAttributeOperand" />

  <xs:complexType name="ListOfSimpleAttributeOperand">
    <xs:sequence>
      <xs:element name="SimpleAttributeOperand" type="tns:SimpleAttributeOperand" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfSimpleAttributeOperand" type="tns:ListOfSimpleAttributeOperand" nillable="true"></xs:element>

  <xs:complexType name="ContentFilterElementResult">
    <xs:sequence>
      <xs:element name="StatusCode" type="ua:StatusCode" minOccurs="0" />
      <xs:element name="OperandStatusCodes" type="ua:ListOfStatusCode" minOccurs="0" nillable="true" />
      <xs:element name="OperandDiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ContentFilterElementResult" type="tns:ContentFilterElementResult" />

  <xs:complexType name="ListOfContentFilterElementResult">
    <xs:sequence>
      <xs:element name="ContentFilterElementResult" type="tns:ContentFilterElementResult" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfContentFilterElementResult" type="tns:ListOfContentFilterElementResult" nillable="true"></xs:element>

  <xs:complexType name="ContentFilterResult">
    <xs:sequence>
      <xs:element name="ElementResults" type="tns:ListOfContentFilterElementResult" minOccurs="0" nillable="true" />
      <xs:element name="ElementDiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ContentFilterResult" type="tns:ContentFilterResult" />

  <xs:complexType name="ParsingResult">
    <xs:sequence>
      <xs:element name="StatusCode" type="ua:StatusCode" minOccurs="0" />
      <xs:element name="DataStatusCodes" type="ua:ListOfStatusCode" minOccurs="0" nillable="true" />
      <xs:element name="DataDiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ParsingResult" type="tns:ParsingResult" />

  <xs:complexType name="ListOfParsingResult">
    <xs:sequence>
      <xs:element name="ParsingResult" type="tns:ParsingResult" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfParsingResult" type="tns:ListOfParsingResult" nillable="true"></xs:element>

  <xs:complexType name="QueryFirstRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="View" type="tns:ViewDescription" minOccurs="0" nillable="true" />
      <xs:element name="NodeTypes" type="tns:ListOfNodeTypeDescription" minOccurs="0" nillable="true" />
      <xs:element name="Filter" type="tns:ContentFilter" minOccurs="0" nillable="true" />
      <xs:element name="MaxDataSetsToReturn" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="MaxReferencesToReturn" type="xs:unsignedInt" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="QueryFirstRequest" type="tns:QueryFirstRequest" />

  <xs:complexType name="QueryFirstResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="QueryDataSets" type="tns:ListOfQueryDataSet" minOccurs="0" nillable="true" />
      <xs:element name="ContinuationPoint" type="xs:base64Binary" minOccurs="0" nillable="true" />
      <xs:element name="ParsingResults" type="tns:ListOfParsingResult" minOccurs="0" nillable="true" />
      <xs:element name="DiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
      <xs:element name="FilterResult" type="tns:ContentFilterResult" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="QueryFirstResponse" type="tns:QueryFirstResponse" />

  <xs:complexType name="QueryNextRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="ReleaseContinuationPoint" type="xs:boolean" minOccurs="0" />
      <xs:element name="ContinuationPoint" type="xs:base64Binary" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="QueryNextRequest" type="tns:QueryNextRequest" />

  <xs:complexType name="QueryNextResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="QueryDataSets" type="tns:ListOfQueryDataSet" minOccurs="0" nillable="true" />
      <xs:element name="RevisedContinuationPoint" type="xs:base64Binary" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="QueryNextResponse" type="tns:QueryNextResponse" />

  <xs:simpleType  name="TimestampsToReturn">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Source_0" />
      <xs:enumeration value="Server_1" />
      <xs:enumeration value="Both_2" />
      <xs:enumeration value="Neither_3" />
      <xs:enumeration value="Invalid_4" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="TimestampsToReturn" type="tns:TimestampsToReturn" />

  <xs:complexType name="ReadValueId">
    <xs:sequence>
      <xs:element name="NodeId" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="AttributeId" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="IndexRange" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="DataEncoding" type="ua:QualifiedName" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ReadValueId" type="tns:ReadValueId" />

  <xs:complexType name="ListOfReadValueId">
    <xs:sequence>
      <xs:element name="ReadValueId" type="tns:ReadValueId" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfReadValueId" type="tns:ListOfReadValueId" nillable="true"></xs:element>

  <xs:complexType name="ReadRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="MaxAge" type="xs:double" minOccurs="0" />
      <xs:element name="TimestampsToReturn" type="tns:TimestampsToReturn" minOccurs="0" />
      <xs:element name="NodesToRead" type="tns:ListOfReadValueId" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ReadRequest" type="tns:ReadRequest" />

  <xs:complexType name="ReadResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="Results" type="ua:ListOfDataValue" minOccurs="0" nillable="true" />
      <xs:element name="DiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ReadResponse" type="tns:ReadResponse" />

  <xs:complexType name="HistoryReadValueId">
    <xs:sequence>
      <xs:element name="NodeId" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="IndexRange" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="DataEncoding" type="ua:QualifiedName" minOccurs="0" nillable="true" />
      <xs:element name="ContinuationPoint" type="xs:base64Binary" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="HistoryReadValueId" type="tns:HistoryReadValueId" />

  <xs:complexType name="ListOfHistoryReadValueId">
    <xs:sequence>
      <xs:element name="HistoryReadValueId" type="tns:HistoryReadValueId" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfHistoryReadValueId" type="tns:ListOfHistoryReadValueId" nillable="true"></xs:element>

  <xs:complexType name="HistoryReadResult">
    <xs:sequence>
      <xs:element name="StatusCode" type="ua:StatusCode" minOccurs="0" />
      <xs:element name="ContinuationPoint" type="xs:base64Binary" minOccurs="0" nillable="true" />
      <xs:element name="HistoryData" type="ua:ExtensionObject" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="HistoryReadResult" type="tns:HistoryReadResult" />

  <xs:complexType name="ListOfHistoryReadResult">
    <xs:sequence>
      <xs:element name="HistoryReadResult" type="tns:HistoryReadResult" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfHistoryReadResult" type="tns:ListOfHistoryReadResult" nillable="true"></xs:element>

  <xs:complexType name="HistoryReadDetails">
    <xs:sequence>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="HistoryReadDetails" type="tns:HistoryReadDetails" />

  <xs:complexType name="ReadEventDetails">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:HistoryReadDetails">
        <xs:sequence>
          <xs:element name="NumValuesPerNode" type="xs:unsignedInt" minOccurs="0" />
          <xs:element name="StartTime" type="xs:dateTime" minOccurs="0" />
          <xs:element name="EndTime" type="xs:dateTime" minOccurs="0" />
          <xs:element name="Filter" type="tns:EventFilter" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="ReadEventDetails" type="tns:ReadEventDetails" />

  <xs:complexType name="ReadRawModifiedDetails">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:HistoryReadDetails">
        <xs:sequence>
          <xs:element name="IsReadModified" type="xs:boolean" minOccurs="0" />
          <xs:element name="StartTime" type="xs:dateTime" minOccurs="0" />
          <xs:element name="EndTime" type="xs:dateTime" minOccurs="0" />
          <xs:element name="NumValuesPerNode" type="xs:unsignedInt" minOccurs="0" />
          <xs:element name="ReturnBounds" type="xs:boolean" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="ReadRawModifiedDetails" type="tns:ReadRawModifiedDetails" />

  <xs:complexType name="ReadProcessedDetails">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:HistoryReadDetails">
        <xs:sequence>
          <xs:element name="StartTime" type="xs:dateTime" minOccurs="0" />
          <xs:element name="EndTime" type="xs:dateTime" minOccurs="0" />
          <xs:element name="ProcessingInterval" type="xs:double" minOccurs="0" />
          <xs:element name="AggregateType" type="ua:ListOfNodeId" minOccurs="0" nillable="true" />
          <xs:element name="AggregateConfiguration" type="tns:AggregateConfiguration" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="ReadProcessedDetails" type="tns:ReadProcessedDetails" />

  <xs:complexType name="ReadAtTimeDetails">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:HistoryReadDetails">
        <xs:sequence>
          <xs:element name="ReqTimes" type="ua:ListOfDateTime" minOccurs="0" nillable="true" />
          <xs:element name="UseSimpleBounds" type="xs:boolean" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="ReadAtTimeDetails" type="tns:ReadAtTimeDetails" />

  <xs:complexType name="ReadAnnotationDataDetails">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:HistoryReadDetails">
        <xs:sequence>
          <xs:element name="ReqTimes" type="ua:ListOfDateTime" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="ReadAnnotationDataDetails" type="tns:ReadAnnotationDataDetails" />

  <xs:complexType name="HistoryData">
    <xs:sequence>
      <xs:element name="DataValues" type="ua:ListOfDataValue" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="HistoryData" type="tns:HistoryData" />

  <xs:complexType name="ModificationInfo">
    <xs:sequence>
      <xs:element name="ModificationTime" type="xs:dateTime" minOccurs="0" />
      <xs:element name="UpdateType" type="tns:HistoryUpdateType" minOccurs="0" />
      <xs:element name="UserName" type="xs:string" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ModificationInfo" type="tns:ModificationInfo" />

  <xs:complexType name="ListOfModificationInfo">
    <xs:sequence>
      <xs:element name="ModificationInfo" type="tns:ModificationInfo" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfModificationInfo" type="tns:ListOfModificationInfo" nillable="true"></xs:element>

  <xs:complexType name="HistoryModifiedData">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:HistoryData">
        <xs:sequence>
          <xs:element name="ModificationInfos" type="tns:ListOfModificationInfo" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="HistoryModifiedData" type="tns:HistoryModifiedData" />

  <xs:complexType name="HistoryEvent">
    <xs:sequence>
      <xs:element name="Events" type="tns:ListOfHistoryEventFieldList" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="HistoryEvent" type="tns:HistoryEvent" />

  <xs:complexType name="HistoryReadRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="HistoryReadDetails" type="ua:ExtensionObject" minOccurs="0" nillable="true" />
      <xs:element name="TimestampsToReturn" type="tns:TimestampsToReturn" minOccurs="0" />
      <xs:element name="ReleaseContinuationPoints" type="xs:boolean" minOccurs="0" />
      <xs:element name="NodesToRead" type="tns:ListOfHistoryReadValueId" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="HistoryReadRequest" type="tns:HistoryReadRequest" />

  <xs:complexType name="HistoryReadResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="Results" type="tns:ListOfHistoryReadResult" minOccurs="0" nillable="true" />
      <xs:element name="DiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="HistoryReadResponse" type="tns:HistoryReadResponse" />

  <xs:complexType name="WriteValue">
    <xs:sequence>
      <xs:element name="NodeId" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="AttributeId" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="IndexRange" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="Value" type="ua:DataValue" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="WriteValue" type="tns:WriteValue" />

  <xs:complexType name="ListOfWriteValue">
    <xs:sequence>
      <xs:element name="WriteValue" type="tns:WriteValue" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfWriteValue" type="tns:ListOfWriteValue" nillable="true"></xs:element>

  <xs:complexType name="WriteRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="NodesToWrite" type="tns:ListOfWriteValue" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="WriteRequest" type="tns:WriteRequest" />

  <xs:complexType name="WriteResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="Results" type="ua:ListOfStatusCode" minOccurs="0" nillable="true" />
      <xs:element name="DiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="WriteResponse" type="tns:WriteResponse" />

  <xs:complexType name="HistoryUpdateDetails">
    <xs:sequence>
      <xs:element name="NodeId" type="ua:NodeId" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="HistoryUpdateDetails" type="tns:HistoryUpdateDetails" />

  <xs:simpleType  name="HistoryUpdateType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Insert_1" />
      <xs:enumeration value="Replace_2" />
      <xs:enumeration value="Update_3" />
      <xs:enumeration value="Delete_4" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="HistoryUpdateType" type="tns:HistoryUpdateType" />

  <xs:simpleType  name="PerformUpdateType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Insert_1" />
      <xs:enumeration value="Replace_2" />
      <xs:enumeration value="Update_3" />
      <xs:enumeration value="Remove_4" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="PerformUpdateType" type="tns:PerformUpdateType" />

  <xs:complexType name="UpdateDataDetails">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:HistoryUpdateDetails">
        <xs:sequence>
          <xs:element name="PerformInsertReplace" type="tns:PerformUpdateType" minOccurs="0" />
          <xs:element name="UpdateValues" type="ua:ListOfDataValue" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="UpdateDataDetails" type="tns:UpdateDataDetails" />

  <xs:complexType name="UpdateStructureDataDetails">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:HistoryUpdateDetails">
        <xs:sequence>
          <xs:element name="PerformInsertReplace" type="tns:PerformUpdateType" minOccurs="0" />
          <xs:element name="UpdateValues" type="ua:ListOfDataValue" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="UpdateStructureDataDetails" type="tns:UpdateStructureDataDetails" />

  <xs:complexType name="UpdateEventDetails">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:HistoryUpdateDetails">
        <xs:sequence>
          <xs:element name="PerformInsertReplace" type="tns:PerformUpdateType" minOccurs="0" />
          <xs:element name="Filter" type="tns:EventFilter" minOccurs="0" nillable="true" />
          <xs:element name="EventData" type="tns:ListOfHistoryEventFieldList" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="UpdateEventDetails" type="tns:UpdateEventDetails" />

  <xs:complexType name="DeleteRawModifiedDetails">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:HistoryUpdateDetails">
        <xs:sequence>
          <xs:element name="IsDeleteModified" type="xs:boolean" minOccurs="0" />
          <xs:element name="StartTime" type="xs:dateTime" minOccurs="0" />
          <xs:element name="EndTime" type="xs:dateTime" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="DeleteRawModifiedDetails" type="tns:DeleteRawModifiedDetails" />

  <xs:complexType name="DeleteAtTimeDetails">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:HistoryUpdateDetails">
        <xs:sequence>
          <xs:element name="ReqTimes" type="ua:ListOfDateTime" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="DeleteAtTimeDetails" type="tns:DeleteAtTimeDetails" />

  <xs:complexType name="DeleteEventDetails">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:HistoryUpdateDetails">
        <xs:sequence>
          <xs:element name="EventIds" type="ua:ListOfByteString" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="DeleteEventDetails" type="tns:DeleteEventDetails" />

  <xs:complexType name="HistoryUpdateResult">
    <xs:sequence>
      <xs:element name="StatusCode" type="ua:StatusCode" minOccurs="0" />
      <xs:element name="OperationResults" type="ua:ListOfStatusCode" minOccurs="0" nillable="true" />
      <xs:element name="DiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="HistoryUpdateResult" type="tns:HistoryUpdateResult" />

  <xs:complexType name="ListOfHistoryUpdateResult">
    <xs:sequence>
      <xs:element name="HistoryUpdateResult" type="tns:HistoryUpdateResult" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfHistoryUpdateResult" type="tns:ListOfHistoryUpdateResult" nillable="true"></xs:element>

  <xs:complexType name="HistoryUpdateRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="HistoryUpdateDetails" type="ua:ListOfExtensionObject" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="HistoryUpdateRequest" type="tns:HistoryUpdateRequest" />

  <xs:complexType name="HistoryUpdateResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="Results" type="tns:ListOfHistoryUpdateResult" minOccurs="0" nillable="true" />
      <xs:element name="DiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="HistoryUpdateResponse" type="tns:HistoryUpdateResponse" />

  <xs:complexType name="CallMethodRequest">
    <xs:sequence>
      <xs:element name="ObjectId" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="MethodId" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="InputArguments" type="ua:ListOfVariant" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CallMethodRequest" type="tns:CallMethodRequest" />

  <xs:complexType name="ListOfCallMethodRequest">
    <xs:sequence>
      <xs:element name="CallMethodRequest" type="tns:CallMethodRequest" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfCallMethodRequest" type="tns:ListOfCallMethodRequest" nillable="true"></xs:element>

  <xs:complexType name="CallMethodResult">
    <xs:sequence>
      <xs:element name="StatusCode" type="ua:StatusCode" minOccurs="0" />
      <xs:element name="InputArgumentResults" type="ua:ListOfStatusCode" minOccurs="0" nillable="true" />
      <xs:element name="InputArgumentDiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
      <xs:element name="OutputArguments" type="ua:ListOfVariant" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CallMethodResult" type="tns:CallMethodResult" />

  <xs:complexType name="ListOfCallMethodResult">
    <xs:sequence>
      <xs:element name="CallMethodResult" type="tns:CallMethodResult" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfCallMethodResult" type="tns:ListOfCallMethodResult" nillable="true"></xs:element>

  <xs:complexType name="CallRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="MethodsToCall" type="tns:ListOfCallMethodRequest" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CallRequest" type="tns:CallRequest" />

  <xs:complexType name="CallResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="Results" type="tns:ListOfCallMethodResult" minOccurs="0" nillable="true" />
      <xs:element name="DiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CallResponse" type="tns:CallResponse" />

  <xs:simpleType  name="MonitoringMode">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Disabled_0" />
      <xs:enumeration value="Sampling_1" />
      <xs:enumeration value="Reporting_2" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="MonitoringMode" type="tns:MonitoringMode" />

  <xs:simpleType  name="DataChangeTrigger">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Status_0" />
      <xs:enumeration value="StatusValue_1" />
      <xs:enumeration value="StatusValueTimestamp_2" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="DataChangeTrigger" type="tns:DataChangeTrigger" />

  <xs:simpleType  name="DeadbandType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None_0" />
      <xs:enumeration value="Absolute_1" />
      <xs:enumeration value="Percent_2" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="DeadbandType" type="tns:DeadbandType" />

  <xs:complexType name="MonitoringFilter">
    <xs:sequence>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="MonitoringFilter" type="tns:MonitoringFilter" />

  <xs:complexType name="DataChangeFilter">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:MonitoringFilter">
        <xs:sequence>
          <xs:element name="Trigger" type="tns:DataChangeTrigger" minOccurs="0" />
          <xs:element name="DeadbandType" type="xs:unsignedInt" minOccurs="0" />
          <xs:element name="DeadbandValue" type="xs:double" minOccurs="0" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="DataChangeFilter" type="tns:DataChangeFilter" />

  <xs:complexType name="EventFilter">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:MonitoringFilter">
        <xs:sequence>
          <xs:element name="SelectClauses" type="tns:ListOfSimpleAttributeOperand" minOccurs="0" nillable="true" />
          <xs:element name="WhereClause" type="tns:ContentFilter" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EventFilter" type="tns:EventFilter" />

  <xs:complexType name="AggregateConfiguration">
    <xs:sequence>
      <xs:element name="UseServerCapabilitiesDefaults" type="xs:boolean" minOccurs="0" />
      <xs:element name="TreatUncertainAsBad" type="xs:boolean" minOccurs="0" />
      <xs:element name="PercentDataBad" type="xs:unsignedByte" minOccurs="0" />
      <xs:element name="PercentDataGood" type="xs:unsignedByte" minOccurs="0" />
      <xs:element name="UseSlopedExtrapolation" type="xs:boolean" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="AggregateConfiguration" type="tns:AggregateConfiguration" />

  <xs:complexType name="AggregateFilter">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:MonitoringFilter">
        <xs:sequence>
          <xs:element name="StartTime" type="xs:dateTime" minOccurs="0" />
          <xs:element name="AggregateType" type="ua:NodeId" minOccurs="0" nillable="true" />
          <xs:element name="ProcessingInterval" type="xs:double" minOccurs="0" />
          <xs:element name="AggregateConfiguration" type="tns:AggregateConfiguration" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="AggregateFilter" type="tns:AggregateFilter" />

  <xs:complexType name="MonitoringFilterResult">
    <xs:sequence>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="MonitoringFilterResult" type="tns:MonitoringFilterResult" />

  <xs:complexType name="EventFilterResult">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:MonitoringFilterResult">
        <xs:sequence>
          <xs:element name="SelectClauseResults" type="ua:ListOfStatusCode" minOccurs="0" nillable="true" />
          <xs:element name="SelectClauseDiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
          <xs:element name="WhereClauseResult" type="tns:ContentFilterResult" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EventFilterResult" type="tns:EventFilterResult" />

  <xs:complexType name="AggregateFilterResult">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:MonitoringFilterResult">
        <xs:sequence>
          <xs:element name="RevisedStartTime" type="xs:dateTime" minOccurs="0" />
          <xs:element name="RevisedProcessingInterval" type="xs:double" minOccurs="0" />
          <xs:element name="RevisedAggregateConfiguration" type="tns:AggregateConfiguration" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="AggregateFilterResult" type="tns:AggregateFilterResult" />

  <xs:complexType name="MonitoringParameters">
    <xs:sequence>
      <xs:element name="ClientHandle" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="SamplingInterval" type="xs:double" minOccurs="0" />
      <xs:element name="Filter" type="ua:ExtensionObject" minOccurs="0" nillable="true" />
      <xs:element name="QueueSize" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="DiscardOldest" type="xs:boolean" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="MonitoringParameters" type="tns:MonitoringParameters" />

  <xs:complexType name="MonitoredItemCreateRequest">
    <xs:sequence>
      <xs:element name="ItemToMonitor" type="tns:ReadValueId" minOccurs="0" nillable="true" />
      <xs:element name="MonitoringMode" type="tns:MonitoringMode" minOccurs="0" />
      <xs:element name="RequestedParameters" type="tns:MonitoringParameters" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="MonitoredItemCreateRequest" type="tns:MonitoredItemCreateRequest" />

  <xs:complexType name="ListOfMonitoredItemCreateRequest">
    <xs:sequence>
      <xs:element name="MonitoredItemCreateRequest" type="tns:MonitoredItemCreateRequest" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfMonitoredItemCreateRequest" type="tns:ListOfMonitoredItemCreateRequest" nillable="true"></xs:element>

  <xs:complexType name="MonitoredItemCreateResult">
    <xs:sequence>
      <xs:element name="StatusCode" type="ua:StatusCode" minOccurs="0" />
      <xs:element name="MonitoredItemId" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="RevisedSamplingInterval" type="xs:double" minOccurs="0" />
      <xs:element name="RevisedQueueSize" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="FilterResult" type="ua:ExtensionObject" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="MonitoredItemCreateResult" type="tns:MonitoredItemCreateResult" />

  <xs:complexType name="ListOfMonitoredItemCreateResult">
    <xs:sequence>
      <xs:element name="MonitoredItemCreateResult" type="tns:MonitoredItemCreateResult" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfMonitoredItemCreateResult" type="tns:ListOfMonitoredItemCreateResult" nillable="true"></xs:element>

  <xs:complexType name="CreateMonitoredItemsRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="SubscriptionId" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="TimestampsToReturn" type="tns:TimestampsToReturn" minOccurs="0" />
      <xs:element name="ItemsToCreate" type="tns:ListOfMonitoredItemCreateRequest" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CreateMonitoredItemsRequest" type="tns:CreateMonitoredItemsRequest" />

  <xs:complexType name="CreateMonitoredItemsResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="Results" type="tns:ListOfMonitoredItemCreateResult" minOccurs="0" nillable="true" />
      <xs:element name="DiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CreateMonitoredItemsResponse" type="tns:CreateMonitoredItemsResponse" />

  <xs:complexType name="MonitoredItemModifyRequest">
    <xs:sequence>
      <xs:element name="MonitoredItemId" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="RequestedParameters" type="tns:MonitoringParameters" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="MonitoredItemModifyRequest" type="tns:MonitoredItemModifyRequest" />

  <xs:complexType name="ListOfMonitoredItemModifyRequest">
    <xs:sequence>
      <xs:element name="MonitoredItemModifyRequest" type="tns:MonitoredItemModifyRequest" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfMonitoredItemModifyRequest" type="tns:ListOfMonitoredItemModifyRequest" nillable="true"></xs:element>

  <xs:complexType name="MonitoredItemModifyResult">
    <xs:sequence>
      <xs:element name="StatusCode" type="ua:StatusCode" minOccurs="0" />
      <xs:element name="RevisedSamplingInterval" type="xs:double" minOccurs="0" />
      <xs:element name="RevisedQueueSize" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="FilterResult" type="ua:ExtensionObject" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="MonitoredItemModifyResult" type="tns:MonitoredItemModifyResult" />

  <xs:complexType name="ListOfMonitoredItemModifyResult">
    <xs:sequence>
      <xs:element name="MonitoredItemModifyResult" type="tns:MonitoredItemModifyResult" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfMonitoredItemModifyResult" type="tns:ListOfMonitoredItemModifyResult" nillable="true"></xs:element>

  <xs:complexType name="ModifyMonitoredItemsRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="SubscriptionId" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="TimestampsToReturn" type="tns:TimestampsToReturn" minOccurs="0" />
      <xs:element name="ItemsToModify" type="tns:ListOfMonitoredItemModifyRequest" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ModifyMonitoredItemsRequest" type="tns:ModifyMonitoredItemsRequest" />

  <xs:complexType name="ModifyMonitoredItemsResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="Results" type="tns:ListOfMonitoredItemModifyResult" minOccurs="0" nillable="true" />
      <xs:element name="DiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ModifyMonitoredItemsResponse" type="tns:ModifyMonitoredItemsResponse" />

  <xs:complexType name="SetMonitoringModeRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="SubscriptionId" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="MonitoringMode" type="tns:MonitoringMode" minOccurs="0" />
      <xs:element name="MonitoredItemIds" type="ua:ListOfUInt32" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SetMonitoringModeRequest" type="tns:SetMonitoringModeRequest" />

  <xs:complexType name="SetMonitoringModeResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="Results" type="ua:ListOfStatusCode" minOccurs="0" nillable="true" />
      <xs:element name="DiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SetMonitoringModeResponse" type="tns:SetMonitoringModeResponse" />

  <xs:complexType name="SetTriggeringRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="SubscriptionId" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="TriggeringItemId" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="LinksToAdd" type="ua:ListOfUInt32" minOccurs="0" nillable="true" />
      <xs:element name="LinksToRemove" type="ua:ListOfUInt32" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SetTriggeringRequest" type="tns:SetTriggeringRequest" />

  <xs:complexType name="SetTriggeringResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="AddResults" type="ua:ListOfStatusCode" minOccurs="0" nillable="true" />
      <xs:element name="AddDiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
      <xs:element name="RemoveResults" type="ua:ListOfStatusCode" minOccurs="0" nillable="true" />
      <xs:element name="RemoveDiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SetTriggeringResponse" type="tns:SetTriggeringResponse" />

  <xs:complexType name="DeleteMonitoredItemsRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="SubscriptionId" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="MonitoredItemIds" type="ua:ListOfUInt32" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="DeleteMonitoredItemsRequest" type="tns:DeleteMonitoredItemsRequest" />

  <xs:complexType name="DeleteMonitoredItemsResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="Results" type="ua:ListOfStatusCode" minOccurs="0" nillable="true" />
      <xs:element name="DiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="DeleteMonitoredItemsResponse" type="tns:DeleteMonitoredItemsResponse" />

  <xs:complexType name="CreateSubscriptionRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="RequestedPublishingInterval" type="xs:double" minOccurs="0" />
      <xs:element name="RequestedLifetimeCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="RequestedMaxKeepAliveCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="MaxNotificationsPerPublish" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="PublishingEnabled" type="xs:boolean" minOccurs="0" />
      <xs:element name="Priority" type="xs:unsignedByte" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CreateSubscriptionRequest" type="tns:CreateSubscriptionRequest" />

  <xs:complexType name="CreateSubscriptionResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="SubscriptionId" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="RevisedPublishingInterval" type="xs:double" minOccurs="0" />
      <xs:element name="RevisedLifetimeCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="RevisedMaxKeepAliveCount" type="xs:unsignedInt" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CreateSubscriptionResponse" type="tns:CreateSubscriptionResponse" />

  <xs:complexType name="ModifySubscriptionRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="SubscriptionId" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="RequestedPublishingInterval" type="xs:double" minOccurs="0" />
      <xs:element name="RequestedLifetimeCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="RequestedMaxKeepAliveCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="MaxNotificationsPerPublish" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="Priority" type="xs:unsignedByte" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ModifySubscriptionRequest" type="tns:ModifySubscriptionRequest" />

  <xs:complexType name="ModifySubscriptionResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="RevisedPublishingInterval" type="xs:double" minOccurs="0" />
      <xs:element name="RevisedLifetimeCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="RevisedMaxKeepAliveCount" type="xs:unsignedInt" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ModifySubscriptionResponse" type="tns:ModifySubscriptionResponse" />

  <xs:complexType name="SetPublishingModeRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="PublishingEnabled" type="xs:boolean" minOccurs="0" />
      <xs:element name="SubscriptionIds" type="ua:ListOfUInt32" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SetPublishingModeRequest" type="tns:SetPublishingModeRequest" />

  <xs:complexType name="SetPublishingModeResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="Results" type="ua:ListOfStatusCode" minOccurs="0" nillable="true" />
      <xs:element name="DiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SetPublishingModeResponse" type="tns:SetPublishingModeResponse" />

  <xs:complexType name="NotificationMessage">
    <xs:sequence>
      <xs:element name="SequenceNumber" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="PublishTime" type="xs:dateTime" minOccurs="0" />
      <xs:element name="NotificationData" type="ua:ListOfExtensionObject" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="NotificationMessage" type="tns:NotificationMessage" />

  <xs:complexType name="NotificationData">
    <xs:sequence>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="NotificationData" type="tns:NotificationData" />

  <xs:complexType name="DataChangeNotification">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:NotificationData">
        <xs:sequence>
          <xs:element name="MonitoredItems" type="tns:ListOfMonitoredItemNotification" minOccurs="0" nillable="true" />
          <xs:element name="DiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="DataChangeNotification" type="tns:DataChangeNotification" />

  <xs:complexType name="MonitoredItemNotification">
    <xs:sequence>
      <xs:element name="ClientHandle" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="Value" type="ua:DataValue" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="MonitoredItemNotification" type="tns:MonitoredItemNotification" />

  <xs:complexType name="ListOfMonitoredItemNotification">
    <xs:sequence>
      <xs:element name="MonitoredItemNotification" type="tns:MonitoredItemNotification" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfMonitoredItemNotification" type="tns:ListOfMonitoredItemNotification" nillable="true"></xs:element>

  <xs:complexType name="EventNotificationList">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:NotificationData">
        <xs:sequence>
          <xs:element name="Events" type="tns:ListOfEventFieldList" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EventNotificationList" type="tns:EventNotificationList" />

  <xs:complexType name="EventFieldList">
    <xs:sequence>
      <xs:element name="ClientHandle" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="EventFields" type="ua:ListOfVariant" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="EventFieldList" type="tns:EventFieldList" />

  <xs:complexType name="ListOfEventFieldList">
    <xs:sequence>
      <xs:element name="EventFieldList" type="tns:EventFieldList" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfEventFieldList" type="tns:ListOfEventFieldList" nillable="true"></xs:element>

  <xs:complexType name="HistoryEventFieldList">
    <xs:sequence>
      <xs:element name="EventFields" type="ua:ListOfVariant" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="HistoryEventFieldList" type="tns:HistoryEventFieldList" />

  <xs:complexType name="ListOfHistoryEventFieldList">
    <xs:sequence>
      <xs:element name="HistoryEventFieldList" type="tns:HistoryEventFieldList" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfHistoryEventFieldList" type="tns:ListOfHistoryEventFieldList" nillable="true"></xs:element>

  <xs:complexType name="StatusChangeNotification">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:NotificationData">
        <xs:sequence>
          <xs:element name="Status" type="ua:StatusCode" minOccurs="0" />
          <xs:element name="DiagnosticInfo" type="ua:DiagnosticInfo" minOccurs="0" nillable="true" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="StatusChangeNotification" type="tns:StatusChangeNotification" />

  <xs:complexType name="SubscriptionAcknowledgement">
    <xs:sequence>
      <xs:element name="SubscriptionId" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="SequenceNumber" type="xs:unsignedInt" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SubscriptionAcknowledgement" type="tns:SubscriptionAcknowledgement" />

  <xs:complexType name="ListOfSubscriptionAcknowledgement">
    <xs:sequence>
      <xs:element name="SubscriptionAcknowledgement" type="tns:SubscriptionAcknowledgement" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfSubscriptionAcknowledgement" type="tns:ListOfSubscriptionAcknowledgement" nillable="true"></xs:element>

  <xs:complexType name="PublishRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="SubscriptionAcknowledgements" type="tns:ListOfSubscriptionAcknowledgement" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="PublishRequest" type="tns:PublishRequest" />

  <xs:complexType name="PublishResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="SubscriptionId" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="AvailableSequenceNumbers" type="ua:ListOfUInt32" minOccurs="0" nillable="true" />
      <xs:element name="MoreNotifications" type="xs:boolean" minOccurs="0" />
      <xs:element name="NotificationMessage" type="tns:NotificationMessage" minOccurs="0" nillable="true" />
      <xs:element name="Results" type="ua:ListOfStatusCode" minOccurs="0" nillable="true" />
      <xs:element name="DiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="PublishResponse" type="tns:PublishResponse" />

  <xs:complexType name="RepublishRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="SubscriptionId" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="RetransmitSequenceNumber" type="xs:unsignedInt" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RepublishRequest" type="tns:RepublishRequest" />

  <xs:complexType name="RepublishResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="NotificationMessage" type="tns:NotificationMessage" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RepublishResponse" type="tns:RepublishResponse" />

  <xs:complexType name="TransferResult">
    <xs:sequence>
      <xs:element name="StatusCode" type="ua:StatusCode" minOccurs="0" />
      <xs:element name="AvailableSequenceNumbers" type="ua:ListOfUInt32" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="TransferResult" type="tns:TransferResult" />

  <xs:complexType name="ListOfTransferResult">
    <xs:sequence>
      <xs:element name="TransferResult" type="tns:TransferResult" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfTransferResult" type="tns:ListOfTransferResult" nillable="true"></xs:element>

  <xs:complexType name="TransferSubscriptionsRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="SubscriptionIds" type="ua:ListOfUInt32" minOccurs="0" nillable="true" />
      <xs:element name="SendInitialValues" type="xs:boolean" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="TransferSubscriptionsRequest" type="tns:TransferSubscriptionsRequest" />

  <xs:complexType name="TransferSubscriptionsResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="Results" type="tns:ListOfTransferResult" minOccurs="0" nillable="true" />
      <xs:element name="DiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="TransferSubscriptionsResponse" type="tns:TransferSubscriptionsResponse" />

  <xs:complexType name="DeleteSubscriptionsRequest">
    <xs:sequence>
      <xs:element name="RequestHeader" type="tns:RequestHeader" minOccurs="0" nillable="true" />
      <xs:element name="SubscriptionIds" type="ua:ListOfUInt32" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="DeleteSubscriptionsRequest" type="tns:DeleteSubscriptionsRequest" />

  <xs:complexType name="DeleteSubscriptionsResponse">
    <xs:sequence>
      <xs:element name="ResponseHeader" type="tns:ResponseHeader" minOccurs="0" nillable="true" />
      <xs:element name="Results" type="ua:ListOfStatusCode" minOccurs="0" nillable="true" />
      <xs:element name="DiagnosticInfos" type="ua:ListOfDiagnosticInfo" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="DeleteSubscriptionsResponse" type="tns:DeleteSubscriptionsResponse" />

  <xs:complexType name="BuildInfo">
    <xs:sequence>
      <xs:element name="ProductUri" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="ManufacturerName" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="ProductName" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="SoftwareVersion" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="BuildNumber" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="BuildDate" type="xs:dateTime" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="BuildInfo" type="tns:BuildInfo" />

  <xs:simpleType  name="RedundancySupport">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None_0" />
      <xs:enumeration value="Cold_1" />
      <xs:enumeration value="Warm_2" />
      <xs:enumeration value="Hot_3" />
      <xs:enumeration value="Transparent_4" />
      <xs:enumeration value="HotAndMirrored_5" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="RedundancySupport" type="tns:RedundancySupport" />

  <xs:simpleType  name="ServerState">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Running_0" />
      <xs:enumeration value="Failed_1" />
      <xs:enumeration value="NoConfiguration_2" />
      <xs:enumeration value="Suspended_3" />
      <xs:enumeration value="Shutdown_4" />
      <xs:enumeration value="Test_5" />
      <xs:enumeration value="CommunicationFault_6" />
      <xs:enumeration value="Unknown_7" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="ServerState" type="tns:ServerState" />

  <xs:complexType name="RedundantServerDataType">
    <xs:sequence>
      <xs:element name="ServerId" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="ServiceLevel" type="xs:unsignedByte" minOccurs="0" />
      <xs:element name="ServerState" type="tns:ServerState" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RedundantServerDataType" type="tns:RedundantServerDataType" />

  <xs:complexType name="ListOfRedundantServerDataType">
    <xs:sequence>
      <xs:element name="RedundantServerDataType" type="tns:RedundantServerDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfRedundantServerDataType" type="tns:ListOfRedundantServerDataType" nillable="true"></xs:element>

  <xs:complexType name="EndpointUrlListDataType">
    <xs:sequence>
      <xs:element name="EndpointUrlList" type="ua:ListOfString" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="EndpointUrlListDataType" type="tns:EndpointUrlListDataType" />

  <xs:complexType name="ListOfEndpointUrlListDataType">
    <xs:sequence>
      <xs:element name="EndpointUrlListDataType" type="tns:EndpointUrlListDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfEndpointUrlListDataType" type="tns:ListOfEndpointUrlListDataType" nillable="true"></xs:element>

  <xs:complexType name="NetworkGroupDataType">
    <xs:sequence>
      <xs:element name="ServerUri" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="NetworkPaths" type="tns:ListOfEndpointUrlListDataType" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="NetworkGroupDataType" type="tns:NetworkGroupDataType" />

  <xs:complexType name="ListOfNetworkGroupDataType">
    <xs:sequence>
      <xs:element name="NetworkGroupDataType" type="tns:NetworkGroupDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfNetworkGroupDataType" type="tns:ListOfNetworkGroupDataType" nillable="true"></xs:element>

  <xs:complexType name="SamplingIntervalDiagnosticsDataType">
    <xs:sequence>
      <xs:element name="SamplingInterval" type="xs:double" minOccurs="0" />
      <xs:element name="MonitoredItemCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="MaxMonitoredItemCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="DisabledMonitoredItemCount" type="xs:unsignedInt" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SamplingIntervalDiagnosticsDataType" type="tns:SamplingIntervalDiagnosticsDataType" />

  <xs:complexType name="ListOfSamplingIntervalDiagnosticsDataType">
    <xs:sequence>
      <xs:element name="SamplingIntervalDiagnosticsDataType" type="tns:SamplingIntervalDiagnosticsDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfSamplingIntervalDiagnosticsDataType" type="tns:ListOfSamplingIntervalDiagnosticsDataType" nillable="true"></xs:element>

  <xs:complexType name="ServerDiagnosticsSummaryDataType">
    <xs:sequence>
      <xs:element name="ServerViewCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="CurrentSessionCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="CumulatedSessionCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="SecurityRejectedSessionCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="RejectedSessionCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="SessionTimeoutCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="SessionAbortCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="CurrentSubscriptionCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="CumulatedSubscriptionCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="PublishingIntervalCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="SecurityRejectedRequestsCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="RejectedRequestsCount" type="xs:unsignedInt" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ServerDiagnosticsSummaryDataType" type="tns:ServerDiagnosticsSummaryDataType" />

  <xs:complexType name="ServerStatusDataType">
    <xs:sequence>
      <xs:element name="StartTime" type="xs:dateTime" minOccurs="0" />
      <xs:element name="CurrentTime" type="xs:dateTime" minOccurs="0" />
      <xs:element name="State" type="tns:ServerState" minOccurs="0" />
      <xs:element name="BuildInfo" type="tns:BuildInfo" minOccurs="0" nillable="true" />
      <xs:element name="SecondsTillShutdown" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="ShutdownReason" type="ua:LocalizedText" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ServerStatusDataType" type="tns:ServerStatusDataType" />

  <xs:complexType name="SessionDiagnosticsDataType">
    <xs:sequence>
      <xs:element name="SessionId" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="SessionName" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="ClientDescription" type="tns:ApplicationDescription" minOccurs="0" nillable="true" />
      <xs:element name="ServerUri" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="EndpointUrl" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="LocaleIds" type="ua:ListOfString" minOccurs="0" nillable="true" />
      <xs:element name="ActualSessionTimeout" type="xs:double" minOccurs="0" />
      <xs:element name="MaxResponseMessageSize" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="ClientConnectionTime" type="xs:dateTime" minOccurs="0" />
      <xs:element name="ClientLastContactTime" type="xs:dateTime" minOccurs="0" />
      <xs:element name="CurrentSubscriptionsCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="CurrentMonitoredItemsCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="CurrentPublishRequestsInQueue" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="TotalRequestCount" type="tns:ServiceCounterDataType" minOccurs="0" nillable="true" />
      <xs:element name="UnauthorizedRequestCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="ReadCount" type="tns:ServiceCounterDataType" minOccurs="0" nillable="true" />
      <xs:element name="HistoryReadCount" type="tns:ServiceCounterDataType" minOccurs="0" nillable="true" />
      <xs:element name="WriteCount" type="tns:ServiceCounterDataType" minOccurs="0" nillable="true" />
      <xs:element name="HistoryUpdateCount" type="tns:ServiceCounterDataType" minOccurs="0" nillable="true" />
      <xs:element name="CallCount" type="tns:ServiceCounterDataType" minOccurs="0" nillable="true" />
      <xs:element name="CreateMonitoredItemsCount" type="tns:ServiceCounterDataType" minOccurs="0" nillable="true" />
      <xs:element name="ModifyMonitoredItemsCount" type="tns:ServiceCounterDataType" minOccurs="0" nillable="true" />
      <xs:element name="SetMonitoringModeCount" type="tns:ServiceCounterDataType" minOccurs="0" nillable="true" />
      <xs:element name="SetTriggeringCount" type="tns:ServiceCounterDataType" minOccurs="0" nillable="true" />
      <xs:element name="DeleteMonitoredItemsCount" type="tns:ServiceCounterDataType" minOccurs="0" nillable="true" />
      <xs:element name="CreateSubscriptionCount" type="tns:ServiceCounterDataType" minOccurs="0" nillable="true" />
      <xs:element name="ModifySubscriptionCount" type="tns:ServiceCounterDataType" minOccurs="0" nillable="true" />
      <xs:element name="SetPublishingModeCount" type="tns:ServiceCounterDataType" minOccurs="0" nillable="true" />
      <xs:element name="PublishCount" type="tns:ServiceCounterDataType" minOccurs="0" nillable="true" />
      <xs:element name="RepublishCount" type="tns:ServiceCounterDataType" minOccurs="0" nillable="true" />
      <xs:element name="TransferSubscriptionsCount" type="tns:ServiceCounterDataType" minOccurs="0" nillable="true" />
      <xs:element name="DeleteSubscriptionsCount" type="tns:ServiceCounterDataType" minOccurs="0" nillable="true" />
      <xs:element name="AddNodesCount" type="tns:ServiceCounterDataType" minOccurs="0" nillable="true" />
      <xs:element name="AddReferencesCount" type="tns:ServiceCounterDataType" minOccurs="0" nillable="true" />
      <xs:element name="DeleteNodesCount" type="tns:ServiceCounterDataType" minOccurs="0" nillable="true" />
      <xs:element name="DeleteReferencesCount" type="tns:ServiceCounterDataType" minOccurs="0" nillable="true" />
      <xs:element name="BrowseCount" type="tns:ServiceCounterDataType" minOccurs="0" nillable="true" />
      <xs:element name="BrowseNextCount" type="tns:ServiceCounterDataType" minOccurs="0" nillable="true" />
      <xs:element name="TranslateBrowsePathsToNodeIdsCount" type="tns:ServiceCounterDataType" minOccurs="0" nillable="true" />
      <xs:element name="QueryFirstCount" type="tns:ServiceCounterDataType" minOccurs="0" nillable="true" />
      <xs:element name="QueryNextCount" type="tns:ServiceCounterDataType" minOccurs="0" nillable="true" />
      <xs:element name="RegisterNodesCount" type="tns:ServiceCounterDataType" minOccurs="0" nillable="true" />
      <xs:element name="UnregisterNodesCount" type="tns:ServiceCounterDataType" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SessionDiagnosticsDataType" type="tns:SessionDiagnosticsDataType" />

  <xs:complexType name="ListOfSessionDiagnosticsDataType">
    <xs:sequence>
      <xs:element name="SessionDiagnosticsDataType" type="tns:SessionDiagnosticsDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfSessionDiagnosticsDataType" type="tns:ListOfSessionDiagnosticsDataType" nillable="true"></xs:element>

  <xs:complexType name="SessionSecurityDiagnosticsDataType">
    <xs:sequence>
      <xs:element name="SessionId" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="ClientUserIdOfSession" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="ClientUserIdHistory" type="ua:ListOfString" minOccurs="0" nillable="true" />
      <xs:element name="AuthenticationMechanism" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="Encoding" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="TransportProtocol" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="SecurityMode" type="tns:MessageSecurityMode" minOccurs="0" />
      <xs:element name="SecurityPolicyUri" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="ClientCertificate" type="xs:base64Binary" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SessionSecurityDiagnosticsDataType" type="tns:SessionSecurityDiagnosticsDataType" />

  <xs:complexType name="ListOfSessionSecurityDiagnosticsDataType">
    <xs:sequence>
      <xs:element name="SessionSecurityDiagnosticsDataType" type="tns:SessionSecurityDiagnosticsDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfSessionSecurityDiagnosticsDataType" type="tns:ListOfSessionSecurityDiagnosticsDataType" nillable="true"></xs:element>

  <xs:complexType name="ServiceCounterDataType">
    <xs:sequence>
      <xs:element name="TotalCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="ErrorCount" type="xs:unsignedInt" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ServiceCounterDataType" type="tns:ServiceCounterDataType" />

  <xs:complexType name="StatusResult">
    <xs:sequence>
      <xs:element name="StatusCode" type="ua:StatusCode" minOccurs="0" />
      <xs:element name="DiagnosticInfo" type="ua:DiagnosticInfo" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="StatusResult" type="tns:StatusResult" />

  <xs:complexType name="ListOfStatusResult">
    <xs:sequence>
      <xs:element name="StatusResult" type="tns:StatusResult" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfStatusResult" type="tns:ListOfStatusResult" nillable="true"></xs:element>

  <xs:complexType name="SubscriptionDiagnosticsDataType">
    <xs:sequence>
      <xs:element name="SessionId" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="SubscriptionId" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="Priority" type="xs:unsignedByte" minOccurs="0" />
      <xs:element name="PublishingInterval" type="xs:double" minOccurs="0" />
      <xs:element name="MaxKeepAliveCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="MaxLifetimeCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="MaxNotificationsPerPublish" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="PublishingEnabled" type="xs:boolean" minOccurs="0" />
      <xs:element name="ModifyCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="EnableCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="DisableCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="RepublishRequestCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="RepublishMessageRequestCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="RepublishMessageCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="TransferRequestCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="TransferredToAltClientCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="TransferredToSameClientCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="PublishRequestCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="DataChangeNotificationsCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="EventNotificationsCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="NotificationsCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="LatePublishRequestCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="CurrentKeepAliveCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="CurrentLifetimeCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="UnacknowledgedMessageCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="DiscardedMessageCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="MonitoredItemCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="DisabledMonitoredItemCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="MonitoringQueueOverflowCount" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="NextSequenceNumber" type="xs:unsignedInt" minOccurs="0" />
      <xs:element name="EventQueueOverFlowCount" type="xs:unsignedInt" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SubscriptionDiagnosticsDataType" type="tns:SubscriptionDiagnosticsDataType" />

  <xs:complexType name="ListOfSubscriptionDiagnosticsDataType">
    <xs:sequence>
      <xs:element name="SubscriptionDiagnosticsDataType" type="tns:SubscriptionDiagnosticsDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfSubscriptionDiagnosticsDataType" type="tns:ListOfSubscriptionDiagnosticsDataType" nillable="true"></xs:element>

  <xs:simpleType  name="ModelChangeStructureVerbMask">
    <xs:restriction base="xs:string">
      <xs:enumeration value="NodeAdded_1" />
      <xs:enumeration value="NodeDeleted_2" />
      <xs:enumeration value="ReferenceAdded_4" />
      <xs:enumeration value="ReferenceDeleted_8" />
      <xs:enumeration value="DataTypeChanged_16" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="ModelChangeStructureVerbMask" type="tns:ModelChangeStructureVerbMask" />

  <xs:complexType name="ModelChangeStructureDataType">
    <xs:sequence>
      <xs:element name="Affected" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="AffectedType" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="Verb" type="xs:unsignedByte" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ModelChangeStructureDataType" type="tns:ModelChangeStructureDataType" />

  <xs:complexType name="ListOfModelChangeStructureDataType">
    <xs:sequence>
      <xs:element name="ModelChangeStructureDataType" type="tns:ModelChangeStructureDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfModelChangeStructureDataType" type="tns:ListOfModelChangeStructureDataType" nillable="true"></xs:element>

  <xs:complexType name="SemanticChangeStructureDataType">
    <xs:sequence>
      <xs:element name="Affected" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="AffectedType" type="ua:NodeId" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SemanticChangeStructureDataType" type="tns:SemanticChangeStructureDataType" />

  <xs:complexType name="ListOfSemanticChangeStructureDataType">
    <xs:sequence>
      <xs:element name="SemanticChangeStructureDataType" type="tns:SemanticChangeStructureDataType" minOccurs="0" maxOccurs="unbounded" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ListOfSemanticChangeStructureDataType" type="tns:ListOfSemanticChangeStructureDataType" nillable="true"></xs:element>

  <xs:complexType name="Range">
    <xs:sequence>
      <xs:element name="Low" type="xs:double" minOccurs="0" />
      <xs:element name="High" type="xs:double" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Range" type="tns:Range" />

  <xs:complexType name="EUInformation">
    <xs:sequence>
      <xs:element name="NamespaceUri" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="UnitId" type="xs:int" minOccurs="0" />
      <xs:element name="DisplayName" type="ua:LocalizedText" minOccurs="0" nillable="true" />
      <xs:element name="Description" type="ua:LocalizedText" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="EUInformation" type="tns:EUInformation" />

  <xs:simpleType  name="AxisScaleEnumeration">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Linear_0" />
      <xs:enumeration value="Log_1" />
      <xs:enumeration value="Ln_2" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="AxisScaleEnumeration" type="tns:AxisScaleEnumeration" />

  <xs:complexType name="ComplexNumberType">
    <xs:sequence>
      <xs:element name="Real" type="xs:float" minOccurs="0" />
      <xs:element name="Imaginary" type="xs:float" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ComplexNumberType" type="tns:ComplexNumberType" />

  <xs:complexType name="DoubleComplexNumberType">
    <xs:sequence>
      <xs:element name="Real" type="xs:double" minOccurs="0" />
      <xs:element name="Imaginary" type="xs:double" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="DoubleComplexNumberType" type="tns:DoubleComplexNumberType" />

  <xs:complexType name="AxisInformation">
    <xs:sequence>
      <xs:element name="EngineeringUnits" type="tns:EUInformation" minOccurs="0" nillable="true" />
      <xs:element name="EURange" type="tns:Range" minOccurs="0" nillable="true" />
      <xs:element name="Title" type="ua:LocalizedText" minOccurs="0" nillable="true" />
      <xs:element name="AxisScaleType" type="tns:AxisScaleEnumeration" minOccurs="0" />
      <xs:element name="AxisSteps" type="ua:ListOfDouble" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="AxisInformation" type="tns:AxisInformation" />

  <xs:complexType name="XVType">
    <xs:sequence>
      <xs:element name="X" type="xs:double" minOccurs="0" />
      <xs:element name="Value" type="xs:float" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="XVType" type="tns:XVType" />

  <xs:complexType name="ProgramDiagnosticDataType">
    <xs:sequence>
      <xs:element name="CreateSessionId" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="CreateClientName" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="InvocationCreationTime" type="xs:dateTime" minOccurs="0" />
      <xs:element name="LastTransitionTime" type="xs:dateTime" minOccurs="0" />
      <xs:element name="LastMethodCall" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="LastMethodSessionId" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="LastMethodInputArguments" type="tns:ListOfArgument" minOccurs="0" nillable="true" />
      <xs:element name="LastMethodOutputArguments" type="tns:ListOfArgument" minOccurs="0" nillable="true" />
      <xs:element name="LastMethodCallTime" type="xs:dateTime" minOccurs="0" />
      <xs:element name="LastMethodReturnStatus" type="tns:StatusResult" minOccurs="0" nillable="true" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ProgramDiagnosticDataType" type="tns:ProgramDiagnosticDataType" />

  <xs:complexType name="ProgramDiagnostic2DataType">
    <xs:sequence>
      <xs:element name="CreateSessionId" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="CreateClientName" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="InvocationCreationTime" type="xs:dateTime" minOccurs="0" />
      <xs:element name="LastTransitionTime" type="xs:dateTime" minOccurs="0" />
      <xs:element name="LastMethodCall" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="LastMethodSessionId" type="ua:NodeId" minOccurs="0" nillable="true" />
      <xs:element name="LastMethodInputArguments" type="tns:ListOfArgument" minOccurs="0" nillable="true" />
      <xs:element name="LastMethodOutputArguments" type="tns:ListOfArgument" minOccurs="0" nillable="true" />
      <xs:element name="LastMethodInputValues" type="ua:ListOfVariant" minOccurs="0" nillable="true" />
      <xs:element name="LastMethodOutputValues" type="ua:ListOfVariant" minOccurs="0" nillable="true" />
      <xs:element name="LastMethodCallTime" type="xs:dateTime" minOccurs="0" />
      <xs:element name="LastMethodReturnStatus" type="ua:StatusCode" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ProgramDiagnostic2DataType" type="tns:ProgramDiagnostic2DataType" />

  <xs:complexType name="Annotation">
    <xs:sequence>
      <xs:element name="Message" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="UserName" type="xs:string" minOccurs="0" nillable="true" />
      <xs:element name="AnnotationTime" type="xs:dateTime" minOccurs="0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Annotation" type="tns:Annotation" />

  <xs:simpleType  name="ExceptionDeviationFormat">
    <xs:restriction base="xs:string">
      <xs:enumeration value="AbsoluteValue_0" />
      <xs:enumeration value="PercentOfValue_1" />
      <xs:enumeration value="PercentOfRange_2" />
      <xs:enumeration value="PercentOfEURange_3" />
      <xs:enumeration value="Unknown_4" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="ExceptionDeviationFormat" type="tns:ExceptionDeviationFormat" />

</xs:schema>
