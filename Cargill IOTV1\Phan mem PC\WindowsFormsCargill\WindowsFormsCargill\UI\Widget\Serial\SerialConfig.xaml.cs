using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace WindowsFormsCargill.UI.Widget.Serial
{
    /// <summary>
    /// Interaction logic for SerialConfig.xaml
    /// </summary>
    public partial class SerialConfig : UserControl
    {
        public EventHandler<object[]> SaveOnEvent;
        public EventHandler<SelectionChangedEventArgs> DeviceSelectEvent;

        public SerialUserControl serialControl = new SerialUserControl();

        List<string> ListHistoryTime = new List<string> { "Default", "7 days ago", "30 days ago", "7 weeks ago", "12 months ago", "range 2 days", "range 10 days" };
        public SerialConfig()
        {
            InitializeComponent();

            XamlSerialPlot.Children.Add(serialControl);

            // Update history time itemsouce
            XamlHistoryTime.ItemsSource = ListHistoryTime;
        }

        private void SavePara_btOnClick(object sender, System.Windows.RoutedEventArgs e)
        {
            if (SaveOnEvent != null)
            {
                object[] array = new object[6] { XamlDeviceName.Text, XamlParaName.Text, XamlRefreshTime.Text, XamlHistoryTime.Text , XamlMinDashboardValue.Text, XamlMaxDashboardValue.Text};

                SaveOnEvent.Invoke(this, array);
            }
        }

        private void DeviceSelectChanger(object sender, SelectionChangedEventArgs e)
        {
            DeviceSelectEvent.Invoke(this, e);
        }
    }
}
