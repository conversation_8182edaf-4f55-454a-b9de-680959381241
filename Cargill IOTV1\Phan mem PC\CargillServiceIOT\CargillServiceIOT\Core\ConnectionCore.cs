﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Security.Policy;
using System.Text;
using System.Threading.Tasks;
using AppServiceCargill.Connection;
using Newtonsoft.Json.Linq;
using static AppServiceCargill.Connection.Connection_PLC_OPC;
using WindowsFormsCargill.DB;

namespace AppServiceCargill.Core
{

    public class ConnectionCore
    {
        List<Gateway_Info> listGatewayInfo;

        public List<Connection_PLC_OPC> listConnectionOpc = new List<Connection_PLC_OPC>();

        public DeviceProcess deviceCore = new DeviceProcess();
        
        // global variable

        public static ApiDatabase apiDatabase = new ApiDatabase();
        public event EventHandler<PLCOPC_ReadData> DataTestPrint_Event;      

        public ConnectionCore()
        {

            deviceCore.Thread_Start();
            // get all OPC from sever 
            listGatewayInfo = apiDatabase.ReadAllGatewayInfo();
            // update config
            listConnectionOpc.Clear();

            for (int i=0;i< listGatewayInfo.Count; i++)
            {
                List<Opc_Msg_Config> listConfig = GetListConfigOffGateway(listGatewayInfo[i].ID);

                AddConnection(listGatewayInfo[i].ID, listGatewayInfo[i].IP, listConfig);
            }
            // update Ruler
        }

        // update data

        public List<Opc_Msg_Config> GetListConfigOffGateway(int gatewayId)
        {
            Random rnd = new Random();

            List<Opc_Msg_Config> listConfig = new List<Opc_Msg_Config>();

            List<MsgOpc_InfoDataDb> listOpcMsgInfo = apiDatabase.ReadOpcMsgInfo_ByGatewayId(gatewayId);
            for( int i=0; i< listOpcMsgInfo.Count; i++)
            {
                Opc_Msg_Config opcDevConf = new Opc_Msg_Config();
                opcDevConf.MsgId = listOpcMsgInfo[i].ID;
                opcDevConf.NodeAddress = listOpcMsgInfo[i].AddressNode;
                opcDevConf.CountTime = ((int)(rnd.Next(0, 50)));
                opcDevConf.TimeCycle = listOpcMsgInfo[i].TimeGet;
                List<MsgOpcVar_InfoDataDb> listVar = apiDatabase.ReadVarInfo_ByMsgId(listOpcMsgInfo[i].ID);
                for (int k = 0; k < listVar.Count; k++)
                {
                    Opc_Data_Config opcData = new Opc_Data_Config(listVar[k].VarName, listVar[k].VarType.ToString());
                    opcDevConf.ListDataConf.Add(opcData);
                }

                listConfig.Add(opcDevConf);
            }

            return listConfig;
        }
        // 

        public void AddConnection(int plcId, string url, List<Opc_Msg_Config> listConfg)
        {
            Connection_PLC_OPC connect = new Connection_PLC_OPC(plcId, url, false, null, null);

            connect.plcConnected_Event += plcConnected_EventHandle;
            connect.plcDisconnected_Event += plcDisconnected_EventHandle;
            connect.plcIntervalData_Event += plcIntervalData_EventHandle;

            connect.SetConfig(listConfg);

            connect.Thread_Start();

            listConnectionOpc.Add(connect);
        }

        public void DeleteConnection(int plcId)
        {
            for(int i=0;i< listConnectionOpc.Count; i++)
            {
                if (listConnectionOpc[i].Get_PLCID() == plcId)
                {
                    listConnectionOpc.Remove(listConnectionOpc[i]);
                }
            }
        }
        public void DeleteAllConnection()
        {
            listConnectionOpc.Clear();
        }

        // event handle
        private void plcConnected_EventHandle(object sender, int plcId)
        {
            Connection_PLC_OPC connection = (Connection_PLC_OPC)sender;

            apiDatabase.AddEvent(new EventData_InfoDb() { DevId = plcId, DeviceType = WindowsFormsCargill.Constant.Device_Type.GATEWAY, EventType = EVENT_TYPE.EVENT_INFOR, Content = $"Gateway {plcId}: CONNECTED", Time = DateTime.Now });

        }

        private void plcDisconnected_EventHandle(object sender, int plcId)
        {
            Connection_PLC_OPC connection = (Connection_PLC_OPC)sender;

            apiDatabase.AddEvent(new EventData_InfoDb() { DevId = plcId, DeviceType = WindowsFormsCargill.Constant.Device_Type.GATEWAY, EventType = EVENT_TYPE.EVENT_WARNING, Content = $"Gateway {plcId}: DISCONNECTED", Time = DateTime.Now });

        }

        private void plcIntervalData_EventHandle(object sender, PLCOPC_ReadData data)
        {
            DataTestPrint_Event.Invoke(this, data);

            deviceCore.plcIntervalData_EventHandle(data);
        }
    }
}
