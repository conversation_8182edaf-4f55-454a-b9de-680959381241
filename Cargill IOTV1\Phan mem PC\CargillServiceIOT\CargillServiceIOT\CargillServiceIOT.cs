﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using AppServiceCargill.Core;
using AppServiceCargill.Connection;
using static AppServiceCargill.Connection.Connection_PLC_OPC;
using System.Timers;

namespace CargillServiceIOT
{
    public partial class CargillServiceIOT: UserControl
    {
        static ConnectionCore connectionCore = new ConnectionCore();
        public ConnectionSocket connectionWebsocket = new ConnectionSocket(connectionCore);
        private bool State = true;

        public CargillServiceIOT()
        {
            InitializeComponent();

            connectionCore.DataTestPrint_Event += plcIntervalData_EventHandle;

            System.Timers.Timer timer = new System.Timers.Timer
            {
                Interval = 1000 // 1 seconds
            };
            timer.Elapsed += new ElapsedEventHandler(this.OnTimer);
            timer.Start();
        }

        private void plcIntervalData_EventHandle(object sender, PLCOPC_ReadData data)
        {

        }

        public void OnTimer(object sender, ElapsedEventArgs args)
        {
            // TODO: Insert monitoring activities here.
            //Utilities.WriteLogError("Monitoring the System" + (EventLogEntryType.Information).ToString() );


        }
        protected override void Dispose(bool disposing)
        {
            Console.WriteLine("Load Device ");
            for (int i = 0; i < connectionCore.listConnectionOpc.Count; i++)
            {
                connectionCore.listConnectionOpc[i].Thread_Stop();
            }
            connectionWebsocket.Stop();

            // Call the base class Dispose method
            base.Dispose(disposing);
        }
    }
}
