﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Collections.ObjectModel;
using WindowsFormsCargill.DB;
using WindowsFormsCargill.Constant;
using WindowsFormsCargill.UI.HardwareConfig;
using WindowsFormsCargill.UI.DashboardEnergy;
using WindowsFormsCargill.UI.EnegryReport;
using WindowsFormsCargill.UI.DashboardSteam;
using WindowsFormsCargill.UI.SteamReport;
using WindowsFormsCargill.UI.SolarEnegry;
using WindowsFormsCargill.UI.Event;
using WindowsFormsCargill.UI.Report;
using System.Threading;
using System.Windows.Media;
using System.Windows.Threading;
using Wpf<PERSON>agill;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.ToolTip;
using OpenTK.Graphics.ES11;


namespace WindowsFormsCargill
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : UserControl
    {
        public static ApiDatabase apiDatabase = new ApiDatabase();

        UI_HwConfigMainLayout uiHwConfigMain;

        public UI_EnegryMainLayout uiEnegryMain;

        UI_EnegryReportMainLayout uiEnegryReportMain;

        UI_SteamMainLayout uiSteamMain;

        UI_SteamReportMainLayout uiSteamReportMain;

        UI_SolarMainLayout uiSolarMain;

        UI_EventReport uiEventReport;

        UI_MainReportLayout uiReportPage;

        WindowSub subWindow;

        private readonly DispatcherTimer _dispatcherTimer;

        private Thread trSeviceConnect;

        public class SystermState
        {
            // respond getinfo
            public class GatewayState
            {
                public int gatewayId { get; set; }
                public CONNECTION_STATE ConnectionState { get; set; }
            }
            public class DeviceState
            {
                public int deviceId { get; set; }
                public CONNECTION_STATE ConnectionState { get; set; }

                public List<TelemetryData> listTelemetry { get; set; }
            }
            public List<GatewayState> listGatewayState;
            public List<DeviceState> listDevState;
            public SystermState()
            {
                listGatewayState = new List<GatewayState>();
                listDevState = new List<DeviceState>();
            }
        }

        public class SystemInfo
        {
            public class DeviceInfo
            {
                public int deviceId { get; set; }
                public string deviceName { get; set; }
                public List<string> listParaName;
                public DeviceInfo()
                {
                    listParaName = new List<string>();
                   
                }
            }

            public ObservableCollection<DeviceInfo> listDeviceEnegry { get; set; } 
            public ObservableCollection<DeviceInfo> listDeviceSteam { get; set; }
            public SystemInfo()
            {
                listDeviceEnegry = new ObservableCollection<DeviceInfo>();
                listDeviceSteam = new ObservableCollection<DeviceInfo>();
            }
        }

        public MainWindow()
        {
            InitializeComponent();
            InitSysData();
            //CeartPower();
            // UI
            uiHwConfigMain = new UI_HwConfigMainLayout();
            XamlConfig_Frame.Content = uiHwConfigMain;

            uiEnegryMain = new UI_EnegryMainLayout();
            XamlEnergry_Frame.Content = uiEnegryMain;

            uiEnegryReportMain = new UI_EnegryReportMainLayout();
            XamlEnergryReport_Frame.Content = uiEnegryReportMain;

            //uiSteamMain = new UI_SteamMainLayout();
            //XamlSteam_Frame.Content = uiSteamMain;

            //uiSteamReportMain = new UI_SteamReportMainLayout();
            //XamlSteamReport_Frame.Content = uiSteamReportMain;

            //uiSolarMain = new UI_SolarMainLayout();
            //XamlSolar_Frame.Content = uiSolarMain;

            uiEventReport = new UI_EventReport();
            XamlEventReport_Frame.Content = uiEventReport;

            uiReportPage = new UI_MainReportLayout();
            XamlReport_Frame.Content = uiReportPage;

            _dispatcherTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(5)
            };
            _dispatcherTimer.Tick += OnTimer;
            _dispatcherTimer.Start();

            clientSevice.getInforHandleEvent += respondGetInfo_EventHandle;



            trSeviceConnect = new Thread(SeviceConnect_Thread);
            trSeviceConnect.Start();

            //Closed += (s, e) =>
            //{
            //    Console.WriteLine("Load Device ");
            //    thSevicerunning = false;
            //    App.Current.Shutdown();
            //};
        }

        bool thSevicerunning = true;
        private void SeviceConnect_Thread()
        {
            while (thSevicerunning)
            {
                try
                {
                    if (clientSevice.busyFlag == false)
                    { 
                        ReadJsonFile readJsonFile = new ReadJsonFile();
                        string IPAddress = readJsonFile.ReadFromJsonIOTFile("IPAddress");
                        clientSevice.connect(IPAddress, 8182, new TimeSpan(0, 0, 5));
                    }
                    if (CountTimeSevice < 10)
                    {
                        CountTimeSevice++;
                    }
                    if (CountTimeSevice >= 3)
                    {
                        CountTimeSevice = 0;
                        this.Dispatcher.Invoke(() =>
                        {
                            XamlSeviceState.Background = new SolidColorBrush(Colors.Red);
                            for (int k = 0; k < systemState.listGatewayState.Count; k++)
                            {
                                systemState.listGatewayState[k].ConnectionState = CONNECTION_STATE.DISCONNECT;
                            }
                            for (int k = 0; k < systemState.listDevState.Count; k++)
                            {
                                systemState.listDevState[k].ConnectionState = CONNECTION_STATE.DISCONNECT;
                            }
                        });
                    }
                    //Console.WriteLine("===> Now :" + CurentTimeCount);
                    Thread.Sleep(5000);
                }
                catch (Exception)
                {

                }
            }
            clientSevice.Close();
        }

        #region Ui Hardware Config
        // Hardware config - List PLCs -------------------------
        // -------------------------------------

        #endregion
        public float Valuetest = 0;
        Random rnd = new Random();

        static SocketClientS clientSevice = new SocketClientS(); 

        public static SystermState systemState = new SystermState();// websocket state

        public static SystemInfo systemInfo = new SystemInfo();

        public void InitSysData()
        {
            List<Gateway_Info> listGatewayInfo = apiDatabase.ReadAllGatewayInfo();
            // update systemState
            systemState.listGatewayState.Clear();

            for (int i = 0; i < listGatewayInfo.Count; i++)
            {
                SystermState.GatewayState gwState = new SystermState.GatewayState();
                gwState.gatewayId = listGatewayInfo[i].ID;
                gwState.ConnectionState = CONNECTION_STATE.DISCONNECT;// default
                systemState.listGatewayState.Add(gwState);
            }

            List<Device_Info> listDev = apiDatabase.ReadAllDeviceInfo();
            for (int i = 0; i < listDev.Count; i++)
            {
                SystermState.DeviceState dvState = new SystermState.DeviceState();
                dvState.deviceId = listDev[i].ID;
                dvState.ConnectionState = CONNECTION_STATE.DISCONNECT;// default
                systemState.listDevState.Add(dvState);
            }
            // for systeminfo
            systemInfo.listDeviceEnegry.Clear();
            systemInfo.listDeviceSteam.Clear();
            SystemInfo.DeviceInfo device1 = new SystemInfo.DeviceInfo();
            device1.deviceName = "None";
            systemInfo.listDeviceEnegry.Add(device1);
            systemInfo.listDeviceSteam.Add(device1);
            for (int i = 0; i < listDev.Count; i++)
            {
                SystemInfo.DeviceInfo device = new SystemInfo.DeviceInfo();
                device.deviceId = listDev[i].ID;
                device.deviceName = listDev[i].Name;
                device.listParaName.Clear();
                List<MsgOpcVar_InfoDataDb> listVar = MainWindow.apiDatabase.ReadAllOpcVar_ByDeviceId(device.deviceId);
                for (int k = 0; k < listVar.Count; k++)
                {
                    device.listParaName.Add(listVar[k].VarName);
                }
                switch (listDev[i].Type)
                {
                    case Device_Application.ENEGRY:
                        
                        systemInfo.listDeviceEnegry.Add(device);
                        break;
                    case Device_Application.STEAM:
                        
                        systemInfo.listDeviceSteam.Add(device);
                        break;
                }

            }

        }

        private int CountTimeSevice = 0;

        private void OnTimer(object source, EventArgs e)
        {


            // check connection All
            //CeartPower();
        }


        private void respondGetInfo_EventHandle(object sender, MessageSocket.respondGetInfo respGetinfor)
        {

            for (int i = 0; i < respGetinfor.listGatewayState.Count; i++)
            {
                for (int k = 0; k < systemState.listGatewayState.Count; k++)
                {
                    if (respGetinfor.listGatewayState[i].gatewayId == systemState.listGatewayState[k].gatewayId)
                    {
                        systemState.listGatewayState[k].ConnectionState = respGetinfor.listGatewayState[i].ConnectionState;
                        break;
                    }
                }
            }
            for (int i = 0; i < respGetinfor.listDevState.Count; i++)
            {
                for (int k = 0; k < systemState.listDevState.Count; k++)
                {
                    if (respGetinfor.listDevState[i].deviceId == systemState.listDevState[k].deviceId)
                    {
                        systemState.listDevState[k].ConnectionState = respGetinfor.listDevState[i].ConnectionState;
                        systemState.listDevState[k].listTelemetry = respGetinfor.listDevState[i].listTelemetry;
                        break;
                    }
                }
            }
            CountTimeSevice = 0;
            this.Dispatcher.Invoke(() =>
            {
                XamlSeviceState.Background = new SolidColorBrush(Colors.Green);
            });

        }

        #region Test_data
        public void CeartValue()
        {
            List<TelemetryData> ListData = new List<TelemetryData>();

            DB.TelemetryData data = new TelemetryData();
            Valuetest = Valuetest + 10;
            if (Valuetest > 500) Valuetest = 0;
            data.Value = Valuetest;
            data.Time = DateTime.Now;
            data.deviceID = 1;
            data.Name = "VoltageA";
            ListData.Add(data);

            DB.TelemetryData data1 = new TelemetryData();
            Valuetest = Valuetest + 12;
            if (Valuetest > 500) Valuetest = 0;
            data1.Value = Valuetest;
            data1.Time = DateTime.Now;
            data1.deviceID = 1;
            data1.Name = "VoltageB";
            ListData.Add(data1);

            DB.TelemetryData data2 = new TelemetryData();
            Valuetest = Valuetest + 8;
            if (Valuetest > 500) Valuetest = 0;
            data2.Value = Valuetest;
            data2.Time = DateTime.Now;
            data2.deviceID = 1;
            data2.Name = "VoltageA-B";
            ListData.Add(data2);

            apiDatabase.AddListTelemetry(ListData);
        }

        public void CeartPower()
        {
            DateTime tempDay = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 0, 0, 0);
            tempDay = tempDay.AddDays(-10);
            List<TelemetryData> ListData = new List<TelemetryData>();
            DateTime caculator = tempDay.AddSeconds(0);
            int count = 0;
            for (int i = 0; caculator < DateTime.Now; i++)
            {
                caculator = tempDay.AddSeconds(i * 300);
                DB.TelemetryData data = new TelemetryData();
                Valuetest = Valuetest + ((uint)(rnd.Next(20, 100)));
                data.Value = /*((uint)(rnd.Next(1, 20)));//*/Valuetest;
                data.Time = caculator;
                data.deviceID = 2;
                data.Name = "EDel";
                ListData.Add(data);

                count++;
                if (count == 10)
                {
                    count = 0;
                    apiDatabase.AddListTelemetry(ListData);
                    ListData.Clear();
                }
            }

        }
        #endregion
    }
}
