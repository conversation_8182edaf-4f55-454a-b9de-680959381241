﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace WindowsFormsCargill.DB
{
    public enum VarOpc_Type
    {
        Float,
        Int,
        String,
        Double
    }
    public partial class MsgOpcVar_InfoDataDb // for Read struct only
    {
        public int ID { get; set; }
        public int msgOpcId { get; set; }
        public string VarName { get; set; }
        public VarOpc_Type VarType { get; set; }
        public MsgOpcVar_InfoDataDb()
        {
            VarName = "";
            VarType = VarOpc_Type.Float;
        }
    }
    public class OpcMsgVarDB
    {



        private string connectionString;
        private string TableString;

        public OpcMsgVarDB(string connection)
        {
            {// Creat variables
                connectionString = connection;//connectionString = "Data Source= EnegryDatabase.db; Version = 3; New = True; Compress = True";
                TableString = "OpcMsgVarTable";
                //CreateTable();
            }
        }

        public void CreateTable()
        {
            try
            {
                // Create a new database connection:
                using (SqlConnection sqlite_conn = new SqlConnection(connectionString))
                {
                    sqlite_conn.Open();
                    if (sqlite_conn.State == ConnectionState.Open)
                    {
                        SqlCommand sql_cmd;

                        sql_cmd = sqlite_conn.CreateCommand();

                        // Kiểm tra sự tồn tại của bảng trong SQL Server
                        sql_cmd.CommandText = "IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = @TableName) SELECT 0 ELSE SELECT 1";
                        sql_cmd.Parameters.AddWithValue("@TableName", TableString);
                        object obj = sql_cmd.ExecuteScalar();

                        // Nếu bảng chưa tồn tại, tạo bảng mới
                        if (Convert.ToInt32(obj) == 0)
                        {
                            sql_cmd.CommandText = "CREATE TABLE " + TableString + " (ID INT PRIMARY KEY, msgOpcId INT, VarName NVARCHAR(255), VarType INT)";
                            sql_cmd.ExecuteNonQuery();
                        }
                        sql_cmd.Dispose();
                    }
                    sqlite_conn.Close();
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
        }
        public List<MsgOpcVar_InfoDataDb> ReadAll()
        {
            List<MsgOpcVar_InfoDataDb> responseBuf = new List<MsgOpcVar_InfoDataDb>();
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        string commandStringBuf = "SELECT * FROM " + TableString;
                        using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                        {
                            using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                            {
                                // Call Read before accessing data.
                                while (readerBuf.Read())
                                {
                                    if (readerBuf.FieldCount == 4)
                                    {
                                        MsgOpcVar_InfoDataDb deviceInfoBuf = new MsgOpcVar_InfoDataDb();

                                        deviceInfoBuf.ID = readerBuf.GetInt32(0);
                                        deviceInfoBuf.msgOpcId = readerBuf.GetInt32(1);
                                        deviceInfoBuf.VarName = readerBuf["VarName"].ToString();
                                        deviceInfoBuf.VarType = (VarOpc_Type)readerBuf.GetInt32(3);

                                        responseBuf.Add(deviceInfoBuf);
                                    }
                                }
                                // Call Close when done reading.
                                readerBuf.Close();
                            }
                        }
                        sqlConnectionBuf.Close();
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
            return responseBuf;
        }
        public List<int> ReadAllID()
        {
            List<int> responseBuf = new List<int>();
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        try
                        {
                            // Câu lệnh SQL với TOP để lấy dữ liệu từ SQL Server
                            string commandStringBuf = "SELECT TOP 1000 ID FROM " + TableString;

                            using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                            {
                                using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                                {
                                    // Call Read before accessing data.
                                    while (readerBuf.Read())
                                    {
                                        if (readerBuf.FieldCount == 1)
                                        {
                                            responseBuf.Add(readerBuf.GetInt32(0));
                                        }
                                    }
                                    // Call Close when done reading.
                                    readerBuf.Close();
                                }
                            }
                        }
                        catch (Exception err)
                        {
                            //MessageBox.Show(err.ToString());
                        }
                        sqlConnectionBuf.Close();
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
            return responseBuf;
        }

        public List<MsgOpcVar_InfoDataDb> ReadOpcMsgInfo_ByMsgId(int msgId)
        {
            List<MsgOpcVar_InfoDataDb> responseBuf = new List<MsgOpcVar_InfoDataDb>();
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        try
                        {
                            string commandStringBuf = "SELECT * FROM " + TableString + " WHERE   msgOpcId  =" + msgId;
                            using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                            {
                                using (SqlDataReader readerBuf = commandBuf.ExecuteReader())
                                {
                                    while (readerBuf.Read())
                                    {
                                        if (readerBuf.FieldCount == 4)
                                        {
                                            MsgOpcVar_InfoDataDb deviceInfoBuf = new MsgOpcVar_InfoDataDb();

                                            deviceInfoBuf.ID = readerBuf.GetInt32(0);
                                            deviceInfoBuf.msgOpcId = readerBuf.GetInt32(1);
                                            deviceInfoBuf.VarName = readerBuf["VarName"].ToString();
                                            deviceInfoBuf.VarType = (VarOpc_Type)readerBuf.GetInt32(3);

                                            responseBuf.Add(deviceInfoBuf);
                                        }
                                    }
                                    // Call Close when done reading.
                                    readerBuf.Close();
                                }
                            }
                        }
                        catch (Exception err)
                        {
                            //MessageBox.Show(err.ToString());
                        }
                        sqlConnectionBuf.Close();
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
            return responseBuf;
        }
        public MsgOpcVar_InfoDataDb Add(MsgOpcVar_InfoDataDb newDeviceInfo)
        {
            MsgOpcVar_InfoDataDb responseBuf = null;
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        try
                        {
                            string commandStringBuf = "INSERT INTO " + TableString + " (msgOpcId, VarName, VarType) VALUES (@msgOpcId, @VarName, @VarType)";

                            using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                            {
                                commandBuf.Parameters.AddWithValue("@msgOpcId", newDeviceInfo.msgOpcId);
                                commandBuf.Parameters.AddWithValue("@VarName", newDeviceInfo.VarName);
                                commandBuf.Parameters.AddWithValue("@VarType", newDeviceInfo.VarType);

                                commandBuf.ExecuteNonQuery();
                                responseBuf = newDeviceInfo;
                            }
                        }
                        catch (Exception err)
                        {
                            //MessageBox.Show(err.ToString());
                        }
                        sqlConnectionBuf.Close();
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
            return responseBuf;
        }
        public int DeleteByOpcMsgID(int msgId)
        {
            int responseBuf = 0;
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        try
                        {
                            string commandStringBuf = "DELETE FROM " + TableString + " WHERE msgOpcId = @msgOpcId;";

                            using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                            {
                                commandBuf.Parameters.AddWithValue("@msgOpcId", msgId);

                                commandBuf.ExecuteNonQuery();
                                responseBuf = msgId;
                            }
                        }
                        catch (Exception err)
                        {
                            //MessageBox.Show(err.ToString());
                        }
                        sqlConnectionBuf.Close();
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
            return responseBuf;
        }
        public int DeleteByID(int id)
        {
            int responseBuf = 0;
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        try
                        {
                            string commandStringBuf = "DELETE FROM " + TableString + " WHERE ID = @ID;";

                            using (SqlCommand commandBuf = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                            {
                                commandBuf.Parameters.AddWithValue("@ID", id);

                                commandBuf.ExecuteNonQuery();
                                responseBuf = id;
                            }
                        }
                        catch (Exception err)
                        {
                            //MessageBox.Show(err.ToString());
                        }
                        sqlConnectionBuf.Close();
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
            return responseBuf;
        }
        //UPDATE table_name
        //SET column1 = value1, column2 = value2...., columnN = valueN
        //WHERE[condition];
        public void UpdateMsgInfo(MsgOpcVar_InfoDataDb msgInfo)
        {
            try
            {
                using (SqlConnection sqlConnectionBuf = new SqlConnection(connectionString))
                {
                    sqlConnectionBuf.Open();
                    if (sqlConnectionBuf.State == ConnectionState.Open)
                    {
                        try
                        {
                            string commandStringBuf = "UPDATE " + TableString + " SET gatewayID = @msgOpcId, VarName = @VarName, VarType = @VarType" +
                                                       " WHERE ID = @ID;";

                            using (SqlCommand command = new SqlCommand(commandStringBuf, sqlConnectionBuf))
                            {
                                command.Parameters.AddWithValue("@msgOpcId", msgInfo.msgOpcId);
                                command.Parameters.AddWithValue("@VarName", msgInfo.VarName);
                                command.Parameters.AddWithValue("@VarType", msgInfo.VarType);
                                command.Parameters.AddWithValue("@ID", msgInfo.ID);

                                command.ExecuteNonQuery();
                            }
                        }
                        catch (Exception err)
                        {
                            //MessageBox.Show(err.ToString());
                        }

                        sqlConnectionBuf.Close();
                    }
                }
            }
            catch (Exception err)
            {
                //MessageBox.Show(err.ToString());
            }
        }
    }
}
