﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Forms;
using System.Windows.Forms.Integration;
using WindowsFormsCargill.DB;
using WindowsFormsCargill.UI.Widget.Serial2;
using static WindowsFormsCargill.UI.Widget.Device.DeviceWidget;

namespace WindowsFormsCargill.UI.Widget.Device
{
    public partial class DeviceWindowWidgetForm : Form
    { 
        public ElementHost ele;
        public DeviceWindowWidget dialog = new DeviceWindowWidget();
        public DeviceWindowWidgetForm()
        {
            InitializeComponent();

            ele = new ElementHost
            {
                Dock = DockStyle.Fill,
                Child = dialog
            };
            panel1.Controls.Add(ele);
            
        }
        private void SaveBtPosHandle(object sender, object[] value)
        {
           

        }
        protected override void OnClosing(CancelEventArgs e)
        {
            this.Visible = false;
            e.Cancel = true;
        }
    }
}
