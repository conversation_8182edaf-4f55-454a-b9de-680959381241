//using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.DataVisualization.Charting;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Drawing;

namespace WindowsFormsCargill.UI.Widget.Serial
{
    /// <summary>
    /// Interaction logic for SerialUserControl.xaml
    /// </summary>
    public partial class SerialUserControl : UserControl
    {
        private ToolTip currentTooltip;

        // Main chart area for values
        ChartArea chartArea = new ChartArea
        {
            Name = "MainChartArea",
            AxisX =
                {
                    Title = "Time",
                    IntervalType = DateTimeIntervalType.Auto,
                    LabelStyle = { Format = "dd/MM/yyyy HH:mm" },
                    ScaleView = { Zoomable = true },
                    ScrollBar = { Enabled = true }
                },
            AxisY =
                {
                    Title = "Value",
                    ScaleView = { Zoomable = true },
                    ScrollBar = { Enabled = true }
                },
            AxisY2 =
                {
                    Title = "Percentage (%)",
                    ScaleView = { Zoomable = true },
                    ScrollBar = { Enabled = true },
                    Enabled = AxisEnabled.True
                }
        };

        // Series for device values (columns)
        Series series1 = new Series
        {
            Name = "DeviceValue",
            Color = System.Drawing.Color.SteelBlue,
            ChartType = SeriesChartType.Column,
            XValueType = ChartValueType.String,
            YValueType = ChartValueType.Double,
            MarkerStyle = MarkerStyle.Circle,
            MarkerSize = 4,
            MarkerColor = System.Drawing.Color.Blue,
            BorderColor = System.Drawing.Color.FromArgb(255,46,87,128),
            BorderWidth = 1,
            YAxisType = AxisType.Primary
        };

        // Series for percentage (line)
        Series series2 = new Series
        {
            Name = "Percentage",
            Color = System.Drawing.Color.Orange,
            ChartType = SeriesChartType.Line,
            XValueType = ChartValueType.String,
            YValueType = ChartValueType.Double,
            MarkerStyle = MarkerStyle.Circle,
            MarkerSize = 6,
            MarkerColor = System.Drawing.Color.DarkOrange,
            BorderColor = System.Drawing.Color.Orange,
            BorderWidth = 3,
            YAxisType = AxisType.Secondary
        };
        public SerialUserControl()
        {
            InitializeComponent();
            XamlPlot.Series.Clear();
            XamlPlot.ChartAreas.Clear();

            // Configure chart area
            chartArea.AxisX.MajorGrid.LineColor = System.Drawing.Color.FromArgb(255, 240, 240, 240);
            chartArea.AxisY.MajorGrid.LineColor = System.Drawing.Color.FromArgb(255, 240, 240, 240);
            chartArea.AxisY2.MajorGrid.LineColor = System.Drawing.Color.FromArgb(255, 250, 240, 240);
            chartArea.CursorX.IsUserEnabled = true;
            chartArea.CursorY.IsUserEnabled = true;
            chartArea.CursorX.IsUserSelectionEnabled = true;
            chartArea.CursorY.IsUserSelectionEnabled = true;

            XamlPlot.ChartAreas.Add(chartArea);
            XamlPlot.ChartAreas[0].AxisX.LabelStyle.Angle = 0;

            // Add both series
            XamlPlot.Series.Add(series1);
            XamlPlot.Series.Add(series2);

            // Add legend
            Legend legend = new Legend("MainLegend")
            {
                Docking = Docking.Top,
                Alignment = StringAlignment.Center
            };
            XamlPlot.Legends.Add(legend);

            XamlPlot.MouseDown += XamlPlot_MouseDown;
        }
        public void UpdatePlot(List<DB.TelemetryData> listPara, string TimeChoose, double MinYAxis, double MaxYAxis)
        {
            UpdatePlot(listPara, TimeChoose, MinYAxis, MaxYAxis, 0);
        }

        public void UpdatePlot(List<DB.TelemetryData> listPara, string TimeChoose, double MinYAxis, double MaxYAxis, double totalValue)
        {
            series1.Points.Clear();
            series2.Points.Clear();

            int NumberOfLabel = XamlPlot.Width / 90;
            int WidthLabel = listPara.Count / NumberOfLabel;
            XamlPlot.ChartAreas[0].AxisX.LabelStyle.Interval = WidthLabel;
            XamlPlot.ChartAreas[0].AxisX.MajorGrid.Interval = WidthLabel;
            XamlPlot.ChartAreas[0].AxisX.MajorTickMark.Interval = WidthLabel;
            if (listPara.Count > 0 && listPara.Count < 1000)
            {
                double MaxValue = 0;
                List<DateTime> dataX = new List<DateTime>();
                List<double> dataY = new List<double>();

                // Add each data point to both series
                if (TimeChoose == "12 months ago") // 12 months ago
                {
                    for (int i = 0; i < listPara.Count; i++)
                    {
                        // Add max min
                        if (i == 0) MaxValue = listPara[i].Value;
                        if (listPara[i].Value > MaxValue) MaxValue = listPara[i].Value;

                        string timeLabel = listPara[i].Time.ToString("dd/MM/yy");
                        double value = listPara[i].Value;
                        double percentage = totalValue > 0 ? (value / totalValue) * 100 : 0;

                        // Add to column series (values)
                        series1.Points.AddXY(timeLabel, value);

                        // Add to line series (percentage)
                        series2.Points.AddXY(timeLabel, percentage);
                    }
                }
                else if (TimeChoose == "7 weeks ago") // 7 weeks ago
                {
                    string ParaSupport;
                    for (int i = 0; i < listPara.Count; i++)
                    {
                        // Add max min
                        if (i == 0) MaxValue = listPara[i].Value;
                        if (listPara[i].Value > MaxValue) MaxValue = listPara[i].Value;

                        // Add to ParaSupport
                        DateTime StartWeek = listPara[i].Time;
                        DateTime EndWeek = StartWeek.AddDays(7);
                        ParaSupport = StartWeek.ToString("dd/MM/yy") + EndWeek.ToString("\ndd/MM/yy");

                        double value = listPara[i].Value;
                        double percentage = totalValue > 0 ? value / totalValue * 100 : 0;

                        // Add to column series (values)
                        series1.Points.AddXY(ParaSupport, value);

                        // Add to line series (percentage)
                        series2.Points.AddXY(ParaSupport, percentage);
                    }
                }
                else // 7 days ago, 30 days ago, other
                {
                    for (int i = 0; i < listPara.Count; i++)
                    {
                        // Add max min
                        if (i == 0) MaxValue = listPara[i].Value;
                        if (listPara[i].Value > MaxValue) MaxValue = listPara[i].Value;

                        string timeLabel = listPara[i].Time.ToString("dd/MM/yyyy\nHH:mm");
                        double value = listPara[i].Value;
                        double percentage = totalValue > 0 ? value / totalValue * 100 : 0;

                        // Add to column series (values)
                        series1.Points.AddXY(timeLabel, value);

                        // Add to line series (percentage)
                        series2.Points.AddXY(timeLabel, percentage);
                    }
                }
                // Configure Y-axis scaling for values
                if (MaxYAxis > MinYAxis)
                {
                    if (MaxYAxis > MaxValue) MaxYAxis = MaxValue;
                    chartArea.AxisY.ScaleView.Zoom(MinYAxis, MaxYAxis);
                }
                else
                {
                    chartArea.AxisY.ScaleView.ZoomReset();
                }

                // Configure Y2-axis for percentage (0-100%)
                chartArea.AxisY2.ScaleView.ZoomReset();
                chartArea.AxisY2.Minimum = 0;
                chartArea.AxisY2.Maximum = 100;

                // Update chart types based on time selection
                if (TimeChoose == "Default" || TimeChoose == "range 2 days" || TimeChoose == "range 10 days")
                {
                    series1.ChartType = SeriesChartType.Line;
                }
                else
                {
                    series1.ChartType = SeriesChartType.Column;
                }

                // Line series always remains as line
                series2.ChartType = SeriesChartType.Line;

                // Series are already added in constructor, just refresh the chart
                XamlPlot.Invalidate();
            }
        }

        public void ClearPlot()
        {
            series1.Points.Clear();
            series2.Points.Clear();
        }

        /// <summary>
        /// Set header text for the chart
        /// </summary>
        /// <param name="headerText">Header text to display</param>
        public void SetHeaderText(string headerText)
        {
            XamlHeader.Text = headerText;
        }

        private void XamlPlot_MouseDown(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            // Lấy vị trí chuột
            var pos = e.Location;

            // HitTest để kiểm tra tại vị trí chuột có phần tử nào không
            System.Windows.Forms.DataVisualization.Charting.HitTestResult result = XamlPlot.HitTest(pos.X, pos.Y, false);

            if (result.ChartElementType == ChartElementType.DataPoint)
            {
                // Lấy chỉ số của điểm dữ liệu
                int pointIndex = result.PointIndex;
                Series series = result.Series;
                DataPoint point = series.Points[pointIndex];

                // Lấy giá trị của điểm dữ liệu
                string categoryName = point.AxisLabel;
                double value = point.YValues[0];
                string seriesName = series.Name;

                // Đóng tooltip hiện tại nếu nó đang mở
                if (currentTooltip != null && currentTooltip.IsOpen)
                {
                    currentTooltip.IsOpen = false;
                }

                // Tạo ToolTip mới với thông tin phù hợp
                string tooltipContent;
                if (seriesName == "DeviceValue")
                {
                    tooltipContent = $"Date: {categoryName}\nValue: {value:F2}";
                }
                else if (seriesName == "Percentage")
                {
                    tooltipContent = $"Date: {categoryName}\nPercentage: {value:F2}%";
                }
                else
                {
                    tooltipContent = $"Date: {categoryName}\n{seriesName}: {value:F2}";
                }

                currentTooltip = new ToolTip
                {
                    Content = tooltipContent,
                    Placement = System.Windows.Controls.Primitives.PlacementMode.Mouse,
                    IsOpen = true
                };

                // Đặt thời gian tắt tooltip (tuỳ chọn)
                Task.Delay(5000).ContinueWith(_ => Dispatcher.Invoke(() => currentTooltip.IsOpen = false));
            }
        }
    }
}
