//using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.DataVisualization.Charting;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace WindowsFormsCargill.UI.Widget.Serial
{
    /// <summary>
    /// Interaction logic for SerialUserControl.xaml
    /// </summary>
    public partial class SerialUserControl : UserControl
    {
        private ToolTip currentTooltip;
        ChartArea chartArea = new ChartArea
        {

            Name = "MainChartArea",
            AxisX =
                {
                    Title = "Time",
                    IntervalType = DateTimeIntervalType.Auto,
                    LabelStyle = { Format = "dd/MM/yyyy HH:mm" },//{ Format = "MM/dd" },
                    ScaleView = { Zoomable = true },
                    ScrollBar = { Enabled = true }
                },
            AxisY =
                {   Title = "Value",
                    ScaleView = { Zoomable = true },
                    ScrollBar = { Enabled = true }
                }
            
        };

        Series series1 = new Series
        {
            Name = "DataSeries1",
            Color = System.Drawing.Color.SteelBlue,
            ChartType = SeriesChartType.Line,
            XValueType = ChartValueType.String,
            YValueType = ChartValueType.Double,
            MarkerStyle = MarkerStyle.Circle,
            MarkerSize = 4,                   
            MarkerColor = System.Drawing.Color.Blue,
            BorderColor = System.Drawing.Color.FromArgb(255,46,87,128),
            BorderWidth = 1,
        };
        public SerialUserControl()
        {
            InitializeComponent();
            XamlPlot.Series.Clear();
            XamlPlot.ChartAreas.Clear();
            chartArea.AxisX.MajorGrid.LineColor = System.Drawing.Color.FromArgb(255, 240, 240, 240);
            chartArea.AxisY.MajorGrid.LineColor = System.Drawing.Color.FromArgb(255, 240, 240, 240);
            chartArea.CursorX.IsUserEnabled = true;
            chartArea.CursorY.IsUserEnabled = true;
            chartArea.CursorX.IsUserSelectionEnabled = true;
            chartArea.CursorY.IsUserSelectionEnabled = true;
            XamlPlot.ChartAreas.Add(chartArea);
            XamlPlot.ChartAreas[0].AxisX.LabelStyle.Angle = 0;
            XamlPlot.Series.Add(series1);
            XamlPlot.MouseDown += XamlPlot_MouseDown;
        }
        public void UpdatePlot(List<DB.TelemetryData> listPara, string TimeChoose, double MinYAxis, double MaxYAxis)
        {
            series1.Points.Clear();
            int NumberOfLabel = XamlPlot.Width / 90;
            int WidthLabel = listPara.Count / NumberOfLabel;
            XamlPlot.ChartAreas[0].AxisX.LabelStyle.Interval = WidthLabel;
            XamlPlot.ChartAreas[0].AxisX.MajorGrid.Interval = WidthLabel;
            XamlPlot.ChartAreas[0].AxisX.MajorTickMark.Interval = WidthLabel;
            if (listPara.Count > 0 && listPara.Count < 1000)
            {
                double MaxValue = 0;
                List<DateTime> dataX = new List<DateTime>();
                List<double> dataY = new List<double>();

                // Add each data point to the series1           
                if (TimeChoose == "12 months ago") // 12 months ago
                {
                    for (int i = 0; i < listPara.Count; i++)
                    {
                        // Add max min
                        if (i == 0) MaxValue = listPara[i].Value;
                        if (listPara[i].Value > MaxValue) MaxValue = listPara[i].Value;

                        series1.Points.AddXY(listPara[i].Time.ToString("dd/MM/yy"), listPara[i].Value);
                    }
                }
                else if (TimeChoose == "7 weeks ago") // 7 weeks ago
                {
                    string ParaSupport;
                    for (int i = 0; i < listPara.Count; i++)
                    {
                        // Add max min
                        if (i == 0) MaxValue = listPara[i].Value;
                        if (listPara[i].Value > MaxValue) MaxValue = listPara[i].Value;

                        // Add to ParaSupport
                        DateTime StartWeek = listPara[i].Time;
                        DateTime EndWeek = StartWeek.AddDays(7);
                        ParaSupport = StartWeek.ToString("dd/MM/yy") + EndWeek.ToString("\ndd/MM/yy");
                        series1.Points.AddXY(ParaSupport, listPara[i].Value);
                    }
                }
                else // 7 days ago, 30 days ago, other
                {
                    for (int i = 0; i < listPara.Count; i++)
                    {
                        // Add max min
                        if (i == 0) MaxValue = listPara[i].Value;
                        if (listPara[i].Value > MaxValue) MaxValue = listPara[i].Value;

                        series1.Points.AddXY(listPara[i].Time.ToString("dd/MM/yyyy\nHH:mm"), listPara[i].Value);
                    }
                }
                if (MaxYAxis > MinYAxis)
                {
                    if (MaxYAxis > MaxValue) MaxYAxis = MaxValue;
                    chartArea.AxisY.ScaleView.Zoom(MinYAxis, MaxYAxis);
                }
                else
                {
                    chartArea.AxisY.ScaleView.ZoomReset();
                }

                // Update line, bar
                if (TimeChoose == "Default" || TimeChoose == "range 2 days" || TimeChoose == "range 10 days") series1.ChartType = SeriesChartType.Line;
                else series1.ChartType = SeriesChartType.Column;

                XamlPlot.Series.Add(series1);            
            }    
        }

        public void ClearPlot() { series1.Points.Clear(); }

        private void XamlPlot_MouseDown(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            // Lấy vị trí chuột
            var pos = e.Location;

            // HitTest để kiểm tra tại vị trí chuột có phần tử nào không
            System.Windows.Forms.DataVisualization.Charting.HitTestResult result = XamlPlot.HitTest(pos.X, pos.Y, false);

            if (result.ChartElementType == ChartElementType.DataPoint)
            {
                // Lấy chỉ số của điểm dữ liệu
                int pointIndex = result.PointIndex;
                Series series = result.Series;
                DataPoint point = series.Points[pointIndex];

                // Lấy giá trị của điểm dữ liệu
                string categoryName = point.AxisLabel;
                double value = point.YValues[0];

                // Đóng tooltip hiện tại nếu nó đang mở
                if (currentTooltip != null && currentTooltip.IsOpen)
                {
                    currentTooltip.IsOpen = false;
                }

                // Tạo ToolTip mới
                currentTooltip = new ToolTip
                {
                    Content = $"Date: {categoryName}, Value: {value}",
                    Placement = System.Windows.Controls.Primitives.PlacementMode.Mouse,
                    IsOpen = true
                };

                // Đặt thời gian tắt tooltip (tuỳ chọn)
                Task.Delay(5000).ContinueWith(_ => Dispatcher.Invoke(() => currentTooltip.IsOpen = false));
            }
        }
    }
}
